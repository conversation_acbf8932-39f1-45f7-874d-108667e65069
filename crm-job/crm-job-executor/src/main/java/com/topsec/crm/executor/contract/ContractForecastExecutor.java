package com.topsec.crm.executor.contract;

import com.topsec.crm.contract.api.RemoteContractForecastConfigService;
import com.topsec.crm.flow.api.RemoteContractForecastService;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.tbscommon.JsonObject;
import com.xxl.job.core.handler.annotation.XxlJob;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class ContractForecastExecutor {

    @Resource
    private RemoteContractForecastConfigService remoteContractForecastConfigService;

    @Resource
    private RemoteContractForecastService remoteContractForecastService;

    @XxlJob("createContractForecastPlan")
    public JsonObject<Boolean> createContractForecastPlan() throws Exception {
        log.info("begin--->CRM定时创建合同预测计划");
        remoteContractForecastConfigService.createContractForecastPlan();
        return new JsonObject(ResultEnum.SUCCESS.getResult(),ResultEnum.SUCCESS.getMessage());
    }

    @XxlJob("sendContractForecastFlow")
    public JsonObject<Boolean> sendContractForecastFlow() throws Exception {
        log.info("begin--->CRM定时发起合同预测流程");
        JsonObject<String> currentMonth = remoteContractForecastConfigService.getCurrentMonth();
        if(currentMonth.isSuccess() && StringUtils.isNotBlank(currentMonth.getObjEntity())){
            remoteContractForecastService.sendContractForecastFlow(currentMonth.getObjEntity());
        }
        return new JsonObject(ResultEnum.SUCCESS.getResult(),ResultEnum.SUCCESS.getMessage());
    }

    @XxlJob("autoFinishContractForecastFlow01")
    public JsonObject<Boolean> autoFinishContractForecastFlow01() throws Exception {
        log.info("begin--->CRM定时自动完成合同预测01步");
        JsonObject<List<String>> currentMonth = remoteContractForecastConfigService.getAutoFinishMonth01();
        if(currentMonth.isSuccess()){
            remoteContractForecastService.autoFinishContractForecastFlow01(currentMonth.getObjEntity());
        }
        return new JsonObject(ResultEnum.SUCCESS.getResult(),ResultEnum.SUCCESS.getMessage());
    }

    @XxlJob("autoFinishContractForecastFlow02")
    public JsonObject<Boolean> autoFinishContractForecastFlow02() throws Exception {
        log.info("begin--->CRM定时自动完成合同预测02步");
        JsonObject<List<String>> currentMonth = remoteContractForecastConfigService.getAutoFinishMonth02();
        if(currentMonth.isSuccess()){
            remoteContractForecastService.autoFinishContractForecastFlow02(currentMonth.getObjEntity());
        }
        return new JsonObject(ResultEnum.SUCCESS.getResult(),ResultEnum.SUCCESS.getMessage());
    }
}
