package com.topsec.crm.framework.security.aspect;

import com.topsec.crm.account.api.client.RemoteResourceService;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.stats.api.RemoteMocSourceDisableService;
import com.topsec.crm.stats.api.RemoteMocSourceService;
import com.topsec.crm.stats.api.entity.MocSourceAvaliablePropsVo;
import com.topsec.crm.stats.api.entity.MocSourceDisableVo;
import com.topsec.crm.stats.api.entity.MocSourceVo;
import com.topsec.crm.stats.api.entity.MocTemplateVO;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.tbscommon.JsonObject;
import jakarta.annotation.Resource;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.*;

/**
 * 数据源权限处理
 */
@Aspect
@Component
public class MocSourceDisableAuthorizeAspect {
    @Resource
    private RemoteMocSourceService remoteMocSourceService;
    @Resource
    private RemoteMocSourceDisableService remoteMocSourceDisableService;

    /**
     * 权限切入点
     */
    @Pointcut("@annotation(com.topsec.crm.framework.security.annotation.MocSourceDisableAuthorize)")
    public void doMocDataPreAuthorize() {

    }

    @Around("doMocDataPreAuthorize()")
    public Object doAfterReturning(ProceedingJoinPoint joinPoint) throws Throwable {
        // 1.获取方法参数列表，设置模板ID
        String sourceId = "";
        Object[] args = joinPoint.getArgs();
        MocSourceVo resource;
        // 1.1遍历参数并记录
        for (Object arg : args) {
            // 获取模板ID
            sourceId = ((MocSourceVo) arg).getId();
        }
        if (StringUtils.isBlank(sourceId)) {
            throw new IllegalArgumentException("Missing SourceId");
        }else{
            JsonObject<MocSourceVo> retObj = remoteMocSourceService.getSource(sourceId);
            if(!retObj.isSuccess() || retObj.getObjEntity() == null){
                throw new IllegalArgumentException("Source Not Found");
            }else {
                resource = retObj.getObjEntity();
            }
        }

        //1.2 判断是否有数据源的权限
        JsonObject<Boolean> booleanJsonObject = remoteMocSourceDisableService.checkHasPri(sourceId);
        if(!booleanJsonObject.isSuccess() || booleanJsonObject.getObjEntity() == false){
            throw new CrmException(ResultEnum.MOC_UNAUTH_ERROR);
        }

        // 2.执行目标方法并获取返回值
        Object result = joinPoint.proceed();
        // 2.1处理返回值-对返回值为null的不处理
        JsonObject jObj = (JsonObject) result;
        if(jObj.getObjEntity() == null){
            return new JsonObject<>(Collections.emptyMap());
        }

        // 3.列表对象提取对象属性列表
        Map<String, String> beanMap = new HashMap<String, String>();
        // 检查返回值是否为 List
        if (jObj.getObjEntity() instanceof List) {
            List<?> list = (List<?>) jObj.getObjEntity();
            //3.1对结果集为空的数组，不需要处理，直接返回
            if(CollectionUtils.isEmpty(list)){
                return new JsonObject<>(Collections.emptyMap());
            }
            // 提取每个元素的属性
            beanMap = (Map) list.get(0);
        }else{
            beanMap = (Map) jObj.getObjEntity();
        }

        // 4.查询当前人员对应的数据源所有属性对应的权限配置，找出需要隐藏的属性
        JsonObject<List<String>> disablePropsObj = remoteMocSourceDisableService.getDisableProps(sourceId);
        if(disablePropsObj.isSuccess()){
            List<String> disableProps = disablePropsObj.getObjEntity();
            List<String> avaliableProps = resource.getAvaliableProps().stream().map(MocSourceAvaliablePropsVo::getFieldName).toList();

            //4.1.首先判断字段有没有加权限-不包含不用管
            List<String> hiddenProps = new ArrayList<String>();
            for (String prop : beanMap.keySet()) {
                //4.1.1判断字段是否可用，如果找不到，说明不可以
                String isAva = avaliableProps.stream().filter(av -> av.equals(prop)).findFirst().orElse(null);
                if(isAva == null){
                    //如果找不到，说明不可以
                    hiddenProps.add(prop);
                }
                //4.1.2判断字段有没有加权限-不包含不用管
                String exist = disableProps.stream().filter(e -> e.equals(prop)).findFirst().orElse(null);
                if(exist != null){
                    //如果需要隐藏，则要移出这个属性
                    hiddenProps.add(prop);
                }
            }
            //4.2将没有权限点的数据设置为null
            nullifyBeanAndMap(jObj.getObjEntity(), beanMap, hiddenProps);
        }

        // 返回结果（可修改返回值）
        return result;
    }

    /**
     * 同步将原始对象和 Map 的指定属性设为 null
     * @param bean 原始对象
     * @param beanMap BeanUtils.describe() 生成的 Map
     * @param propertiesToNull 需要设为 null 的属性名数组
     */
    public static void nullifyBeanAndMap(Object bean, Map<String, String> beanMap, List<String> propertiesToNull) {
        if (bean == null || beanMap == null || propertiesToNull == null || CollectionUtils.isEmpty(propertiesToNull)) return;

        for (String prop : propertiesToNull) {
            try {
                // 1. 检查属性是否存在
                if (!beanMap.containsKey(prop)) {
                    System.err.println("属性不存在: " + prop);
                    continue;
                }

                if (bean instanceof List) {
                    for (Object o : (List) bean) {
                        // 3. 修改原始对象属性
//                        // 设置字段可访问（即使它是私有的）
//                        BeanUtils.setProperty(o, prop, null);

                        //移出改对象的属性
                        Map describe = (Map) o;
                        describe.remove(prop);
                    }
                }else{
//                    BeanUtils.setProperty(bean, prop, null);
                    //移出改对象的属性
                    Map describe = (Map) bean;
                    describe.remove(prop);
                }

                // 4. 同步修改 Map-将Map中的属性移除掉
                beanMap.remove(prop);

            } catch (Exception e) {
                System.err.println("操作属性失败: " + prop + ", 原因: " + e.getMessage());
            }
        }
    }
}
