package com.topsec.crm.framework.common.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 借款相关枚举
 */
public class LoanEnum{

    // 借款分类
    @Getter
    public enum LoanTypeEnum{

        MARKETING_BID_BOND("营销类",1,"投标保证金",1),
        MARKETING_LOG("营销类",1,"保函",2),
        MARKETING_PERFORMANCE_BOND("营销类",1,"履约保证金",3),
        MARKETING_TENDER_BOND("营销类",1,"标书费",4),
        MARKETING_BIND_SERVICE_FEE("营销类",1,"中标服务费",5),
        PURCHASE_LOAN("采购类",2,"采购借款",6),
        EXPENSE_COST("费用类",3,"费用",7);

        private final String parentDesc;
        private final Integer parentCode;
        private final String desc;
        private final Integer code;

        LoanTypeEnum(String parentDesc, Integer parentCode, String desc, Integer code) {
            this.parentDesc = parentDesc;
            this.parentCode = parentCode;
            this.desc = desc;
            this.code = code;
        }

        // 根据code获取parentCode
        public static Integer getParentCodeByCode(Integer code) {
            for (LoanTypeEnum value : LoanTypeEnum.values()) {
                if (value.getCode().equals(code)) {
                    return value.getParentCode();
                }
            }
            return null;
        }

        // 是否营销类
        public static Boolean isMarketing(Integer code) {
            for (LoanTypeEnum value : LoanTypeEnum.values()) {
                if (value.getCode().equals(code) && value.getParentCode().equals(1)) {
                    return true;
                }
            }
            return false;
        }

        // 是否采购类
        public static Boolean isPurchase(Integer code) {
            for (LoanTypeEnum value : LoanTypeEnum.values()) {
                if (value.getCode().equals(code) && value.getParentCode().equals(2)) {
                    return true;
                }
            }
            return false;
        }
    }

    // 付款方式
    @Getter
    public enum PaymentTypeEnum{

        CHEQUE("支票",1),
        CASH("现金",2),
        TELEGRAPH("电汇",3),
        OTHER("其他",4);

        private final String desc;
        private final Integer code;

        PaymentTypeEnum(String desc, Integer code) {
            this.desc = desc;
            this.code = code;
        }
    }

    // 付款对象类型
    @Getter
    public enum PayerTypeEnum{

        PERSONAL("个人",1),
        BIDDING_COMPANY("招标公司",2),
        USER("用户",3);

        private final String desc;
        private final Integer code;

        PayerTypeEnum(String desc, Integer code) {
            this.desc = desc;
            this.code = code;
        }
    }

    // 借款附件类型
    @Getter
    public enum LoanAttachmentTypeEnum{

        BID_DOCUMENT("招标文件关键页"),
        BID_NOTICE("中标通知书"),
        OTHER("其它"),

        WORK_REQUEST("工作请示报告"),
        MARKET_ACTIVITY("市场活动申请"),
        CHANNEL_MARKET_ACTIVITY("渠道市场活动申请"),

        LOAN_APPLY("借款单"),
        PAYMENT_PROOF("付款底单")
        ;

        private final String desc;

        LoanAttachmentTypeEnum(String desc) {
            this.desc = desc;
        }

        // 获取所有申请依据的desc
        public static List<String> getApplyBasisAttachments(){
            return List.of(BID_DOCUMENT.getDesc(),BID_NOTICE.getDesc(),OTHER.getDesc());
        }

        // loanProofAttachments
        public static List<String> getLoanProofAttachments(){
            return List.of(LOAN_APPLY.getDesc(),PAYMENT_PROOF.getDesc());
        }

        // otherAttachments
        public static List<String> getOtherAttachments(){
            return List.of(WORK_REQUEST.getDesc(),MARKET_ACTIVITY.getDesc(),CHANNEL_MARKET_ACTIVITY.getDesc());
        }

    }

    @Getter
    public enum LoanVerificationTypeEnum{

        BID_SERVICE_FEE("中标服务费",1),
        BID_MARGIN_RETURN("保证金退回",2),
        BID_TENDER_FEE("标书费报销",3),
        BID_CONTRACT_EXPIRE("保函到期",4),
        BID_RETURN("退回",5),
        BID_REIMBURSE("报销",6),
        BID_TRANSFER("转费用(借款转费用办结生成)",7);

        private final String desc;
        private final Integer code;

        LoanVerificationTypeEnum(String desc, Integer code) {
            this.desc = desc;
            this.code = code;
        }
    }

    @Getter
    public enum LoanTellerEnum{

        AN_HUI("安徽分公司","夏攀琴6444"),
        GAN_SU("甘肃分公司","华海霞0717"),
        GUANG_ZHOU("广州分公司","许静萍0043"),
        HAI_NAN("海南分公司","卢秋凤3014"),
        HANG_ZHOU("杭州分公司","方小君7219"),
        JI_LIN("吉林分公司","李茜2385"),
        NAN_JING("南京分公司","蒋凡14001"),
        SHEN_ZHEN("深圳分公司","江泳诗5470"),
        TIAN_JIN("天津分公司","林晓棠4208"),
        XI_AN("西安分公司","靳艳艳5682"),
        CHANG_SHA("长沙分公司","李岭7243"),
        NING_XIA("宁夏分公司","刘光静6099"),
        XIN_JIANG("新疆分公司","王玉晶2737"),
        JIANG_XI("江西分公司","雷娜2425"),
        QING_HUA("青海分公司","杜昊6159"),
        HE_BEI("河北分公司","李雅慧091"),
        FU_ZHOU("福州分公司","李锦爱5406"),
        GUANG_XI("广西分公司","吕晓芬3930"),
        GUI_ZHOU("贵州分公司","陈薇薇2364"),
        HA_ER_BIN("哈尔滨分公司","王红艳7493"),
        HU_BEI("湖北分公司","郭娟5556"),
        JI_NAN("济南分公司","胡欣2433"),
        NEI_MENG("内蒙古分公司","王亚红8328"),
        PU_DONG("上海浦东分公司","周子煜6812"),
        SHEN_YANG("沈阳分公司","刘洋9318"),
        YUN_NAN("云南分公司","王卫0348"),
        ZHENG_ZHOU("郑州分公司","段国利18437"),
        CHONG_QING("重庆分公司","张娅13773"),
        XI_BA_XI("西藏分公司","巫晓丹13637"),
        SHAN_XI("山西分公司","张娟0422"),
        SI_CHUAN("四川分公司","肖爽5745"),
        BEI_JING_ONE("北京分公司","王媛媛15419"),
        BEI_JING_TWO("北京分公司","陈露15815");


        private final String company;
        private final String teller;

        LoanTellerEnum(String company, String teller) {
            this.company = company;
            this.teller = teller;
        }

        // 获取所有转成Map<String,null> key:【company】+teller value:null
        public static Map<String,String> getAllTellerMap(){
            Map<String,String> map = new HashMap<>();
            for (LoanTellerEnum value : LoanTellerEnum.values()) {
                map.put("【"+value.getCompany()+"】"+value.getTeller(),value.teller);
            }
            return map;
        }
    }

}
