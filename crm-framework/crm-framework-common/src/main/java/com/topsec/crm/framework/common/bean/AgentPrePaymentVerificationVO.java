package com.topsec.crm.framework.common.bean;

import com.topsec.crm.framework.common.annotation.Excel;
import com.topsec.crm.framework.common.enums.AgentVerificationTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <p>
 * 渠道回款核销
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AgentPrePaymentVerificationVO {
    /**
     * id
     */
    @Schema(description = "核销明细id")
    private String id;

    /**
     * 渠道回款核销id
     */
    @Schema(description = "渠道回款核销id")
    private String verificationId;

    /**
     * 付款单位
     */
    @Schema(description = "付款单位")
    private String payerAgentId;

    /**
     * 付款单位名
     */
    @Schema(description = "付款单位名")
    private String payerAgentName;
    /**
     * 付款单位身份
     */
    @Schema(description = "付款单位身份")
    private Integer receivedLevel;

    /**
     * 收款单位
     */
    @Schema(description = "收款单位")
    private String receivedAgentId;

    /**
     * 收款单位名
     */
    @Schema(description = "收款单位名")
    private String receivedAgentName;

    /**
     * 剩余收款金额
     */
    @Schema(description = "剩余预付款")
    private BigDecimal remainingPrepaymentAmount;


    /**
     * 剩余可用预付款金额
     */
    @Schema(description = "剩余可用预付款金额")
    private BigDecimal prepaymentAmount;

    /**
     * 剩余可用预付款金额
     */
    @Schema(description = "冻结预付款")
    private BigDecimal freezeAmount;

    @Schema(description = "本次使用金额")
    private BigDecimal usedAmount;
    /**
     * 收款时间
     */
    @Schema(description = "收款时间")
    private LocalDate receivedDate;
    /**
     * 冻结预付款
     */
    @Schema(description = "冻结状态，0=冻结，1=未冻结")
    @Excel(name = "冻结状态", sort = 7,readConverterExp = "0=冻结,1=未冻结")
    private Integer status;

    /**
     * 核销分类 1-货款 2-预付款 3-其它
     */
    @Schema(description = "核销分类 1-货款 2-预付款 3-其它")
    private Integer verificationType;

    @Schema(description = "核销分类中文")
    private String verificationTypeCn;

    public String getVerificationTypeCn() {
        if (verificationType!=null){
            return AgentVerificationTypeEnum.getValue(verificationType);
        }
        return null;
    }
}
