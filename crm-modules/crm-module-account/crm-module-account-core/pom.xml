<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>crm-module-account</artifactId>
        <groupId>com.topsec</groupId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>crm-module-account-core</artifactId>
    <packaging>jar</packaging>
    <description>账号服务</description>
    <name>crm-module-account-core</name>

    <dependencies>
        <!-- SpringCloud Ailibaba Nacos -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>com.topsec</groupId>
            <artifactId>crm-framework-data</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>

        <!-- 添加 retry依赖 -->
        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
        </dependency>


        <!-- 跟着core走，不能放在crm-modules里，会导致xx-api也依赖security，而security依赖xx-api，导致循环依赖 -->
        <dependency>
            <groupId>com.topsec</groupId>
            <artifactId>crm-framework-security</artifactId>
        </dependency>

        <!-- 审计日志依赖 -->

        <dependency>
            <groupId>com.topsec.soft4j</groupId>
            <artifactId>audit-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.topsec</groupId>
            <artifactId>crm-module-account-api</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.topsec</groupId>
            <artifactId>crm-module-agent-api</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.topsec</groupId>
            <artifactId>crm-module-customer-api</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.topsec</groupId>
            <artifactId>crm-module-file-api</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>

        <!-- Redis依赖 -->
        <dependency>
            <groupId>com.topsec</groupId>
            <artifactId>crm-starter-redis</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>

        <!-- tbs服务 -->
        <dependency>
            <groupId>com.topsec</groupId>
            <artifactId>tbs-api</artifactId>
        </dependency>

        <!-- tos服务 -->
        <dependency>
            <groupId>com.topsec</groupId>
            <artifactId>tos-api</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <dependency>
            <groupId>com.topsec</groupId>
            <artifactId>crm-framework-swagger3</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>com.topsec</groupId>
            <artifactId>tac-spring-boot3-starter</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.name}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>

        </plugins>
    </build>

</project>
