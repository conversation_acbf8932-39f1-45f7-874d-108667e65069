package com.topsec.crm.account.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.topsec.crm.account.api.dto.CrmAccountSignatureVO;
import com.topsec.crm.account.core.entity.CrmAccountSignature;
import com.topsec.crm.account.core.mapper.CrmAccountSignatureMapper;
import com.topsec.crm.account.core.service.ICrmAccountSignatureService;
import com.topsec.crm.file.api.RemoteFileService;
import com.topsec.crm.file.api.RemoteFsmDocService;
import com.topsec.crm.framework.common.bean.CrmFsmDoc;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class CrmAccountSignatureServiceImpl extends ServiceImpl<CrmAccountSignatureMapper, CrmAccountSignature> implements ICrmAccountSignatureService {

    private final RemoteFileService fileService;
    private final RemoteFsmDocService remoteFsmDocService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean saveSignature(CrmAccountSignatureVO crmAccountSignature) {

        String accountId = UserInfoHolder.getCurrentAccountId();
        String personId = UserInfoHolder.getCurrentPersonId();

        // 逻辑删除旧签名
        update().lambda()
                .eq(CrmAccountSignature::getAccountId, accountId)
                .eq(CrmAccountSignature::getDelFlag, false)
                .set(CrmAccountSignature::getDelFlag, true)
                .update();

        CrmAccountSignature signature = HyperBeanUtils.copyProperties(crmAccountSignature, CrmAccountSignature::new);
        signature.setAccountId(accountId);
        signature.setPersonId(personId);

        // 保存新签名
        return save(signature);
    }


    @Override
    public Boolean updateSignature(CrmAccountSignatureVO crmAccountSignature) {
        CrmAccountSignature signature = HyperBeanUtils.copyProperties(crmAccountSignature, CrmAccountSignature::new);
        signature.setDocId(signature.getDocId());
        return updateById(signature);
    }

    @Override
    public Boolean deleteSignature(String id) {
        CrmAccountSignature signature = getById(id);
        if (signature != null){
            signature.setDelFlag(true);
            return updateById(signature);
        }
        return false;
    }

    @Override
    public CrmAccountSignatureVO queryByAccountId(String accountId) {
        CrmAccountSignature signature = baseMapper.selectOne(new LambdaQueryWrapper<CrmAccountSignature>()
                .eq(CrmAccountSignature::getAccountId, accountId)
                .eq(CrmAccountSignature::getDelFlag, false)
        );
        return buildSignatureVO(signature);
    }

    @Override
    public CrmAccountSignatureVO queryByPersonId(String personId) {
        CrmAccountSignature signature = baseMapper.selectOne(new LambdaQueryWrapper<CrmAccountSignature>()
                .eq(CrmAccountSignature::getPersonId, personId)
                .eq(CrmAccountSignature::getDelFlag, false)
        );
        return buildSignatureVO(signature);
    }

    /**
     * 构建 CrmAccountSignatureVO 对象
     */
    private CrmAccountSignatureVO buildSignatureVO(CrmAccountSignature signature) {
        if (signature == null) {
            return null;
        }

        try {
            CrmAccountSignatureVO signatureVO = HyperBeanUtils.copyProperties(signature, CrmAccountSignatureVO::new);
            String docId = signature.getDocId();
            if (docId != null) {
                JsonObject<CrmFsmDoc> docResult = remoteFsmDocService.selectByDocId(docId);
                if (docResult != null && docResult.getObjEntity() != null) {
                    signatureVO.setDocInfo(docResult.getObjEntity());
                }
            }
            return signatureVO;
        } catch (Exception e) {
            return null;
        }
    }
}
