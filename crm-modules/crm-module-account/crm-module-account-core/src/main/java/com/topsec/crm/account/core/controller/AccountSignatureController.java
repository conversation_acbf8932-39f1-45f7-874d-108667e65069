package com.topsec.crm.account.core.controller;

import com.topsec.crm.account.api.dto.CrmAccountSignatureVO;
import com.topsec.crm.account.core.service.ICrmAccountSignatureService;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import static com.topsec.jwt.UserInfoHolder.getCurrentPersonId;

@RestController
@RequestMapping("/accountSignature")
@Tag(name = "预留签名")
@RequiredArgsConstructor
public class AccountSignatureController {
    private final ICrmAccountSignatureService signatureService;

    @PostMapping("/saveSignature")
    @Operation(summary = "保存签名")
    @PreAuthorize
    public JsonObject<Boolean> saveSignature(@RequestBody CrmAccountSignatureVO crmAccountSignature) {
        return new JsonObject<>(signatureService.saveSignature(crmAccountSignature));
    }

    @GetMapping("/queryCurrentPersonSignature")
    @Operation(summary = "查询当前登录人签名")
    @PreAuthorize
    public JsonObject<CrmAccountSignatureVO> queryCurrentPersonSignature() {
        return new JsonObject<>(signatureService.queryByPersonId(getCurrentPersonId()));
    }

}
