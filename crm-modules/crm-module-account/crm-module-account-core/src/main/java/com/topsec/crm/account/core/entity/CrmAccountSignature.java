package com.topsec.crm.account.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.topsec.crm.framework.common.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName(autoResultMap = true, value = "crm_account_signature")
public class CrmAccountSignature extends BaseEntity {

    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 账号id
     */
    private String accountId;

    /**
     * 人员id
     */
    private String personId;

    /**
     * 文件id
     */
    private String docId;

    /**
     * 拓展信息
     */
    private String extendInfo;

    /**
     * 删除标记 0-未删除 1-已删除
     */
    private Boolean delFlag;

}
