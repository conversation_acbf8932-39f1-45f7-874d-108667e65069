package com.topsec.crm.account.core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.topsec.crm.account.api.dto.CrmAccountSignatureVO;
import com.topsec.crm.account.core.entity.CrmAccountSignature;

public interface ICrmAccountSignatureService extends IService<CrmAccountSignature> {

    /**
     * 保存签名
     */
    Boolean saveSignature(CrmAccountSignatureVO crmAccountSignature);

    /**
     * 修改签名
     */
    Boolean updateSignature(CrmAccountSignatureVO crmAccountSignature);

    /**
     * 删除签名
     */
    Boolean deleteSignature(String id);

    /**
     * 根据账户ID查询签名
     */
    CrmAccountSignatureVO queryByAccountId(String accountId);

    /**
     * 根据人员ID查询签名
     */
    CrmAccountSignatureVO queryByPersonId(String personId);

}
