package com.topsec.crm.account.api.dto;

import com.topsec.crm.framework.common.bean.CrmFsmDoc;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class CrmAccountSignatureVO {

    @Schema(description = "主键")
    private String id;

    @Schema(description = "账号id")
    private String accountId;

    @Schema(description = "人员id")
    private String personId;

    @Schema(description = "文件id")
    private String docId;

    @Schema(description = "文件信息")
    private CrmFsmDoc docInfo;

}
