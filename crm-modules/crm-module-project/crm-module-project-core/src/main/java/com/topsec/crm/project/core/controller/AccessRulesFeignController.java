package com.topsec.crm.project.core.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.project.core.entity.CrmProjectProductAccessGroupDetail;
import com.topsec.crm.project.core.service.ICrmProjectProductAccessGroupService;
import com.topsec.crm.project.core.service.ICrmProjectProductAccessGroupDetailService;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 通路规则Feign接口控制器
 */
@RestController
@RequestMapping("/accessRulesFeign")
@Tag(name = "通路规则Feign接口", description = "AccessRulesFeignController")
public class AccessRulesFeignController extends BaseController {

    @Autowired
    private ICrmProjectProductAccessGroupService accessGroupService;
    
    @Autowired
    private ICrmProjectProductAccessGroupDetailService accessGroupDetailService;

    /**
     * 根据组名称获取产品信息
     * @param groupName 组名称
     * @return 产品ID列表
     */
    @GetMapping("/getInfoByGroupName")
    @Operation(summary = "根据组名称获取产品信息")
    public JsonObject<List<String>> getInfoByGroupName(@RequestParam("groupName") String groupName) {
        // 根据组名称查询产品组详情
        List<CrmProjectProductAccessGroupDetail> groupDetails = accessGroupDetailService.list(
            new QueryWrapper<CrmProjectProductAccessGroupDetail>()
                .eq("group_name", groupName)
                .eq("del_flag", 0)
        );
        
        // 提取产品ID列表
        List<String> productIds = groupDetails.stream()
                .map(CrmProjectProductAccessGroupDetail::getProductId)
                .collect(Collectors.toList());
        
        return new JsonObject<>(productIds);
    }
}
