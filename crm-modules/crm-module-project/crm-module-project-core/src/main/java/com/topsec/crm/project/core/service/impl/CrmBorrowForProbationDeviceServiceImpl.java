package com.topsec.crm.project.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.topsec.crm.flow.api.RemoteFlowService;
import com.topsec.crm.flow.api.dto.BorrowForProbationDeviceOccupyStateVO;
import com.topsec.crm.flow.api.vo.handoverProcess.HandoverTrialBorrowVo;
import com.topsec.crm.framework.common.bean.HandoverProcessQuery;
import com.topsec.crm.framework.common.enums.ProductTypeEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.name.NameUtils;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.Query;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.project.api.dto.BorrowForProbationDevicePageQuery;
import com.topsec.crm.project.api.dto.BorrowForProbationPenaltyPageQuery;
import com.topsec.crm.project.api.entity.*;
import com.topsec.crm.project.core.entity.*;
import com.topsec.crm.project.core.mapper.CrmBorrowForProbationDeviceMapper;
import com.topsec.crm.project.core.service.ICrmBorrowForProbationDeviceService;
import com.topsec.crm.project.core.service.ICrmBorrowForProbationLimitService;
import com.topsec.tac.filter.UserInfoThreadExecutor;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.api.client.TosEmployeeClient;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.tos.common.vo.EmployeeVO;
import com.topsec.tos.common.vo.TosDepartmentVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.topsec.crm.project.core.util.IdGenerator;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <p>
 * 已生效借试用设备表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-16
 */
@Service
public class CrmBorrowForProbationDeviceServiceImpl extends ServiceImpl<CrmBorrowForProbationDeviceMapper, CrmBorrowForProbationDevice> implements ICrmBorrowForProbationDeviceService {

    @Autowired
    private ICrmBorrowForProbationDeviceService crmBorrowForProbationDeviceService;

    @Autowired
    private ICrmBorrowForProbationLimitService crmBorrowForProbationLimitService;

    @Resource
    private TosEmployeeClient tosEmployeeClient;

    @Resource
    private RemoteFlowService remoteFlowService;

    @Resource
    private IdGenerator idGenerator;

    /**
     * @param crmBorrowForProbationVO
     * @return
     * @Description: 保存借试用设备
     * @author: leo
     * @date: 2024-07-15
     */
    @Override
    public Boolean saveBorrowForProbationPassData(CrmBorrowForProbationVO crmBorrowForProbationVO) {
        List<CrmBorrowForProbationDevice> list = new ArrayList<>();
        List<CrmBorrowForProbationProductVO> productList = crmBorrowForProbationVO.getProductList();
        if(CollectionUtils.isNotEmpty(productList)){
            productList.forEach(product -> {
                Integer productNum = product.getProductNum();
                for (int i = 0; i < productNum; i++) {
                    CrmBorrowForProbationDevice crmBorrowForProbationDevice = CrmBorrowForProbationDevice.builder()
                            .id(UUID.randomUUID().toString().replace("-", ""))
                            .borrowId(crmBorrowForProbationVO.getId())
                            .processInstanceId(crmBorrowForProbationVO.getProcessInstanceId())
                            .processNumber(crmBorrowForProbationVO.getProcessNumber())
                            .borrowProductId(product.getId())
                            .personId(crmBorrowForProbationVO.getPersonId())
                            //.psn()  // 生产以后回写序列号
                            .psn(idGenerator.buildBorrowForProbationPsn())
                            .borrowProductType(crmBorrowForProbationVO.getBorrowProductType())
                            .projectId(crmBorrowForProbationVO.getProjectId())
                            .projectNo(crmBorrowForProbationVO.getProjectNo())
                            .projectName(crmBorrowForProbationVO.getProjectName())
                            .customerId(crmBorrowForProbationVO.getCustomerId())
                            .customerName(crmBorrowForProbationVO.getCustomerName())
                            .stuffCode(product.getActualStuffCode())
                            .productName(product.getProductName())
                            .pnCode(product.getPnCode())
                            .productType(product.getProductType())
                            .productCategory(product.getProductCategory())
                            .productSpecification(product.getProductSpecification())
                            .quotedPrice(product.getQuotedPrice())
                            .firstBorrowTime(crmBorrowForProbationVO.getUpdateTime())
                            .borrowTime(crmBorrowForProbationVO.getUpdateTime())
                            .borrowType(crmBorrowForProbationVO.getBorrowType())
                            //.outboundDeliveryDate() // 回写序列号的时候进行填充
                            .estimatedReturnTime(crmBorrowForProbationVO.getEstimatedReturnTime())
                            .borrowStatus(ProductTypeEnum.BorrowForProbationStatusEnum.BORROW.getStatus())
                            .middleman(crmBorrowForProbationVO.getMiddleman())
                            .deposit(product.getDeposit())
                            //.storageLocation() // 回写序列号的时候进行填充
                            .build();
                    list.add(crmBorrowForProbationDevice);
                }
            });
        }
        return crmBorrowForProbationDeviceService.saveBatch(list);
    }

    /**
     * @param borrowForProbationPenaltyPageQuery
     * @return
     * @Description: 查询已生效借试用设备中需要罚款的设备信息
     * @author: leo
     * @date: 2024-07-15
     */
    @Override
    public PageUtils<CrmBorrowForProbationDevice> penaltyProductPage(BorrowForProbationPenaltyPageQuery borrowForProbationPenaltyPageQuery) {
        Set<String> personIds = new HashSet<>();
        String personId = borrowForProbationPenaltyPageQuery.getPersonId();
        if(StringUtils.isNotBlank(personId)){
            personIds.add(personId);
            /*DeptBorrowForProbationVO deptBorrowForProbationVO = deptBorrowForProbationSituation(personId);
            if(deptBorrowForProbationVO.getDeptBorrowingRatio().doubleValue() < 1){
                // 借用率小于100% 直接免罚
                return new PageUtils<>(new ArrayList<>(), 0, borrowForProbationPenaltyPageQuery.getPageSize(), borrowForProbationPenaltyPageQuery.getPageNum());
            }*/
            JsonObject<EmployeeVO> employeeVOJsonObject = tosEmployeeClient.findById(personId);
            if(employeeVOJsonObject.isSuccess()){
                TosDepartmentVO department = employeeVOJsonObject.getObjEntity().getDept();
                if(null != department){
                    JsonObject<List<EmployeeVO>> employeeByDeptIdNew = tosEmployeeClient.findEmployeeByDeptIdNew(department.getUuid(), true);
                    if(employeeByDeptIdNew.isSuccess() && CollectionUtils.isNotEmpty(employeeByDeptIdNew.getObjEntity())){
                        personIds.addAll(employeeByDeptIdNew.getObjEntity().stream().map(EmployeeVO::getUuid).collect(Collectors.toSet()));
                    }
                }
            }
        }else{
            throw new CrmException("参数错误");
        }
        if(CollectionUtils.isNotEmpty(personIds)){
            LambdaQueryWrapper<CrmBorrowForProbationDevice> queryWrapper = new LambdaQueryWrapper<CrmBorrowForProbationDevice>()
                    .in(CrmBorrowForProbationDevice::getPersonId, personIds)
                    //.ne(CrmBorrowForProbationDevice::getBorrowType, "维修备机") // 维修备机免罚
                    .lt(CrmBorrowForProbationDevice::getFirstBorrowTime, LocalDateTime.now().plusYears(-1)) // 借试用设备不足一年的免罚
                    //.and(m -> m.eq(CrmBorrowForProbationDevice::getMiddleman, false).or(n -> n.eq(CrmBorrowForProbationDevice::getMiddleman, true).eq(CrmBorrowForProbationDevice::getDeposit, 0))) // 中间商借用且押金不为0免罚
                    .in(CrmBorrowForProbationDevice::getBorrowStatus, ProductTypeEnum.BorrowForProbationStatusEnum.inBorrowStatus())
                    .eq(StringUtils.isNotBlank(borrowForProbationPenaltyPageQuery.getBorrowStatus()),CrmBorrowForProbationDevice::getBorrowStatus, borrowForProbationPenaltyPageQuery.getBorrowStatus());
            Page<CrmBorrowForProbationDevice> page = crmBorrowForProbationDeviceService.page(new Query<CrmBorrowForProbationDevice>().getPage(borrowForProbationPenaltyPageQuery), queryWrapper);
            if(CollectionUtils.isNotEmpty(page.getRecords())){
                List<CrmBorrowForProbationDevice> records = page.getRecords();
                List<String> collect = records.stream().map(CrmBorrowForProbationDevice::getPersonId).collect(Collectors.toList());
                JsonObject<List<EmployeeVO>> byIds = tosEmployeeClient.findByIds(collect);
                records.forEach(crmBorrowForProbationDevice -> {
                    if(byIds.isSuccess()){
                        Optional<EmployeeVO> any = byIds.getObjEntity().stream().filter(employeeVO -> employeeVO.getUuid().equals(crmBorrowForProbationDevice.getPersonId())).findAny();
                        if(any.isPresent()){
                            crmBorrowForProbationDevice.setUsername(NameUtils.getNameByEmployeeVO(any.get()));
                            TosDepartmentVO dept = any.get().getDept();
                            if(null != dept){
                                crmBorrowForProbationDevice.setDeptId(dept.getUuid());
                                crmBorrowForProbationDevice.setDeptName(dept.getName());
                            }
                        }
                    }
                });
            }
            return new PageUtils<>(page);
        }else{
            return new PageUtils<>(new ArrayList<>(), 0, borrowForProbationPenaltyPageQuery.getPageSize(), borrowForProbationPenaltyPageQuery.getPageNum());
        }
    }

    /**
     * @param crmBorrowForProbationDevice
     * @return
     * @Description: 更新借试用设备
     * @author: leo
     * @date: 2024-07-15
     */
    @Override
    public Boolean updateBorrowForProbationDevice(CrmBorrowForProbationDevice crmBorrowForProbationDevice) {
        return crmBorrowForProbationDeviceService.updateById(crmBorrowForProbationDevice);
    }

    /**
     * @param id
     * @return
     * @Description: 根据id查询借试用设备
     * @author: leo
     * @date: 2024-07-15
     */
    @Override
    public CrmBorrowForProbationDevice getBorrowForProbationDeviceById(String id) {
        return crmBorrowForProbationDeviceService.getById(id);
    }

    /**
     * @param ids
     * @return
     * @Description: 根据id查询借试用设备
     * @author: leo
     * @date: 2024-07-15
     */
    @Override
    public List<CrmBorrowForProbationDevice> getBorrowForProbationDeviceByIds(List<String> ids) {
        return crmBorrowForProbationDeviceService.listByIds(ids);
    }

    /**
     * @param id
     * @param personIdsOfPermission
     * @return
     * @Description: 根据id查询借试用设备
     * @author: leo
     * @date: 2024-07-15
     */
    @Override
    public CrmBorrowForProbationDevice getDetailById(String id, Set<String> personIdsOfPermission) {
        LambdaQueryWrapper<CrmBorrowForProbationDevice> lambdaQueryWrapper = new LambdaQueryWrapper<CrmBorrowForProbationDevice>().eq(CrmBorrowForProbationDevice::getId, id)
                .in(CollectionUtils.isNotEmpty(personIdsOfPermission), CrmBorrowForProbationDevice::getPersonId, personIdsOfPermission);
        CrmBorrowForProbationDevice crmBorrowForProbationDevice = crmBorrowForProbationDeviceService.getOne(lambdaQueryWrapper);
        if(crmBorrowForProbationDevice != null){
            JsonObject<EmployeeVO> byId = tosEmployeeClient.findById(crmBorrowForProbationDevice.getPersonId());
            if(byId.isSuccess()){
                EmployeeVO objEntity = byId.getObjEntity();
                crmBorrowForProbationDevice.setUsername(NameUtils.getNameByEmployeeVO(objEntity));
                TosDepartmentVO dept = objEntity.getDept();
                if(null != dept){
                    crmBorrowForProbationDevice.setDeptId(dept.getUuid());
                    crmBorrowForProbationDevice.setDeptName(dept.getName());
                }
            }
            return crmBorrowForProbationDevice;
        }else{
            throw new RuntimeException("未查询到数据");
        }
    }

    /**
     * @param personId
     * @return
     * @Description: 查询借试用情况
     * @author: leo
     * @date: 2024-07-15 15:01
     */
    @Override
    public CrmBorrowForProbationSituationVO borrowForProbationSituation(String personId) {
        JsonObject<EmployeeVO> employeeVOJsonObject = tosEmployeeClient.findById(personId);
        if(employeeVOJsonObject.isSuccess()){
            TosDepartmentVO department = employeeVOJsonObject.getObjEntity().getDept();
            if(null != department){
                CrmBorrowForProbationSituationVO crmBorrowForProbationSituationVO = new CrmBorrowForProbationSituationVO();
                // 异步获取个人借用情况
                CompletableFuture<PersonBorrowForProbationVO> futurePersonal = CompletableFuture.supplyAsync(() -> buildPersonBorrowForProbation(employeeVOJsonObject.getObjEntity(),department), UserInfoThreadExecutor.getExecutor());

                // 异步获取部门借用情况
                CompletableFuture<DeptBorrowForProbationVO> futureDept = CompletableFuture.supplyAsync(() -> buildDeptBorrowForProbation(department), UserInfoThreadExecutor.getExecutor());

                // 异步获取机构借用情况
                CompletableFuture<List<OrganizationBorrowForProbationVO>> futureOrganization = CompletableFuture.supplyAsync(() -> buildOrganizationBorrowForProbation(department), UserInfoThreadExecutor.getExecutor());

                // 当异步任务都完成时，组合结果
                CompletableFuture<Void> combinedFuture = CompletableFuture.allOf(futurePersonal, futureDept,futureOrganization);
                // 阻塞直到所有任务完成
                combinedFuture.join();
                crmBorrowForProbationSituationVO.setPersonBorrowForProbation(futurePersonal.join());
                crmBorrowForProbationSituationVO.setDeptBorrowForProbation(futureDept.join());
                crmBorrowForProbationSituationVO.setOrganizationBorrowForProbation(futureOrganization.join());
                return crmBorrowForProbationSituationVO;
            }
        }
        return null;
    }

    /**
     * @param personId
     * @return
     * @Description: 查询部门借试用情况
     * @author: leo
     * @date: 2024-07-15 15:01
     */
    @Override
    public DeptBorrowForProbationVO deptBorrowForProbationSituation(String personId) {
        JsonObject<EmployeeVO> employeeVOJsonObject = tosEmployeeClient.findById(personId);
        if(employeeVOJsonObject.isSuccess()){
            TosDepartmentVO department = employeeVOJsonObject.getObjEntity().getDept();
            if(null != department){
                return buildDeptBorrowForProbation(department);
            }
        }
        return null;
    }

    /**
     * @param deptId
     * @return
     * @Description: 统计部门借试用情况
     * @author: leo
     * @date: 2024-07-15 15:01
     */
    @Override
    public DeptBorrowForProbationVO DeptBorrowForProbationStatistics(String deptId,BigDecimal deptLimit) {
        JsonObject<List<EmployeeVO>> employeeByDeptIdNew = tosEmployeeClient.findEmployeeByDeptIdNew(deptId, true);
        if(employeeByDeptIdNew.isSuccess() && CollectionUtils.isNotEmpty(employeeByDeptIdNew.getObjEntity())){
            Set<String> personIds = employeeByDeptIdNew.getObjEntity().stream().map(EmployeeVO::getUuid).collect(Collectors.toSet());
            List<CrmBorrowForProbationDevice> list = crmBorrowForProbationDeviceService.list(new LambdaQueryWrapper<CrmBorrowForProbationDevice>()
                    .in(CrmBorrowForProbationDevice::getBorrowStatus, ProductTypeEnum.BorrowForProbationStatusEnum.inBorrowStatus())
                    .in(CrmBorrowForProbationDevice::getPersonId, personIds));
            BorrowForProbationCollector borrowForProbationCollector = dataCollector(list);
            BigDecimal divide = deptLimit.compareTo(BigDecimal.ZERO) == 0? BigDecimal.ZERO : borrowForProbationCollector.getBorrowedAmount().divide(deptLimit, 2, BigDecimal.ROUND_HALF_UP);
            return DeptBorrowForProbationVO.builder().deptId(deptId).deptBorrowingLimit(deptLimit).deptBorrowingRatio(divide).deptBorrowedAmount(borrowForProbationCollector.getBorrowedAmount())
                    .deptBorrowedDevice(borrowForProbationCollector.getDeviceCount()).deptBorrowedComponent(borrowForProbationCollector.getComponentCount()).deptOverdueDevice(borrowForProbationCollector.getOverdueDevice())
                    .deptOverdueComponent(borrowForProbationCollector.getOverdueComponent()).deptExceedDevice(borrowForProbationCollector.getExceedDevice()).deptExceedComponent(borrowForProbationCollector.getExceedComponent()).build();

        }
        return null;
    }

    /**
     * @param borrowForProbationPageQuery
     * @return
     * @Description: 查询借试用设备
     * @author: leo
     * @date: 2024-07-15 15:01
     */
    @Override
    public PageUtils<CrmBorrowForProbationDevice> borrowForProbationDevicePage(BorrowForProbationDevicePageQuery borrowForProbationPageQuery) {
        String borrowId = borrowForProbationPageQuery.getBorrowId();
        String stuffCode = borrowForProbationPageQuery.getStuffCode();
        Boolean psnNotNull = borrowForProbationPageQuery.getPsnNotNull();
        String processNumber = borrowForProbationPageQuery.getProcessNumber();
        String personId = borrowForProbationPageQuery.getPersonId();
        DataScopeParam dataScopeParam = borrowForProbationPageQuery.getDataScopeParam();
        Set<String> personIdsOfPermission = dataScopeParam != null ? dataScopeParam.getPersonIdList() : Collections.EMPTY_SET;

        LambdaQueryWrapper<CrmBorrowForProbationDevice> queryWrapper = new LambdaQueryWrapper<CrmBorrowForProbationDevice>()
                .like(StringUtils.isNotBlank(stuffCode),CrmBorrowForProbationDevice::getStuffCode, stuffCode)
                .eq(StringUtils.isNotBlank(borrowId),CrmBorrowForProbationDevice::getBorrowId, borrowId)
                .eq(StringUtils.isNotBlank(processNumber),CrmBorrowForProbationDevice::getProcessNumber, processNumber)
                .eq(StringUtils.isNotBlank(personId),CrmBorrowForProbationDevice::getPersonId, personId)
                .in(CollectionUtils.isNotEmpty(borrowForProbationPageQuery.getBorrowStatus()),CrmBorrowForProbationDevice::getBorrowStatus, borrowForProbationPageQuery.getBorrowStatus())
                .in(CrmBorrowForProbationDevice::getBorrowStatus, ProductTypeEnum.BorrowForProbationStatusEnum.inBorrowStatus())
                .like(StringUtils.isNotBlank(borrowForProbationPageQuery.getPsn()),CrmBorrowForProbationDevice::getPsn, borrowForProbationPageQuery.getPsn())
                .isNotNull(null != psnNotNull && psnNotNull,CrmBorrowForProbationDevice::getPsn)
                .in(CollectionUtils.isNotEmpty(personIdsOfPermission), CrmBorrowForProbationDevice::getPersonId, personIdsOfPermission)
                .orderByDesc(CrmBorrowForProbationDevice::getFirstBorrowTime);
        Page<CrmBorrowForProbationDevice> page = crmBorrowForProbationDeviceService.page(new Query<CrmBorrowForProbationDevice>().getPage(borrowForProbationPageQuery), queryWrapper);
        List<CrmBorrowForProbationDevice> records = page.getRecords();
        if(CollectionUtils.isNotEmpty(records)){
            Set<String> deviceIds = records.stream().map(CrmBorrowForProbationDevice::getId).collect(Collectors.toSet());
            JsonObject<Map<String, BorrowForProbationDeviceOccupyStateVO>> mapJsonObject = remoteFlowService.queryBorrowForProbationDeviceOccupyState(deviceIds);
            List<String> collect = records.stream().map(CrmBorrowForProbationDevice::getPersonId).collect(Collectors.toList());
            JsonObject<List<EmployeeVO>> byIds = tosEmployeeClient.findByIds(collect);
            records.forEach(crmBorrowForProbationDevice -> {
                // 附加借试用设备的占用状态（例如：正在走续借流程的设备不允许发起归还流程等等）
                if(mapJsonObject.isSuccess()){
                    BorrowForProbationDeviceOccupyStateVO borrowForProbationDeviceOccupyStateVO = mapJsonObject.getObjEntity().get(crmBorrowForProbationDevice.getId());
                    if(null != borrowForProbationDeviceOccupyStateVO){
                        crmBorrowForProbationDevice.setOccupy(borrowForProbationDeviceOccupyStateVO.getState()==1);
                        crmBorrowForProbationDevice.setOccupyFlowName(borrowForProbationDeviceOccupyStateVO.getOccupyFlowName());
                    }else{
                        crmBorrowForProbationDevice.setOccupy(false);
                    }
                }
                if(byIds.isSuccess()){
                    Optional<EmployeeVO> any = byIds.getObjEntity().stream().filter(employeeVO -> employeeVO.getUuid().equals(crmBorrowForProbationDevice.getPersonId())).findAny();
                    if(any.isPresent()){
                        crmBorrowForProbationDevice.setUsername(NameUtils.getNameByEmployeeVO(any.get()));
                        TosDepartmentVO dept = any.get().getDept();
                        if(null != dept){
                            crmBorrowForProbationDevice.setDeptId(dept.getUuid());
                            crmBorrowForProbationDevice.setDeptName(dept.getName());
                        }
                    }
                }
            });
        }
        return new PageUtils<>(page);
    }

    /**
     * @param borrowForProbationPageQuery
     * @return
     * @Description: 查询借试用设备
     * @author: leo
     * @date: 2024-07-15 15:01
     */
    @Override
    public PageUtils<CrmBorrowForProbationDevice> deptBorrowForProbationDevicePage(BorrowForProbationDevicePageQuery borrowForProbationPageQuery) {
        Set<String> personIds = new HashSet<>();
        String deptId = borrowForProbationPageQuery.getDeptId();
        String personId = borrowForProbationPageQuery.getPersonId();
        String borrowId = borrowForProbationPageQuery.getBorrowId();
        String stuffCode = borrowForProbationPageQuery.getStuffCode();
        Boolean psnNotNull = borrowForProbationPageQuery.getPsnNotNull();
        String processNumber = borrowForProbationPageQuery.getProcessNumber();
        if(StringUtils.isNotBlank(personId)){
            personIds.add(personId);
        }
        if(StringUtils.isNotBlank(deptId)){
            JsonObject<List<EmployeeVO>> employeeByDeptIdNew = tosEmployeeClient.findEmployeeByDeptIdNew(deptId, true);
            if(employeeByDeptIdNew.isSuccess() && CollectionUtils.isNotEmpty(employeeByDeptIdNew.getObjEntity())){
                personIds.addAll(employeeByDeptIdNew.getObjEntity().stream().map(EmployeeVO::getUuid).collect(Collectors.toSet()));
            }
        }
        if(CollectionUtils.isNotEmpty(personIds)){
            LambdaQueryWrapper<CrmBorrowForProbationDevice> queryWrapper = new LambdaQueryWrapper<CrmBorrowForProbationDevice>()
                    .in(CrmBorrowForProbationDevice::getPersonId, personIds)
                    .eq(StringUtils.isNotBlank(stuffCode),CrmBorrowForProbationDevice::getStuffCode, stuffCode)
                    .eq(StringUtils.isNotBlank(borrowId),CrmBorrowForProbationDevice::getBorrowId, borrowId)
                    .eq(StringUtils.isNotBlank(processNumber),CrmBorrowForProbationDevice::getProcessNumber, processNumber)
                    .in(CollectionUtils.isNotEmpty(borrowForProbationPageQuery.getBorrowStatus()),CrmBorrowForProbationDevice::getBorrowStatus, borrowForProbationPageQuery.getBorrowStatus())
                    .in(CrmBorrowForProbationDevice::getBorrowStatus, ProductTypeEnum.BorrowForProbationStatusEnum.inBorrowStatus())
                    .eq(StringUtils.isNotBlank(borrowForProbationPageQuery.getPsn()),CrmBorrowForProbationDevice::getPsn, borrowForProbationPageQuery.getPsn())
                    .isNotNull(null != psnNotNull && psnNotNull,CrmBorrowForProbationDevice::getPsn)
                    .orderByDesc(CrmBorrowForProbationDevice::getFirstBorrowTime);
            Page<CrmBorrowForProbationDevice> page = crmBorrowForProbationDeviceService.page(new Query<CrmBorrowForProbationDevice>().getPage(borrowForProbationPageQuery), queryWrapper);
            List<CrmBorrowForProbationDevice> records = page.getRecords();
            if(CollectionUtils.isNotEmpty(records)){
                Set<String> deviceIds = records.stream().map(CrmBorrowForProbationDevice::getId).collect(Collectors.toSet());
                JsonObject<Map<String, BorrowForProbationDeviceOccupyStateVO>> mapJsonObject = remoteFlowService.queryBorrowForProbationDeviceOccupyState(deviceIds);
                List<String> collect = records.stream().map(CrmBorrowForProbationDevice::getPersonId).distinct().collect(Collectors.toList());
                JsonObject<List<EmployeeVO>> byIds = tosEmployeeClient.findByIdsByPost(collect);
                records.forEach(crmBorrowForProbationDevice -> {
                    // 附加借试用设备的占用状态（例如：正在走续借流程的设备不允许发起归还流程等等）
                    if(mapJsonObject.isSuccess()){
                        BorrowForProbationDeviceOccupyStateVO borrowForProbationDeviceOccupyStateVO = mapJsonObject.getObjEntity().get(crmBorrowForProbationDevice.getId());
                        if(null != borrowForProbationDeviceOccupyStateVO){
                            crmBorrowForProbationDevice.setOccupy(borrowForProbationDeviceOccupyStateVO.getState()==1);
                            crmBorrowForProbationDevice.setOccupyFlowName(borrowForProbationDeviceOccupyStateVO.getOccupyFlowName());
                        }else{
                            crmBorrowForProbationDevice.setOccupy(false);
                        }
                    }
                    if(byIds.isSuccess()){
                        Optional<EmployeeVO> any = byIds.getObjEntity().stream().filter(employeeVO -> employeeVO.getUuid().equals(crmBorrowForProbationDevice.getPersonId())).findAny();
                        if(any.isPresent()){
                            crmBorrowForProbationDevice.setUsername(NameUtils.getNameByEmployeeVO(any.get()));
                            TosDepartmentVO dept = any.get().getDept();
                            if(null != dept){
                                crmBorrowForProbationDevice.setDeptId(dept.getUuid());
                                crmBorrowForProbationDevice.setDeptName(dept.getName());
                            }
                        }
                    }
                });
            }
            return new PageUtils<>(page);
        }else{
            return new PageUtils<>();
        }
    }

    /**
     * @param personIdsOfPermission
     * @return
     * @Description: 查询借试用设备统计
     * @author: leo
     * @date: 2024-07-15
     */
    @Override
    public CrmBorrowForProbationDeviceStatisticVO statistic(Set<String> personIdsOfPermission) {
        List<CrmBorrowForProbationDevice> list = crmBorrowForProbationDeviceService.list(new LambdaQueryWrapper<CrmBorrowForProbationDevice>().in(CollectionUtils.isNotEmpty(personIdsOfPermission),CrmBorrowForProbationDevice::getPersonId, personIdsOfPermission)
                .in(CrmBorrowForProbationDevice::getBorrowStatus, ProductTypeEnum.BorrowForProbationStatusEnum.inBorrowStatus()));
        BorrowForProbationCollector borrowForProbationCollector = dataCollector(list);
        long inBorrow = borrowForProbationCollector.getDeviceCount() + borrowForProbationCollector.getComponentCount();
        long exceed = borrowForProbationCollector.getExceedDevice() + borrowForProbationCollector.getExceedComponent();
        long overdue = borrowForProbationCollector.getOverdueDevice() + borrowForProbationCollector.getOverdueComponent();
        long borrowTotal = list.size() ;
        return CrmBorrowForProbationDeviceStatisticVO.builder().inBorrow(inBorrow).borrowTotal(borrowTotal).exceed(exceed).overdue(overdue).build();
    }



    private List<OrganizationBorrowForProbationVO> buildOrganizationBorrowForProbation(TosDepartmentVO department) {
        List<OrganizationBorrowForProbationVO> result = new ArrayList<>();
        List<String> uuidPath = department.getUuidPath();
        List<String> namePath = department.getNamePath();
        // 创建一个 CompletableFuture 数组来保存所有的异步任务
        CompletableFuture<OrganizationBorrowForProbationVO>[] futures = new CompletableFuture[uuidPath.size()-2];

        // 调整遍历条件，避免不必要的部门遍历
        // 动态创建异步任务
        for (int i = 2; i < uuidPath.size(); i++) {
            int finalI = i;
            futures[i-2] = CompletableFuture.supplyAsync(() ->
                    buildOrganizationBorrowForProbationItem(uuidPath.get(finalI), namePath.get(finalI)), UserInfoThreadExecutor.getExecutor()
            );
        }

        // 等待所有异步任务完成
        CompletableFuture<Void> combinedFuture = CompletableFuture.allOf(futures);
        combinedFuture.join();

        // 收集所有已完成的异步任务的结果
        for (CompletableFuture<OrganizationBorrowForProbationVO> future : futures) {
            OrganizationBorrowForProbationVO item = future.join();
            if (item != null) {
                result.add(item);
            }
        }

        return result;
    }

    private OrganizationBorrowForProbationVO buildOrganizationBorrowForProbationItem(String deptId,String deptName) {
        // 获取部门借用限额
        CrmBorrowForProbationLimit crmBorrowForProbationLimit = crmBorrowForProbationLimitService.borrowForProbationLimitDetail(deptId);
        BigDecimal deptLimit = crmBorrowForProbationLimit.getLimit();
        JsonObject<List<EmployeeVO>> employeeByDeptIdNew = tosEmployeeClient.findEmployeeByDeptIdNew(deptId, true);
        if(employeeByDeptIdNew.isSuccess() && CollectionUtils.isNotEmpty(employeeByDeptIdNew.getObjEntity())){
            Set<String> personIds = employeeByDeptIdNew.getObjEntity().stream().map(EmployeeVO::getUuid).collect(Collectors.toSet());
            List<CrmBorrowForProbationDevice> list = crmBorrowForProbationDeviceService.list(new LambdaQueryWrapper<CrmBorrowForProbationDevice>()
                    .in(CrmBorrowForProbationDevice::getBorrowStatus, ProductTypeEnum.BorrowForProbationStatusEnum.inBorrowStatus())
                    .in(CrmBorrowForProbationDevice::getPersonId, personIds));
            BorrowForProbationCollector borrowForProbationCollector = dataCollector(list);
            BigDecimal divide = deptLimit.compareTo(BigDecimal.ZERO) == 0? BigDecimal.ZERO : borrowForProbationCollector.getBorrowedAmount().divide(deptLimit, 2, BigDecimal.ROUND_HALF_UP);
            return OrganizationBorrowForProbationVO.builder().deptId(deptId).deptName(deptName).deptBorrowedRatioPre(divide).deptBorrowedAmount(borrowForProbationCollector.getBorrowedAmount()).deptBorrowingLimit(deptLimit).build();
        }
        return null;
    }

    private DeptBorrowForProbationVO buildDeptBorrowForProbation(TosDepartmentVO department) {
        // 获取部门借用限额
        CrmBorrowForProbationLimit crmBorrowForProbationLimit = crmBorrowForProbationLimitService.borrowForProbationLimitDetail(department.getUuid());
        BigDecimal deptLimit = crmBorrowForProbationLimit.getLimit();
        JsonObject<List<EmployeeVO>> employeeByDeptIdNew = tosEmployeeClient.findEmployeeByDeptIdNew(department.getUuid(), true);
        if(employeeByDeptIdNew.isSuccess() && CollectionUtils.isNotEmpty(employeeByDeptIdNew.getObjEntity())){
            Set<String> personIds = employeeByDeptIdNew.getObjEntity().stream().map(EmployeeVO::getUuid).collect(Collectors.toSet());
            List<CrmBorrowForProbationDevice> list = crmBorrowForProbationDeviceService.list(new LambdaQueryWrapper<CrmBorrowForProbationDevice>().in(CrmBorrowForProbationDevice::getPersonId, personIds)
                    .in(CrmBorrowForProbationDevice::getBorrowStatus, ProductTypeEnum.BorrowForProbationStatusEnum.inBorrowStatus()));
            BorrowForProbationCollector borrowForProbationCollector = dataCollector(list);
            BigDecimal divide = deptLimit.compareTo(BigDecimal.ZERO) == 0? BigDecimal.ZERO : borrowForProbationCollector.getBorrowedAmount().divide(deptLimit, 2, BigDecimal.ROUND_HALF_UP);
            BorrowForProbationDevicePageQuery borrowForProbationPageQuery = new BorrowForProbationDevicePageQuery();
            borrowForProbationPageQuery.setDeptId(department.getUuid());
            borrowForProbationPageQuery.setPageNum(1);
            borrowForProbationPageQuery.setPageSize(Integer.MAX_VALUE);
            PageUtils<CrmBorrowForProbationDevice> crmBorrowForProbationDevices = crmBorrowForProbationDeviceService.deptBorrowForProbationDevicePage(borrowForProbationPageQuery);
            List<CrmBorrowForProbationDeviceVO> deptBorrowForProbationDevices = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(crmBorrowForProbationDevices.getList())){
                deptBorrowForProbationDevices = HyperBeanUtils.copyListPropertiesByJackson(crmBorrowForProbationDevices.getList(), CrmBorrowForProbationDeviceVO.class);
            }
            return DeptBorrowForProbationVO.builder().deptId(department.getUuid()).deptName(department.getName()).deptBorrowingLimit(deptLimit).deptBorrowingRatio(divide).deptBorrowedAmount(borrowForProbationCollector.getBorrowedAmount())
                    .deptBorrowedDevice(borrowForProbationCollector.getDeviceCount()).deptBorrowedComponent(borrowForProbationCollector.getComponentCount()).deptOverdueDevice(borrowForProbationCollector.getOverdueDevice())
                    .deptOverdueComponent(borrowForProbationCollector.getOverdueComponent()).deptExceedDevice(borrowForProbationCollector.getExceedDevice()).deptExceedComponent(borrowForProbationCollector.getExceedComponent())
                    .deptBorrowForProbationDevices(deptBorrowForProbationDevices).build();
        }
        return null;
    }

    private PersonBorrowForProbationVO buildPersonBorrowForProbation(EmployeeVO person, TosDepartmentVO department) {
        // 获取部门借用限额
        CrmBorrowForProbationLimit crmBorrowForProbationLimit = crmBorrowForProbationLimitService.borrowForProbationLimitDetail(department.getUuid());
        BigDecimal deptLimit = crmBorrowForProbationLimit.getLimit();
        List<CrmBorrowForProbationDevice> list = crmBorrowForProbationDeviceService.list(new LambdaQueryWrapper<CrmBorrowForProbationDevice>().eq(CrmBorrowForProbationDevice::getPersonId, person.getUuid())
                .in(CrmBorrowForProbationDevice::getBorrowStatus, ProductTypeEnum.BorrowForProbationStatusEnum.inBorrowStatus()));
        BorrowForProbationCollector borrowForProbationCollector = dataCollector(list);
        BigDecimal divide = deptLimit.compareTo(BigDecimal.ZERO) == 0? BigDecimal.ZERO : borrowForProbationCollector.getBorrowedAmount().divide(deptLimit, 2, BigDecimal.ROUND_HALF_UP);
        return PersonBorrowForProbationVO.builder().personId(person.getUuid()).username(NameUtils.getNameByEmployeeVO(person)).exceedDevice(borrowForProbationCollector.getExceedDevice()).exceedComponent(borrowForProbationCollector.getExceedComponent())
                .personalAmountPre(borrowForProbationCollector.getBorrowedAmount()).personalBorrowedDevicePre(borrowForProbationCollector.getDeviceCount()).personalBorrowedComponentPre(borrowForProbationCollector.getComponentCount())
                .personalAmountProportionPre(divide).deptBorrowingLimit(deptLimit).build();
    }

    private BorrowForProbationCollector dataCollector(List<CrmBorrowForProbationDevice> list) {
        if(CollectionUtils.isNotEmpty(list)){
            double borrowedAmount = list.stream().mapToDouble(m -> m.getQuotedPrice().doubleValue()).sum();
            // 在借
            long deviceCount = list.stream().filter(m -> ProductTypeEnum.ProductTypeProductEnum.isProduct(m.getProductType())
                    && ProductTypeEnum.BorrowForProbationStatusEnum.inBorrow(m.getBorrowStatus())).count();
            long componentCount = list.stream().filter(m -> !ProductTypeEnum.ProductTypeProductEnum.isProduct(m.getProductType())
                    && ProductTypeEnum.BorrowForProbationStatusEnum.inBorrow(m.getBorrowStatus())).count();
            // 逾期：超过预计归还时间，算作超期的设备不算在逾期中
            long overdueDevice = list.stream().filter(m -> ProductTypeEnum.ProductTypeProductEnum.isProduct(m.getProductType())
                    && ProductTypeEnum.BorrowForProbationStatusEnum.isOverdue(m.getBorrowStatus())).count();
            long overdueComponent = list.stream().filter(m -> !ProductTypeEnum.ProductTypeProductEnum.isProduct(m.getProductType())
                    && ProductTypeEnum.BorrowForProbationStatusEnum.isOverdue(m.getBorrowStatus())).count();
            // 超期：首次生效时间开始累计，满1年为超期
            long exceedDevice = list.stream().filter(m -> ProductTypeEnum.ProductTypeProductEnum.isProduct(m.getProductType())
                    && ProductTypeEnum.BorrowForProbationStatusEnum.isExceed(m.getBorrowStatus())).count();
            long exceedComponent = list.stream().filter(m -> !ProductTypeEnum.ProductTypeProductEnum.isProduct(m.getProductType())
                    && ProductTypeEnum.BorrowForProbationStatusEnum.isExceed(m.getBorrowStatus())).count();
            return BorrowForProbationCollector.builder().borrowedAmount(BigDecimal.valueOf(borrowedAmount)).deviceCount(deviceCount).componentCount(componentCount).exceedDevice(exceedDevice).exceedComponent(exceedComponent).overdueDevice(overdueDevice).overdueComponent(overdueComponent).build();
        }else{
            return BorrowForProbationCollector.builder().borrowedAmount(new BigDecimal(0)).deviceCount(0L).componentCount(0L).exceedDevice(0L).exceedComponent(0L).overdueDevice(0L).overdueComponent(0L).build();
        }
    }

    /**
     * @return
     * @Description: 定时刷新借试用设备状态
     * @author: leo
     * @date: 2024-07-15
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean refreshBorrowStatus() {
        List<CrmBorrowForProbationDevice> list = crmBorrowForProbationDeviceService.list(new LambdaQueryWrapper<CrmBorrowForProbationDevice>().in(CrmBorrowForProbationDevice::getBorrowStatus,
                ProductTypeEnum.BorrowForProbationStatusEnum.BORROW.getStatus(), ProductTypeEnum.BorrowForProbationStatusEnum.OVERDUE.getStatus()));
        if(CollectionUtils.isNotEmpty(list)){
            // 在借变逾期的主键ID
            List<String> overdueList = new ArrayList<>();
            list.forEach(m -> {
                // 预计归还日期
                LocalDate estimatedReturnTime = m.getEstimatedReturnTime();
                if(ProductTypeEnum.BorrowForProbationStatusEnum.BORROW.getStatus().equals(m.getBorrowStatus()) && LocalDate.now().isAfter(estimatedReturnTime)){
                    overdueList.add(m.getId());
                }
            });
            if(CollectionUtils.isNotEmpty(overdueList)){
                crmBorrowForProbationDeviceService.lambdaUpdate().set(CrmBorrowForProbationDevice::getBorrowStatus, ProductTypeEnum.BorrowForProbationStatusEnum.OVERDUE.getStatus()).in(CrmBorrowForProbationDevice::getId, overdueList).update();
            }
        }
        return true;
    }

    /**
     * 根据人员ID和物料代码集合查询借试用设备信息
     *
     * @param personId
     * @param stuffCodes
     * @return
     */
    @Override
    public List<CrmBorrowForProbationDevice> queryBorrowForProbationDeviceByPersonId(String personId, List<String> stuffCodes) {
        LambdaQueryWrapper<CrmBorrowForProbationDevice> queryWrapper = new LambdaQueryWrapper<CrmBorrowForProbationDevice>()
                .eq(CrmBorrowForProbationDevice::getPersonId, personId)
                .in(CollectionUtils.isNotEmpty(stuffCodes),CrmBorrowForProbationDevice::getStuffCode, stuffCodes)
                .in(CrmBorrowForProbationDevice::getBorrowStatus, ProductTypeEnum.BorrowForProbationStatusEnum.inBorrowStatus())
                .isNotNull(CrmBorrowForProbationDevice::getPsn)
                .orderByDesc(CrmBorrowForProbationDevice::getFirstBorrowTime);
        List<CrmBorrowForProbationDevice> list = crmBorrowForProbationDeviceService.list(queryWrapper);
        if(CollectionUtils.isNotEmpty(list)){
            Set<String> deviceIds = list.stream().map(CrmBorrowForProbationDevice::getId).collect(Collectors.toSet());
            JsonObject<Map<String, BorrowForProbationDeviceOccupyStateVO>> mapJsonObject = remoteFlowService.queryBorrowForProbationDeviceOccupyState(deviceIds);
            list.forEach(crmBorrowForProbationDevice -> {
                if(mapJsonObject.isSuccess()){
                    BorrowForProbationDeviceOccupyStateVO borrowForProbationDeviceOccupyStateVO = mapJsonObject.getObjEntity().get(crmBorrowForProbationDevice.getId());
                    if(null != borrowForProbationDeviceOccupyStateVO){
                        crmBorrowForProbationDevice.setOccupy(borrowForProbationDeviceOccupyStateVO.getState()==1);
                        crmBorrowForProbationDevice.setOccupyFlowName(borrowForProbationDeviceOccupyStateVO.getOccupyFlowName());
                        crmBorrowForProbationDevice.setOccupyProjectId(borrowForProbationDeviceOccupyStateVO.getProjectId());
                    }else{
                        crmBorrowForProbationDevice.setOccupy(false);
                    }
                }
            });
        }
        return list;
    }

    /**
     * 根据借试用产品ID集合查询借试用设备信息
     *
     * @param borrowProductIds
     * @return
     */
    @Override
    public List<CrmBorrowForProbationDevice> getBorrowForProbationDeviceByBorrowProductIds(List<String> borrowProductIds) {
        return crmBorrowForProbationDeviceService.list(new LambdaQueryWrapper<CrmBorrowForProbationDevice>().in(CrmBorrowForProbationDevice::getBorrowProductId, borrowProductIds));
    }

    /**
     * 查询用户交接借试用设备列表
     *
     * @param handoverProcessQuery
     * @return
     */
    @Override
    public List<HandoverTrialBorrowVo> queryUserTransferDeviceList(HandoverProcessQuery handoverProcessQuery) {
        String personId = handoverProcessQuery.getPersonId();
        if(StringUtils.isNotBlank(personId)){
            handoverProcessQuery.setPageSize(-1);
            IPage<HandoverTrialBorrowVo> handoverTrialBorrowVoIPage = baseMapper.selectBorrowForProbationDevicePageGroupByProcessNumber(new Query<>().getPage(handoverProcessQuery),handoverProcessQuery);
            return handoverTrialBorrowVoIPage.getRecords();
        }
        return List.of();
    }

    /**
     * 获取用户交接借试用设备列表
     *
     * @param handoverProcessQuery
     * @return
     */
    @Override
    public PageUtils<HandoverTrialBorrowVo> queryUserTransferDevicePage(HandoverProcessQuery handoverProcessQuery) {
        String personId = handoverProcessQuery.getPersonId();
        if(StringUtils.isNotBlank(personId)){
            IPage<HandoverTrialBorrowVo> handoverTrialBorrowVoIPage = baseMapper.selectBorrowForProbationDevicePageGroupByProcessNumber(new Query<>().getPage(handoverProcessQuery),handoverProcessQuery);
            return new PageUtils<>(handoverTrialBorrowVoIPage);
        }
        return new PageUtils<>();
    }

    /**
     * 执行用户交接借试用设备
     *
     * @param list
     * @return
     */
    @Override
    public Boolean executeUserTransferOfBorrowForProbationDevice(List<HandoverTrialBorrowVo> list) {
        if(CollectionUtils.isNotEmpty(list)){
            // 交接人
            String personId = list.get(0).getPersonId();
            List<String> processNumbers = list.stream().map(HandoverTrialBorrowVo::getProcessNumber).toList();
            // 交接借试用在借产品信息
            LambdaQueryWrapper<CrmBorrowForProbationDevice> deviceLambdaQueryWrapper = new LambdaQueryWrapper<CrmBorrowForProbationDevice>()
                    .eq(CrmBorrowForProbationDevice::getPersonId, personId)
                    .in(CrmBorrowForProbationDevice::getProcessNumber, processNumbers);
            List<CrmBorrowForProbationDevice> crmBorrowForProbationDevices = crmBorrowForProbationDeviceService.list(deviceLambdaQueryWrapper);
            if(CollectionUtils.isNotEmpty(crmBorrowForProbationDevices)){
                crmBorrowForProbationDevices.forEach(crmBorrowForProbationDevice -> {
                    list.stream().filter(handoverProjectVo -> handoverProjectVo.getProjectId().equals(crmBorrowForProbationDevice.getProjectId())
                            && handoverProjectVo.getPersonId().equals(crmBorrowForProbationDevice.getPersonId())).findFirst().ifPresent(handoverProjectVo -> {
                        crmBorrowForProbationDevice.setPersonId(handoverProjectVo.getReceiverId());
                    });

                });
                crmBorrowForProbationDeviceService.updateBatchById(crmBorrowForProbationDevices);
            }
        }
        return true;
    }
}
