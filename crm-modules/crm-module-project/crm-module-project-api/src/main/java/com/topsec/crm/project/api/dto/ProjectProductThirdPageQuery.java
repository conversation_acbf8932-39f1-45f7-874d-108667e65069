package com.topsec.crm.project.api.dto;

import com.topsec.crm.framework.common.web.page.CrmPageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name = "项目第三方产品分页查询参数")
public class ProjectProductThirdPageQuery extends CrmPageQuery {

    @Schema(description = "项目ID")
    private String projectId;

    @Schema(description = "供应商名称或物料代码")
    private String commonSearch;

    @Schema(description = "当前用户ID",hidden = true)
    private String currentPersonId;

    //业务字段
    private String processInstanceId;
}

