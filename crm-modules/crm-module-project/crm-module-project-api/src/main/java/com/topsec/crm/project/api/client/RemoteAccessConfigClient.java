package com.topsec.crm.project.api.client;

import com.topsec.crm.framework.common.constant.ServiceNameConstants;
import com.topsec.crm.project.api.entity.BriefInfo;
import com.topsec.crm.project.api.entity.CrmAccessProductInfoVo;
import com.topsec.crm.project.api.entity.CrmProjectProductAccessSwitchingVo;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

@FeignClient(contextId = "accessConfigClient", value = ServiceNameConstants.PROJECT_SERVICE,url = ServiceNameConstants.PROJECT_SERVICE_URL)
public interface RemoteAccessConfigClient {
    /**
     * 通路转换流程办结回写
     * @param projectProductAccessSwitchingVo
     * @return
     */
    @PostMapping("/accessRulesFeign/saveAccessSwitching")
    JsonObject<Boolean> saveAccessSwitching(@RequestBody CrmProjectProductAccessSwitchingVo projectProductAccessSwitchingVo);

    /**
     * 查询可转换通路产品
     * @param projectId
     * @param targerSwitching
     * @return
     */
    @GetMapping("/accessRulesFeign/getAccessProduct")
    JsonObject<List<CrmAccessProductInfoVo>> getAccessProduct(@RequestParam(name = "projectId") String projectId, @RequestParam(name = "targerSwitching") Integer targerSwitching);

    /**
     * 查询项目产品通路
     * @param projectNo
     * @param personId
     * @param saleAccessQuery
     * @param projectRecordIds
     * @return
     */
    @GetMapping("/accessRulesFeign/getAccessRulesByProjectQuery")
    JsonObject<Map<String,List<Integer>>> getAccessRulesByProjectQuery(@RequestParam("projectNo") String projectNo, @RequestParam("personId") String personId, @RequestParam List<Integer> saleAccessQuery, @RequestParam List<String> projectRecordIds);

    /**
     * 根据组名称获取产品信息
     * @param groupName 组名称
     * @return 产品ID列表
     */
    @GetMapping("/accessRulesFeign/getInfoByGroupName")
    JsonObject<List<String>> getInfoByGroupName(@RequestParam("groupName") String groupName);

}
