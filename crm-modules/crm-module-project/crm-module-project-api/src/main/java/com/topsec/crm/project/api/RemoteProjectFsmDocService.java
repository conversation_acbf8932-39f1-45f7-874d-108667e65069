package com.topsec.crm.project.api;

import com.topsec.crm.framework.common.constant.ServiceNameConstants;
import com.topsec.crm.project.api.entity.CrmProjectFsmDocVo;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(contextId = "RemoteProjectFsmDocService", value = ServiceNameConstants.PROJECT_SERVICE,url = ServiceNameConstants.PROJECT_SERVICE_URL)
public interface RemoteProjectFsmDocService {
    @PostMapping("/hidden/doc/add")
    JsonObject<Boolean> add(@RequestBody CrmProjectFsmDocVo crmProjectFsmDocVo);

    /**
     * 查询【项目-项目附件】列表
     */
    @PostMapping("/hidden/doc/list")
    JsonObject<CrmProjectFsmDocVo> list(@RequestBody CrmProjectFsmDocVo crmProjectFsmDocVo);
}

