package com.topsec.crm.project.api.client;

import com.topsec.crm.customer.api.entity.CrmCustomerVo;
import com.topsec.crm.framework.common.bean.HandoverProjectVo;
import com.topsec.crm.framework.common.bean.HandoverProcessQuery;
import com.topsec.crm.framework.common.bean.CrmProjectProductOwnServiceVo;
import com.topsec.crm.framework.common.bean.CrmProjectProductSnSelectVO;
import com.topsec.crm.framework.common.bean.StatsPersonTimeSearchVO;
import com.topsec.crm.framework.common.constant.ServiceNameConstants;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.project.api.dto.ProjectDirectlyPageQuery;
import com.topsec.crm.project.api.dto.ProjectDirectlyPersonIdPageQuery;
import com.topsec.crm.project.api.entity.*;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

@FeignClient(contextId = "remoteProjectDirectlyClient", value = ServiceNameConstants.PROJECT_SERVICE,url = ServiceNameConstants.PROJECT_SERVICE_URL)
public interface RemoteProjectDirectlyClient {

    @GetMapping(value = "/projectFeign/directly/hasRight")
    JsonObject<Boolean> hasRight(@RequestParam String projectId, @RequestParam String personId);

    @GetMapping(value = "/projectFeign/directly/{id}")
    JsonObject<CrmProjectDirectlyVo> getProjectInfo(@PathVariable String id);

    @PostMapping(value = "/projectFeign/directly/listByIds")
    JsonObject<List<CrmProjectDirectlyVo>> listByIds(@RequestBody List<String> projectIds);

    @PostMapping(value = "/projectFeign/directly/listSimpleDataByIds")
    JsonObject<List<CrmProjectDirectlyVo>> listSimpleDataByIds(@RequestBody List<String> projectIds);

    @GetMapping("/projectFeign/directly/getProjectDirectlyPriceStatistics")
    JsonObject<CrmProjectDirectlyPriceStatisticsVO> getProjectDirectlyPriceStatistics(@RequestParam String id);

    @PostMapping("/projectFeign/directly/getProjectDirectlyPriceStatisticsWithCompleteProduct")
    JsonObject<CrmProjectDirectlyPriceStatisticsVO> getProjectDirectlyPriceStatisticsWithCompleteProduct(@RequestBody CrmProjectDirectlyPriceStatisticsVO sourceData);

    @GetMapping("/projectFeign/checkUserRightOfDirectlyProject")
    JsonObject<Boolean> checkUserRightOfDirectlyProject(@RequestParam String personId, @RequestParam String projectId, @RequestParam Integer permissionCode);

    @PostMapping("/projectFeign/directly/getProjectPriceStatistics")
    JsonObject<CrmProjectDirectlyPriceStatisticsVO> getProjectPriceStatistics(@RequestBody CrmProjectProductVO projectProduct);

    @PostMapping("/projectFeign/directly/getContractPriceStatistics")
    JsonObject<CrmProjectDirectlyPriceStatisticsVO> getContractPriceStatistics(@RequestBody CrmContractProductVO contractProduct);

    @PostMapping("/projectFeign/directly/getSalesAgreementPriceStatistics")
    JsonObject<CrmProjectDirectlyPriceStatisticsVO> getSalesAgreementPriceStatistics(@RequestBody List<CrmProjectProductOwnVO> salesAgreementProduct, @RequestParam Boolean isSecurityIndustry);

    @PutMapping("/projectFeign/directly/updateBusinessType")
    JsonObject<Boolean> updateBusinessType(@RequestParam Integer businessType, @RequestParam String projectId);

    @GetMapping("/projectFeign/directly/tiledDataOfDirectlyProject")
    JsonObject<CrmProjectTiledProductsVO> tiledDataOfDirectlyProject(@RequestParam String projectId);

    @GetMapping("/projectFeign/directly/tiledProductOwnOfDirectlyProject")
    JsonObject<List<CrmProjectProductOwnVO>> tiledProductOwnOfDirectlyProject(@RequestParam String projectId);

    @PostMapping("/projectFeign/directly/tiledProductOwnOfDirectlyProjectByRecordIds")
    JsonObject<List<CrmProjectProductOwnVO>> tiledProductOwnOfDirectlyProjectByRecordIds(@RequestParam String projectId, @RequestBody List<String> productRecordIds);

    @GetMapping("/projectFeign/directly/tiledProductThirdOfDirectlyProject")
    JsonObject<List<CrmProjectProductThirdVo>> tiledProductThirdOfDirectlyProject(@RequestParam String projectId);

    @GetMapping("/projectFeign/directly/tiledProductOutsourcingOfDirectlyProject")
    JsonObject<List<CrmProjectOutsourcingServiceVo>> tiledProductOutsourcingOfDirectlyProject(@RequestParam String projectId);

    @GetMapping("/projectFeign/directly/subService/tiledProductSubServiceOfDirectlyProject")
    public JsonObject<List<CrmProjectProductOwnServiceVo>> tiledProductSubServiceOfDirectlyProject(@RequestParam String projectId);

    @PutMapping("/projectFeign/directly/updateProjectStatus")
    JsonObject<Boolean> updateProjectStatus(@RequestParam String projectId, @RequestParam Integer status);

    @GetMapping(value = "/projectFeign/directly/queryRecordIds")
    JsonObject<Set<String>> queryRecordIds(@RequestParam String projectId);

    @PostMapping(value = "/projectFeign/directly/pageOfPersonIdByParams")
    JsonObject<PageUtils<CrmProjectDirectlyVo>> pageOfPersonIdByParams(@RequestBody ProjectDirectlyPersonIdPageQuery projectDirectlyPersonIdPageQuery);

    @GetMapping(value = "/projectFeign/directly/refreshStrategicFieldOfProjectDirectly")
    JsonObject<Boolean> refreshStrategicFieldOfProjectDirectly();

    @GetMapping(value = "/projectFeign/directly/refreshInsuranceFieldOfProjectDirectly")
    JsonObject<Boolean> refreshInsuranceFieldOfProjectDirectly();

    @PostMapping(value = "/projectFeign/directly/queryProjectIds")
    JsonObject<Set<String>> queryProjectIds(@RequestBody ProjectDirectlyQuery projectDirectlyQuery);

    @PostMapping(value = "/projectFeign/directly/queryProjectSnByPersonId")
    JsonObject<List<CrmProjectProductSnSelectVO>> queryProjectSnByPersonId(@RequestBody List<String> stuffCodes, @RequestParam String personId, @RequestParam String projectId);

    @PostMapping(value = "/projectFeign/directly/queryProjectNoBySn")
    JsonObject<Map<String,CrmProjectDirectlyVo>> queryProjectNoBySn(@RequestBody List<String> sns);

    @GetMapping(value = "/projectFeign/directly/hasProjectRightByJwt")
    JsonObject<Boolean> hasProjectRightByJwt(@RequestParam String projectId);

    @GetMapping(value = "/projectFeign/directly/queryProjectDirectlyCommonDataByProjectNo")
    JsonObject<List<CrmProjectCommonDataVO>> queryProjectDirectlyCommonDataByProjectNo(@RequestParam String projectNo);

    @PostMapping(value = "/projectFeign/directly/directlyPageByParams")
    JsonObject<PageUtils<CrmProjectDirectlyVo>> directlyPageByParams(@RequestBody ProjectDirectlyPageQuery projectDirectlyPageQuery);

    @PostMapping(value = "/projectFeign/directly/statisticUserProject")
    JsonObject<Long> statisticUserProject(@RequestBody StatsPersonTimeSearchVO searchVO);

    @PostMapping(value = "/projectFeign/directly/updateWinningWebsite")
    JsonObject<Boolean> updateWinningWebsite(@RequestParam String projectId, @RequestParam String winningWebsite);

    @PostMapping(value = "/projectFeign/directly/queryProjectIdsByParam")
    JsonObject<Set<String>> queryProjectIdsByParam(@RequestBody ProjectIdQueryParam projectIdQueryParam);

    @PostMapping(value = "/projectFeign/directly/queryUserTransferProjectList")
    JsonObject<List<HandoverProjectVo>> queryUserTransferProjectList(@RequestBody HandoverProcessQuery handoverProcessQuery);

    @PostMapping(value = "/projectFeign/directly/queryUserTransferProjectPage")
    JsonObject<PageUtils<HandoverProjectVo>> queryUserTransferProjectPage(@RequestBody HandoverProcessQuery handoverProcessQuery);

    @PostMapping(value = "/projectFeign/directly/executeUserTransferOfDirectlyProject")
    JsonObject<Boolean> executeUserTransferOfDirectlyProject(@RequestBody List<HandoverProjectVo> list);

    @GetMapping(value = "/projectFeign/directly/getDirectlyProjectAndUnfinishedProduct")
    JsonObject<List<CrmProjectDirectlyVo>> getDirectlyProjectAndUnfinishedProduct(@RequestParam String personId);

    @GetMapping(value = "/projectFeign/directly/selectCustomerInfoForDirectlyProject")
    JsonObject<CrmCustomerVo> selectCustomerInfoForDirectlyProject(@RequestParam("customerId") String customerId, @RequestParam("projectId") String projectId);

}
