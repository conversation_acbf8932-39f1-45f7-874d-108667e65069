package com.topsec.crm.project.api.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.topsec.crm.framework.common.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 项目-项目附件对象 crm_project_fsm_doc
 *
 * @date 2024-04-25
 */
@Data
public class CrmProjectFsmDocVo {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @Schema(description = "ID")
    private String id;
    /**
     * 项目ID
     */
    @Schema(description = "项目ID")
    private String projectId;
    /**
     * 文件类型
     */
    @Schema(description = "文件类型")
    private String type;
    /**
     * 文件docId
     */
    @Schema(description = "文件docId")
    private String fsmDocId;
    /**
     * 文件名称
     */
    @Schema(description = "文件名称")
    private String docName;
    /**
     * 文件拓展名
     */
    @Schema(description = "文件拓展名")
    private String docType;
    /**
     * 文件说明
     */
    @Schema(description = "文件说明")
    private String description;
    /**
     * 标记删除 0-未删除 1-已删除
     */
    @Schema(description = "标记删除 0-未删除 1-已删除")
    private Integer delFlag;
    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createUser;
    /**
     * 更新人
     */
    @Schema(description = "更新人")
    private String updateUser;

    /**
     * 是否是项目失败文件（0：否，1：是）
     */
    @Schema(description = "是否是项目失败文件（0：否，1：是）")
    private Integer failDoc;
    /**
     * 合同文件 式
     */
    @Schema(description = "合同文件 式")
    private Integer contractKind;
    /**
     * 合同文件 份
     */
    @Schema(description = "合同文件 份")
    private Integer contractCopies;

    @Schema(description = "文件大小")
    private String docSize;

    //业务字段
    @Schema(description = "创建人姓名")
    private String createUserName;
    /** 创建时间 */
    @Schema(description = "创建时间")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime createTime;
    //业务字段
    private String processInstanceId;
}
