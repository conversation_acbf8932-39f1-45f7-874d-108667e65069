package com.topsec.crm.project.api.dto;

import com.topsec.crm.framework.common.web.page.CrmPageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(name = "项目自有产品分页查询参数")
public class ProjectProductOwnPageQuery extends CrmPageQuery {

    @Schema(description = "项目ID")
    private String projectId;

    @Schema(description = "产品名称或物料代码")
    private String commonSearch;

    @Schema(description = "当前用户ID",hidden = true)
    private String currentPersonId;

    @Schema(description = "上报或合同状态 0-待上报或待发起 1-上报中或合同审批中 2-已上报或合同已完成")
    private List<Integer> reportStatus;

    @Schema(description = "销售通路（公司项目专用）")
    private List<Integer> saleAccess;

    @Schema(description = "设备类型（获取项目产品展开数据时有效）")
    private List<String> productType;

    @Schema(description = "是否是流程发起页面调用",hidden = true)
    private boolean launchedPageCall;

    //业务字段
    private String processInstanceId;
}
