package com.topsec.crm.operation.api;

import com.topsec.crm.framework.common.bean.HandoverProcessQuery;
import com.topsec.crm.framework.common.bean.HandoverSupplierVo;
import com.topsec.crm.framework.common.constant.ServiceNameConstants;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.operation.api.entity.SupplierVO;
import com.topsec.tbscommon.JsonObject;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 供应商
 */
@FeignClient(contextId = "remoteSupplierService", value = ServiceNameConstants.OPERATION_SERVICE, url = ServiceNameConstants.OPERATION_SERVICE_URL)
public interface RemoteSupplierService {

    @GetMapping("/dict/supplierHidden/getSupplierList")
    JsonObject<List<SupplierVO>> getSupplierList(@RequestParam List<String> ids);

    @PostMapping("/dict/supplierHidden/queryByHandoverProcessQuery")
    JsonObject<List<HandoverSupplierVo>> queryByHandoverProcessQuery(@RequestBody HandoverProcessQuery query);

    @PostMapping("/dict/supplierHidden/pageByHandoverProcessQuery")
    JsonObject<PageUtils<HandoverSupplierVo>> pageByHandoverProcessQuery(@RequestBody HandoverProcessQuery query);

    @PostMapping("/dict/supplierHidden/updateSupplierCorporateContact")
    JsonObject<Boolean> updateSupplierCorporateContact(@RequestBody List<HandoverSupplierVo> handoverSupplierVos);

}
