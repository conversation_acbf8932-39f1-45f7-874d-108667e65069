package com.topsec.crm.operation.core.service;

import com.alibaba.fastjson.JSON;
import com.topsec.crm.framework.common.bean.HandoverSupplierVo;
import com.topsec.crm.framework.common.bean.HandoverProcessQuery;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.operation.api.entity.SupplierContactsVO;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * @date 2023/10/17 14:28
 */
@ActiveProfiles({"dev", "feignDev", "redisDev", "configDev"})
@SpringBootTest
public class CrmSupplierContactsServiceTest {

    @Autowired
    private CrmSupplierContactsRelService crmSupplierContactsRelService;
    
    @Test
    public void saveOrUpdate(){
        SupplierContactsVO supplierContactsVO = new SupplierContactsVO();
        supplierContactsVO.setSupplierId("fa539d78-7d14-4d37-a5b6-c17ff6567e87");
        supplierContactsVO.setId("d41bc9c595094a3b952355222f2a6319");
        supplierContactsVO.setJob("测试");
        supplierContactsVO.setDept("测试部门");
        supplierContactsVO.setRemark("供应商联系人测试");
        supplierContactsVO.setName("test修改");
        crmSupplierContactsRelService.saveOrUpdate(supplierContactsVO);
    }

    @Test
    public void pageSupplierContacts(){
        SupplierContactsVO supplierContactsVO = new SupplierContactsVO();
        supplierContactsVO.setSupplierId("fa539d78-7d14-4d37-a5b6-c17ff6567e87");

        TableDataInfo tableDataInfo = crmSupplierContactsRelService.pageSupplierContacts(supplierContactsVO);
        System.out.println(JSON.toJSONString(tableDataInfo, true));
    }

    @Test
    public void delete(){
        crmSupplierContactsRelService.delete("cfb99c50c8dcc9a306e3fa05adc1b500", "fa539d78-7d14-4d37-a5b6-c17ff6567e87");

    }


    @Resource
    private CrmSupplierService crmSupplierService;

    @Test
    public void queryByHandoverProcessQuery(){
        HandoverProcessQuery query = new HandoverProcessQuery();
        query.setPersonId("406f395778d843ba824c5b3044de2021");
        List<HandoverSupplierVo> handoverSupplierVos = crmSupplierService.queryByHandoverProcessQuery(query);
        System.out.println(JSON.toJSONString(handoverSupplierVos, true));
    }

    @Test
    public void updateSupplierCorporateContact(){
        HandoverProcessQuery query = new HandoverProcessQuery();
        query.setPersonId("123");
        List<HandoverSupplierVo> handoverSupplierVos = crmSupplierService.queryByHandoverProcessQuery(query);
        handoverSupplierVos.forEach(handoverSupplierVo -> {
            // 406f395778d843ba824c5b3044de2021
            handoverSupplierVo.setReceiverId("406f395778d843ba824c5b3044de2021");
            // 杨梓楠12224
            handoverSupplierVo.setReceiverName("杨梓楠12224");
        });
        crmSupplierService.updateSupplierCorporateContact(handoverSupplierVos);
    }
}
