package com.topsec.crm.operation.core.service;

import com.alibaba.fastjson.JSONObject;
import com.topsec.crm.framework.common.bean.HandoverSupplierVo;
import com.topsec.crm.framework.common.bean.HandoverProcessQuery;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.operation.api.entity.SupplierVO;
import com.topsec.crm.operation.core.entity.CrmSupplier;
import com.baomidou.mybatisplus.extension.service.IService;
import com.topsec.tbscommon.JsonObject;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-22
 */
public interface CrmSupplierService extends IService<CrmSupplier> {

    /**
     * 查询列表
     *
     * @param supplierVO
     * @return
     */
    TableDataInfo listSupplierVO(SupplierVO supplierVO);

    /**
     * 新增或修改供应商
     *
     * @param supplierVO
     * @return
     */
    Boolean saveOrUpdate(SupplierVO supplierVO);


    /**
     * 删除供应商 逻辑删除
     *
     * @param id 供应商id
     * @return
     */
    Boolean deleteSupplier(String id);

    /**
     * 根据供应商ID 获取工商信息
     * @param supplierId 供应商ID
     */
    JsonObject<JSONObject> getEnterpriseInfo(String supplierId);


    /**
     * 根据供应商ID 更新工商信息
     * @param supplierId 供应商ID
     */
    Boolean updateEnterpriseInfo(String supplierId);

    /**
     * 根据供应商ID查询供应商信息
     * @param supplierId
     * @return
     */
    SupplierVO selectCrmSupplierById(String supplierId);

    /**
     * 根据供应商ID集合查询多个供应商信息
     * @param supplierId
     * @return
     */
    List<SupplierVO> selectCrmSupplierByIds(List<String> supplierId);

    /**
     * 根据供应商联系人ID集合查询供应商信息
     * @param corporateIds 联络人id
     * @return 供应商
     */
    List<SupplierVO> getSupplierByContactIds(List<String> corporateIds);

    /**
     * 根据供应商名称查询供应商信息
     * @param supplierName
     * @return
     */
    SupplierVO selectCrmSupplierByName(String supplierName);

    /**
     * 根据人员交接查询条件查询供应商列表
     * @param query
     * @return
     */
    List<HandoverSupplierVo> queryByHandoverProcessQuery(HandoverProcessQuery query);

    /**
     * 根据人员交接查询条件查询供应商列表(分页)
     * @param query
     * @return
     */
    PageUtils<HandoverSupplierVo> pageByHandoverProcessQuery(HandoverProcessQuery query);

    /**
     * 更新供应商联系人
     * @param handoverSupplierVos vo
     * @return
     */
    Boolean updateSupplierCorporateContact(List<HandoverSupplierVo> handoverSupplierVos);


}
