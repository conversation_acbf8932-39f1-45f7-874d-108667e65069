package com.topsec.crm.operation.core.controllerhidden;

import com.topsec.crm.framework.common.bean.HandoverSupplierVo;
import com.topsec.crm.framework.common.bean.HandoverProcessQuery;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.operation.api.entity.SupplierVO;
import com.topsec.crm.operation.core.service.CrmSupplierService;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 供应商
 */
@RestController
@RequestMapping("/dict/supplierHidden")
@Tag(name = "供应商联系人", description = "/dict/supplierHidden")
public class CrmSupplierHiddenController {

    @Resource
    private CrmSupplierService crmSupplierService;

    @GetMapping("/getSupplierList")
    @Operation(summary = "根据供应商id集合查询供应商")
    public JsonObject<List<SupplierVO>> getSupplierList(@RequestParam List<String> ids) {
        return new JsonObject<>(crmSupplierService.selectCrmSupplierByIds(ids));
    }

    @PostMapping("/queryByHandoverProcessQuery")
    @Operation(summary = "根据人员交接查询条件查询供应商列表(不分页)", description = "人员交接供应商搜索")
    public JsonObject<List<HandoverSupplierVo>> queryByHandoverProcessQuery(@RequestBody HandoverProcessQuery query) {
        return new JsonObject<>(crmSupplierService.queryByHandoverProcessQuery(query));
    }

    @PostMapping("/pageByHandoverProcessQuery")
    @Operation(summary = "根据人员交接查询条件查询供应商列表(分页)", description = "人员交接供应商搜索")
    public JsonObject<PageUtils<HandoverSupplierVo>> pageByHandoverProcessQuery(@RequestBody HandoverProcessQuery query) {
        return new JsonObject<>(crmSupplierService.pageByHandoverProcessQuery(query));
    }

    @PostMapping("/updateSupplierCorporateContact")
    @Operation(summary = "更新供应商联系人", description = "更新供应商联系人")
    public JsonObject<Boolean> updateSupplierCorporateContact(@RequestBody List<HandoverSupplierVo> handoverSupplierVos) {
        return new JsonObject<>(crmSupplierService.updateSupplierCorporateContact(handoverSupplierVos));
    }

}
