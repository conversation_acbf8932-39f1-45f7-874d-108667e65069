package com.topsec.crm.operation.core.service.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.topsec.crm.framework.common.bean.HandoverSupplierVo;
import com.topsec.crm.framework.common.bean.HandoverProcessQuery;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.name.NameUtils;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.operation.api.dto.SupplierContactsQuery;
import com.topsec.crm.operation.api.entity.ContactsVO;
import com.topsec.crm.operation.api.entity.SupplierContactsVO;
import com.topsec.crm.operation.api.entity.SupplierVO;
import com.topsec.crm.operation.core.entity.CrmContacts;
import com.topsec.crm.operation.core.entity.CrmSupplier;
import com.topsec.crm.operation.core.entity.CrmSupplierContactsRel;
import com.topsec.crm.operation.core.mapper.CrmSupplierMapper;
import com.topsec.crm.operation.core.service.CrmContactsService;
import com.topsec.crm.operation.core.service.CrmSupplierContactsRelService;
import com.topsec.crm.operation.core.service.CrmSupplierProductService;
import com.topsec.crm.operation.core.service.CrmSupplierService;
import com.topsec.crm.operation.core.util.CorporateTypeHandler;
import com.topsec.crm.tyc.api.RemoteTycSelectService;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-22
 */
@Service
public class CrmSupplierServiceImpl extends ServiceImpl<CrmSupplierMapper, CrmSupplier> implements CrmSupplierService {

    @Resource
    private RemoteTycSelectService remoteTycSelectService;

    @Resource
    private CrmContactsService contactsService;

    @Resource
    private CrmSupplierContactsRelService crmSupplierContactsRelService;

    @Override
    public TableDataInfo listSupplierVO(SupplierVO supplierVO) {
        List<CrmSupplier> list = baseMapper.selectList(new QueryWrapper<CrmSupplier>().lambda()
                .like(StringUtils.isNotEmpty(supplierVO.getSupplierName()), CrmSupplier::getSupplierName, supplierVO.getSupplierName())
                .eq(CrmSupplier::getDelFlag, false)
                .orderByDesc(CrmSupplier::getUpdateTime));
        List<SupplierVO> result = HyperBeanUtils.copyListProperties(list, SupplierVO::new);
        NameUtils.setName(result);

        TableDataInfo tableDataInfo = new TableDataInfo();
        tableDataInfo.setList(result);
        tableDataInfo.setTotalCount(new PageInfo<>(list).getTotal());
        return tableDataInfo;
    }

    @Override
    public Boolean saveOrUpdate(SupplierVO supplierVO) {
        List<CrmSupplier> list = lambdaQuery().eq(CrmSupplier::getDelFlag, false)
                .eq(CrmSupplier::getSupplierName, supplierVO.getSupplierName())
                .ne(StringUtils.isNotEmpty(supplierVO.getId()), CrmSupplier::getId, supplierVO.getId()).list();
        if (!CollectionUtils.isEmpty(list)){
            throw new CrmException("供应商已存在");
        }
        CrmSupplier supplier = HyperBeanUtils.copyProperties(supplierVO, CrmSupplier::new);
        return saveOrUpdate(supplier);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteSupplier(String id) {
        CrmSupplier crmSupplier = getById(id);
        if (crmSupplier == null) {
            // 未查询到记录，交由统一异常处理
            throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
        }
        // 逻辑删除
        crmSupplier.setDelFlag(true);
        updateById(crmSupplier);
        // 删除产品
        CrmSupplierProductService productService = SpringUtil.getBean(CrmSupplierProductService.class);
        productService.deleteSupplierProductBySupplierId(id);
        return true;
    }

    @Override
    public JsonObject<JSONObject> getEnterpriseInfo(String supperlierId){
        String supplierName = getById(supperlierId).getSupplierName();
        JsonObject<JSONObject> info = remoteTycSelectService.getBusinessInfoByName(supplierName);
        if (info.getMessage().equals("success")) {
            return info;
        }
        return null;
    }

    @Override
    public Boolean updateEnterpriseInfo(String supplierId) {
        CrmSupplier crmSupplier = getById(supplierId);
        if (crmSupplier == null) {
            throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
        }
        // TODO 天眼查更新工商信息
        return true;
    }

    @Override
    public SupplierVO selectCrmSupplierById(String supplierId) {
        CrmSupplier crmSupplier = getOne(new QueryWrapper<CrmSupplier>().lambda().eq(CrmSupplier::getDelFlag,0).eq(CrmSupplier::getId,supplierId));
        return HyperBeanUtils.copyProperties(crmSupplier,SupplierVO::new);
    }

    @Override
    public List<SupplierVO> selectCrmSupplierByIds(List<String> supplierId) {
        List<CrmSupplier>  crmSuppliers= list(new QueryWrapper<CrmSupplier>().lambda().in(CrmSupplier::getId,supplierId).eq(CrmSupplier::getDelFlag,0));
        return HyperBeanUtils.copyListProperties(crmSuppliers,SupplierVO::new);
    }

    @Override
    public List<SupplierVO> getSupplierByContactIds(List<String> corporateIds) {
        if (CollectionUtils.isEmpty(corporateIds)) {
            return null;
        }
        List<CrmContacts> contracts = new ArrayList<>();
        // 先查联系人的联络人
        corporateIds.forEach(corporateId -> {
            contracts.addAll(contactsService.list(new QueryWrapper<CrmContacts>().
                    like("corporate_contact", corporateId)
                    .eq("del_flag", 0)));
        });
        if(CollectionUtils.isEmpty(contracts)){
            return new ArrayList<>();
        }
        // 查关联表中有那些供应商id有这个联络人
        List<CrmSupplierContactsRel> list = crmSupplierContactsRelService.list(new LambdaQueryWrapper<CrmSupplierContactsRel>()
                .in(CrmSupplierContactsRel::getContactsId, ListUtils.emptyIfNull(contracts).stream().map(CrmContacts::getId).collect(Collectors.toList())));

        // 返回对应的供应商
        return HyperBeanUtils.copyListProperties(list(new LambdaQueryWrapper<CrmSupplier>().in(CrmSupplier::getId, list.stream().map(CrmSupplierContactsRel::getSupplierId).collect(Collectors.toList()))
                .eq(CrmSupplier::getDelFlag, false)), SupplierVO::new);
    }

    @Override
    public SupplierVO selectCrmSupplierByName(String supplierName) {
        CrmSupplier crmSupplier = getOne(new QueryWrapper<CrmSupplier>().lambda().eq(CrmSupplier::getDelFlag,0).eq(CrmSupplier::getSupplierName,supplierName));
        return HyperBeanUtils.copyProperties(crmSupplier,SupplierVO::new);
    }

    @Override
    public List<HandoverSupplierVo> queryByHandoverProcessQuery(HandoverProcessQuery query) {
        return baseMapper.queryByHandoverProcessQuery(query);
    }

    @Override
    public PageUtils<HandoverSupplierVo> pageByHandoverProcessQuery(HandoverProcessQuery query) {
        // 启动分页
        PageHelper.startPage(query.getPageNum(), query.getPageSize());

        // 查询数据
        List<HandoverSupplierVo> list = baseMapper.queryByHandoverProcessQuery(query);

        // 使用项目的PageUtils工具类
        return PageUtils.paginate(list, query.getPageSize(), query.getPageNum());
    }

    @Override
    public Boolean updateSupplierCorporateContact(List<HandoverSupplierVo> handoverSupplierVos) {
        if (CollectionUtils.isEmpty(handoverSupplierVos)) {
            return true;
        }
        Set<String> supplierIds = handoverSupplierVos.stream().map(HandoverSupplierVo::getBusinessId).collect(Collectors.toSet());
        SupplierContactsQuery query = new SupplierContactsQuery();
        query.setSupplierIds(new ArrayList<>(supplierIds));
        List<SupplierContactsVO> supplierContacts = crmSupplierContactsRelService.getSupplierContacts(query);
        if (CollectionUtils.isEmpty(supplierContacts)) {
            return true;
        }
        Map<String, List<SupplierContactsVO>> contractsBySupplierId = supplierContacts.stream().collect(Collectors.groupingBy(SupplierContactsVO::getSupplierId));
        // 循环update
        handoverSupplierVos.forEach(handoverSupplierVo -> {
            String supplierId = handoverSupplierVo.getBusinessId();
            String receiverId = handoverSupplierVo.getReceiverId();
            String personId = handoverSupplierVo.getPersonId();
            String receiverName = handoverSupplierVo.getReceiverName();
            // 这个供应商对应的联系人
            List<SupplierContactsVO> supplierContactsVOS = contractsBySupplierId.get(supplierId);
            if (CollectionUtils.isEmpty(supplierContactsVOS)) {
                return;
            }
            supplierContactsVOS.forEach(contactsVO -> {
                List<ContactsVO.CorporateContact> corporateContact = contactsVO.getCorporateContact();
                if (CollectionUtils.isEmpty(corporateContact)) {
                    return;
                }
                boolean isChange = false;
                for(ContactsVO.CorporateContact item : corporateContact) {
                    if (Objects.equals(personId, item.getId())) {
                        item.setId(receiverId);
                        item.setName(receiverName);
                        isChange = true;
                    }
                }
                if (isChange) {
                    String json = JSON.toJSONString(corporateContact);
                    contactsService.update(new LambdaUpdateWrapper<CrmContacts>()
                            .set(CrmContacts::getCorporateContact, json)
                            .eq(CrmContacts::getId, contactsVO.getId()));
                    String id = contactsVO.getId();
                    crmSupplierContactsRelService.update(new LambdaUpdateWrapper<CrmSupplierContactsRel>()
                            .set(CrmSupplierContactsRel::getCreateUser, receiverId)
                            .eq(CrmSupplierContactsRel::getContactsId, id)
                            .eq(CrmSupplierContactsRel::getSupplierId, supplierId));
                }
            });
        });
        return true;
    }


}
