<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.operation.core.mapper.CrmSupplierContactsRelMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.operation.core.entity.CrmContacts">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="gender" column="gender" jdbcType="INTEGER"/>
        <result property="birthday" column="birthday" jdbcType="VARCHAR"/>
        <result property="mobile" column="mobile" jdbcType="VARCHAR"/>
        <result property="dept" column="dept" jdbcType="VARCHAR"/>
        <result property="job" column="job" jdbcType="VARCHAR"/>
        <result property="qq" column="qq" jdbcType="VARCHAR"/>
        <result property="email" column="email" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="corporateContact" column="corporate_contact" jdbcType="OTHER" typeHandler="com.topsec.crm.operation.core.util.CorporateTypeHandler"/>
        <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
        <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP" javaType="java.time.LocalDateTime"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP" javaType="java.time.LocalDateTime"/>
    </resultMap>

    <resultMap id="ListResultMap" type="com.topsec.crm.operation.core.entity.CrmContacts" extends="BaseResultMap">
        <result property="delFlag" column="del_flag" jdbcType="BOOLEAN"/>
    </resultMap>

    <resultMap id="BatchResultMap" type="com.topsec.crm.operation.api.entity.SupplierContactsVO" extends="BaseResultMap">
    </resultMap>

    <sql id="supplierIdsInClause">
        <foreach collection="supplierIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </sql>

    <select id="listContactsBySupplierId" resultMap="ListResultMap"
            parameterType="com.topsec.crm.operation.api.entity.SupplierContactsVO">
        select crm_contacts.* from
        (crm_supplier left join crm_supplier_contacts_rel on crm_supplier.id=crm_supplier_contacts_rel.supplier_id)
        left join crm_contacts on crm_supplier_contacts_rel.contacts_id=crm_contacts.id
        where crm_contacts.del_flag=0 and crm_supplier.del_flag=0 and crm_supplier_contacts_rel.create_user= #{createUser}
        <if test="supplierId !=null and supplierId != ''">
            and crm_supplier.id = #{supplierId}
        </if>
        <if test="name !=null and name != '' ">
            and crm_contacts.name like concat('%', #{name}, '%')
        </if>
        order by crm_contacts.update_time desc

    </select>
    <select id="getSupplierContacts"  resultMap="BatchResultMap" parameterType="com.topsec.crm.operation.api.dto.SupplierContactsQuery">
        select crm_supplier.id as supplier_id,crm_contacts.* from
        (crm_supplier left join crm_supplier_contacts_rel on crm_supplier.id=crm_supplier_contacts_rel.supplier_id)
        left join crm_contacts on crm_supplier_contacts_rel.contacts_id=crm_contacts.id
        where crm_contacts.del_flag=0 and crm_supplier.del_flag=0
          <if test="createUser != null and createUser != ''">
            and crm_supplier_contacts_rel.create_user= #{createUser}
          </if>
        and crm_supplier.id in <include refid="supplierIdsInClause"/>
        order by crm_contacts.update_time desc
    </select>
</mapper>
