package com.topsec.crm.flow.api.dto.loan.loanApply;


import com.fasterxml.jackson.annotation.JsonIgnore;
import com.topsec.crm.flow.api.vo.ProcessFileInfoVO;
import com.topsec.crm.framework.common.annotation.Excel;
import com.topsec.crm.framework.common.bean.FileInput;
import com.topsec.crm.framework.common.bean.FlowPerson;
import com.topsec.vo.node.ApproveNode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@Data
public class LoanApplyDTO {


    @Schema(description = "借款ID")
    private String id;

    @Schema(description = "流程实例ID")
    private String processInstanceId;

    @Schema(description = "上级流程实例ID")
    private String parentProcessInstanceId;

    @Schema(description = "上级流程实例单号")
    private String parentProcessNumber;

    @Schema(description = "流程分类")
    private String processDefinitionKey;

    @Schema(description = "流程编号")
    @Excel(name = "借款单号",sort = 1)
    private String processNumber;

    @Schema(description = "借款用户ID")
    public String loanUser;

    @Schema(description = "借款用户名称")
    @Excel(name = "借款人",sort = 2)
    public String loanUserName;

    @Schema(description = "所属部门ID")
    private String loanDeptId;

    @Schema(description = "所属部门名称")
    @Excel(name = "所属部门",sort = 3)
    private String loanDeptName;

    @Schema(description = "核算部门ID")
    private String checkDeptId;

    @Schema(description = "核算部门名称")
    @Excel(name = "核算部门",sort = 4)
    private String checkDeptName;

    @Schema(description = "申请时间")
    @Excel(name = "申请时间",sort = 5, localDateTimeFormat = "yyyy-MM-dd")
    private LocalDateTime createTime;

    @Schema(description = "签订公司ID")
    private String signCompanyId;

    @Schema(description = "签订公司名称")
    @Excel(name = "签订公司",sort = 8)
    private String signCompanyName;

    @Schema(description = "借款分类 1-投标保证金 2-保函 3-履约保证金 4-标书费 5-中标服务费 6-采购借款 7-费用")
    @Excel(name = "借款分类",sort = 6,readConverterExp = "1=投标保证金,2=保函,3=履约保证金,4=标书费,5=中标服务费,6=采购借款,7=费用")
    private Integer loanType;

    @Schema(description = "借款金额")
    @Excel(name = "借款金额",sort = 7, moneyFormat = "￥#,##0.00")
    private BigDecimal loanAmount;

    @Schema(description = "付款方式 1-支票 2-现金 3-电汇 4-其他")
    @Excel(name = "付款方式",sort = 9, readConverterExp = "1=支票,2=现金,3=电汇,4=其他")
    private Integer paymentType;

    @Schema(description = "付款对象类型 1-个人 2-招标公司 3-用户")
    @Excel(name = "付款对象类型",sort = 10, readConverterExp = "1=个人,2=招标公司,3=用户")
    private Integer payerType;

    @Schema(description = "付款对象名称")
    @Excel(name = "付款对象名称",sort = 11)
    private String payerName;

    @Schema(description = "联系人")
    @Excel(name = "联系人",sort = 12)
    private String linkman;

    @Schema(description = "联系电话")
    @Excel(name = "联系电话",sort = 13)
    private String linkPhone;

    @Schema(description = "开户银行名称")
    @Excel(name = "开户银行",sort = 14)
    private String bankName;

    @Schema(description = "银行账号")
    @Excel(name = "开户账号",sort = 15)
    private String bankAccount;

    @Schema(description = "预计归还日期")
    @Excel(name = "预计归还日期",sort = 16, localDateFormat = "yyyy-MM-dd")
    private LocalDateTime repaymentDueDate;

    @Schema(description = "付款日期")
    private LocalDateTime repaymentDate;

    @Schema(description = "项目编号/采购编号")
    private String businessNo;

    @Schema(description = "业务ID")
    private String businessId;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "标的金额")
    private BigDecimal subjectAmount;

    @Schema(description = "比例，借款利率")
    private BigDecimal loanRate;

    @Schema(description = "申请原因")
    @Excel(name = "申请原因",sort = 17)
    private String applyReason;

    @Schema(description = "备注")
    private String remarks;

    @Schema(description = "审批进度")
    private Set<ApproveNode> approveNodes;

    @Schema(description = "流程进度-导出使用")
    @Excel(name = "流程进度",sort = 18)
    private String progress;

    @Schema(description = "是否是借款承诺 0-否 1-是")
    private Boolean isLoanPromise;

    @Schema(description = "转费用金额")
    @Excel(name = "转费用金额",sort = 7, moneyFormat = "￥#,##0.00")
    private BigDecimal toCostAmount;

    @Schema(description = "转费用类型 3-履约保证金 5-中标服务费")
    private Integer toCostType;

    @Schema(description = "申请依据")
    private List<ProcessFileInfoVO> applyBasisFiles;

    @Schema(description = "借款单及付款凭据")
    private List<ProcessFileInfoVO> loanProofFiles;

    @Schema(description = "其他附件")
    private List<ProcessFileInfoVO> otherFiles;

    @Schema(description = "借款出纳accountId")
    private FlowPerson loanTeller;

    @JsonIgnore
    private String loanTellerPersonId;

    @Schema(description = "未核销金额")
    BigDecimal unVerifiedAmount;

}
