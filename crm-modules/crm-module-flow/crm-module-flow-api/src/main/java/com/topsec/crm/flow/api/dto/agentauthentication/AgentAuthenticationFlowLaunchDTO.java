package com.topsec.crm.flow.api.dto.agentauthentication;

import com.topsec.crm.agent.api.entity.CrmAgentContactsVo;
import com.topsec.crm.flow.api.dto.InputLaunchable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class AgentAuthenticationFlowLaunchDTO extends InputLaunchable {
    @Schema(description = "主键")
    private String id;
    /**
     *流程实例ID
     */
    @Schema(description = "流程实例ID")
    private String processInstanceId;
    /**
     *流程状态
     */
    @Schema(description = "流程状态")
    private Integer processState;
    /**
     *流程编号
     */
    @Schema(description = "流程编号")
    private String processNumber;
    /**
     *渠道信息id
     */
    @Schema(description = "渠道信息id")
    @NotEmpty
    private String agentId;
    /**
     *渠道名称
     */
    @Schema(description = "渠道名称")
    @NotEmpty
    private String agentName;
    /**
     *渠道分类id
     */
    @Schema(description = "申请渠道分类id")
    @NotNull
    private Integer agentClassification;

    @Schema(description = "申请渠道分类名称")
    private String agentClassificationName;

    @Schema(description = "授信级别")
    private String creditLevel;
    /**
     *重点拓展方向
     */
    @Schema(description = "重点拓展方向")
    private String keyExpandDirection;
    /**
     *代理产品范围
     */
    @Schema(description = "代理产品范围")
    @NotEmpty
    private String agentProductRange;
    /**
     *是否申请央采 1：是；0：否
     */
    @Schema(description = "是否申请央采 1：是；0：否")
    private Integer applyCentralProcurement;
    /**
     *预付款金额
     */
    @Schema(description = "预付款金额")
    private BigDecimal advancePayment;
    /**
     *任务金额
     */
    @Schema(description = "任务金额")
    @NotNull
    private BigDecimal taskAmount;
    /**
     *欠款金额,超期应收
     */
    @Schema(description = "欠款金额,超期应收")
    private AgentArrearsAmountVO agentArrearsAmountVO;
    /**
     *TSPA（售前初级）人员数量
     */
    @Schema(description = "TSPA（售前初级）人员数量")
    private Integer tspa;
    /**
     *TSPP（售前中级）人员数量
     */
    @Schema(description = "TSPP（售前中级）人员数量")
    private Integer tspp;
    /**
     * TSCP人数
     */
    @Schema(description = "TSCP人数")
    private Integer tscp;
    /**
     * TSCA人数
     */
    @Schema(description = "TSCA人数")
    private Integer tsca;
    /**
     *总代id
     */
    @Schema(description = "总代id")
    private String generalAgentId;
    /**
     *总代名称
     */
    @Schema(description = "总代名称")
    private String generalAgentName;

    @Schema(description = "总代级别")
    private Integer generalAgentLevel;
    /**
     *销售人员id
     */
    @Schema(description = "销售人员id")
    private String saleId;
    /**
     *销售部门
     */
    @Schema(description = "销售部门")
    private String saleDept;
    /**
     *销售人员名字
     */
    @Schema(description = "销售人员名字")
    private String saleName;
    /**
     *销售人员电话
     */
    @Schema(description = "销售人员电话")
    private String salePhone;
    /**
     *销售人员邮箱
     */
    @Schema(description = "销售人员邮箱")
    private String saleEmail;
    /**
     *渠道总监id
     */
    @Schema(description = "渠道总监id")
    private String agentDirectorId;
    /**
     *渠道总监名字
     */
    @Schema(description = "渠道总监名字")
    private String agentDirectorName;
    /**
     *渠道总监部门
     */
    @Schema(description = "渠道总监部门")
    private String agentDirectorDept;
    /**
     *渠道总监电话
     */
    @Schema(description = "渠道总监电话")
    private String agentDirectorPhone;
    /**
     *渠道总监邮箱
     */
    @Schema(description = "渠道总监邮箱")
    private String agentDirectorEmail;
    /**
     *代理开始时间
     */
    @Schema(description = "代理开始时间")
    @NotNull
    private LocalDate agentStartTime;
    /**
     *代理结束时间
     */
    @Schema(description = "代理结束时间")
    @NotNull
    private LocalDate agentEndTime;
    /**
     * 生效时间
     */
    @Schema(description = "生效时间")
    private LocalDateTime effectiveTime;

    @Schema(description = "渠道联系人id")
    private String agentContactId;
    /**
     *删除标记 0-未删除 1-已删除
     */
    @Schema(description = "删除标记 0-未删除 1-已删除")
    private Integer delFlag;

    @Schema(description = "创建人")
    private String createUser;

    @Schema(description = "创建人名字")
    private String createUserName;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "代理区域")
    @NotEmpty
    private List<AgentAuthenticationAreaDTO> agentAuthenticationAreaDTOS;

    @Schema(description = "代理行业")
    @NotEmpty
    private List<AgentAuthenticationIndustryDTO> agentAuthenticationIndustryDTO;

    @Schema(description = "上传附件")
    @NotEmpty
    private List<AgentAuthenticationDocDTO> agentAuthenticationDocDTOS;

    @Schema(description = "渠道联系人")
    @NotNull
    private CrmAgentContactsVo crmAgentContactsVo;

    @Schema(description = "付款凭证与盖章协议")
    private List<AgentAuthenticationDocDTO> agentAuthenticationPaymentDocs;

    @Schema(description = "授权证书和协议原件")
    private List<AgentAuthenticationDocDTO> agentAuthenticationAuthorizationDocs;
}
