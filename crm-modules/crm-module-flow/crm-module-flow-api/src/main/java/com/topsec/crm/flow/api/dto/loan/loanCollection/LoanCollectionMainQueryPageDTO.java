package com.topsec.crm.flow.api.dto.loan.loanCollection;

import com.topsec.crm.framework.common.web.page.CrmPageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Set;

@Data
public class LoanCollectionMainQueryPageDTO extends CrmPageQuery {
    @Schema(description = "催收单号")
    private String processNumber;
    @Schema(description = "借款人")
    private Set<String> loanUserId;
    @Schema(description = "所属部门")
    private String depName;
    @Schema(description = "借款单号")
    private String loanApplyProcessNumber;
    @Schema(description = "转费用单号")
    private String loanToCostNumber;
    @Schema(description = "流程状态")
    private Integer processState;
}
