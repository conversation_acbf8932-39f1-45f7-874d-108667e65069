package com.topsec.crm.flow.api.dto.performancereport;

import com.topsec.crm.flow.api.dto.ProjectInputLaunchable;
import com.topsec.crm.framework.common.bean.AgentPrePaymentVerificationVO;
import com.topsec.crm.framework.common.bean.CrmProjectProductSnVO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class PerformanceReportFlowLaunchDTO extends ProjectInputLaunchable {

    @Schema(description = "基本信息")
    @Valid
    @NotNull
    private PerformanceReportVO baseInfo;

    @Schema(description = "自有产品")
    @NotEmpty
    // @Valid
    private List<PerformanceReportProductOwnLaunchDTO> productList;


    @Schema(description = "付款条款")
    // @Valid
    private List<PerformanceReportPaymentTermsDTO> paymentTermsList;


    @Schema(description = "合同发货信息")
    @Valid
    // @NotEmpty
    private List<PerformanceReportContractDeliveryDTO> contractDeliveryList;


    @Schema(description = "附件")
    @Valid
    // @NotEmpty
    private List<PerformanceReportDocDTO> attachmentList;

    @Schema(description = "使用预付款")
    private List<AgentPrePaymentVerificationVO> prePaymentVerificationList;


    @Data
    public static class PerformanceReportProductOwnLaunchDTO implements Serializable {

        // @ApiModelProperty(value = "ID")
        // private String id;

        @NotEmpty
        @Schema(description = "项目中的产品行记录id（项目订单-项目中报备且状态为“未上报”的产品）")
        private String projectRecordId;
        @NotEmpty
        @Schema(description = "项目中的父行记录id")
        private String parentId;
        @NotEmpty
        @Schema(description = "产品ID")
        private String productId;
        @NotNull
        @Schema(description = "产品数量")
        private Long productNum;

        @Schema(description = "产品LIC数")
        private Integer productLic;

        @NotNull
        @Schema(description = "产品单价")
        private BigDecimal quotedPrice;
        @NotNull
        @Schema(description = "产品总价")
        private BigDecimal quotedTotalPrice;
        // @NotNull
        // @ApiModelProperty(value = "产品属性 1-非分、2-分销")
        // private Integer attr;
        @NotEmpty
        @Schema(description = "产品规格")
        private String productSpecification;
        @NotEmpty
        @Schema(description = "物料代码")
        private String stuffCode;
        @NotEmpty
        @Schema(description = "产品名称")
        private String productName;
        @NotEmpty
        @Schema(description = "产品pn")
        private String pnCode;

        @NotNull
        @Schema(description = "成交单价")
        private BigDecimal dealPrice;
        @NotNull
        @Schema(description = "成交总价")
        private BigDecimal dealTotalPrice;
        @NotNull
        @Schema(description = "使用返点金额（每台）")
        private BigDecimal rebatePrice;

        @NotNull
        @Schema(description = "使用返点总金额")
        private BigDecimal rebateTotalPrice;
        @NotNull
        @Schema(description = "最终用户单价")
        private BigDecimal finalPrice;
        @NotNull
        @Schema(description = "最终用户总价")
        private BigDecimal finalTotalPrice;
        @Schema(description = "税率")
        private BigDecimal taxRate;
        @NotNull
        @Schema(description = "产品保修期(月)")
        private Long productPeriod;
        @NotNull
        @Schema(description = "产品保修开始日期")
        private LocalDate productPeriodStart;
        @NotNull
        @Schema(description = "产品保修结束日期")
        private LocalDate productPeriodEnd;

        @Schema(description = "产品的序列号")
        private List<CrmProjectProductSnVO> crmProjectProductSn;

        @Schema(description = "分摊外包服务费")
        private BigDecimal splitOutsourcePrice;

        @Schema(description = "折扣")
        private BigDecimal discount;
    }
}
