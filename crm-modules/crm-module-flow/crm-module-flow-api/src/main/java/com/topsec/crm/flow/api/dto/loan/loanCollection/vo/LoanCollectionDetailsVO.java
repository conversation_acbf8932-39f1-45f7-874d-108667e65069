package com.topsec.crm.flow.api.dto.loan.loanCollection.vo;

import com.topsec.crm.framework.common.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class LoanCollectionDetailsVO {

    @Schema(hidden = true)
    private String parentProcessInstanceId;

    @Schema(hidden = true)
    private String ProcessInstanceId;

    @Excel(name = "借款催收单号",sort = 0)
    @Schema(description = "借款催收单号")
    private String processNumber;

    @Excel(name = "借款催收发起时间",localDateTimeFormat = ("yyyy-MM-dd"),sort = 1)
    @Schema(description = "借款催收发起时间")
    private LocalDateTime generateTime;

    @Excel(name = "借款催收办结时间",localDateTimeFormat = ("yyyy-MM-dd"),sort = 2)
    @Schema(description = "借款催收办结时间")
    private LocalDateTime processEndTime;

    @Excel(name = "借款流程编号",sort = 3)
    @Schema(description = "借款流程编号")
    private String loanApplyProcessNumber;

    @Excel(name = "借款人",sort = 4)
    @Schema(description = "借款人")
    private String loanUserName;

    @Excel(name = "所属部门",sort = 5)
    @Schema(description = "所属部门")
    private String depName;

    @Excel(name = "借款金额",sort = 6)
    @Schema(description = "借款金额")
    private BigDecimal loanAmount;

    @Excel(name = "借款申请时间",localDateTimeFormat = ("yyyy-MM-dd"),sort = 7)
    @Schema(description = "借款申请时间")
    private LocalDateTime loanApplyTime;

    @Excel(name = "签订公司",sort = 8)
    @Schema(description = "签订公司")
    private String signCompany;

    @Excel(name = "项目编号",sort = 9)
    @Schema(description = "项目编号")
    private String projectNo;

    @Excel(name = "项目名称",sort = 10)
    @Schema(description = "项目名称")
    private String projectName;

    @Excel(name = "付款时间",localDateTimeFormat = ("yyyy-MM-dd"),sort = 11)
    @Schema(description = "付款时间")
    private LocalDateTime repaymentDate;

    @Excel(name = "付款方式", readConverterExp="1=支票,2=现金,3=电汇,4=其他",sort = 12)
    @Schema(description = "付款方式 1=支票 2=现金 3=电汇 4=其他")
    private Integer paymentType;

    @Excel(name = "付款对象类型", readConverterExp="1=个人,2=招标公司,3=用户",sort = 13)
    @Schema(description = "付款对象类型 1-个人 2-招标公司 3-用户")
    private Integer payerType;

    @Excel(name = "付款对象名称",sort = 14)
    @Schema(description = "付款对象名称")
    private String payerName;

    @Excel(name = "转费用单号",sort = 15)
    @Schema(description = "转费用单号")
    private String loanToCostNumber;

    @Excel(name = "费用分类", readConverterExp="1=投标保证金,2=保函,3=履约保证金,4=标书费,5=中标服务费,6=采购借款,7=费用",sort = 16)
    @Schema(description = "费用分类 1=投标保证金 2=保函 3=履约保证金 4=标书费 5=中标服务费 6=采购借款 7=费用")
    private Integer loanType;

    @Excel(name = "费用金额",sort = 17)
    @Schema(description = "费用金额")
    private BigDecimal costMoney;

    @Excel(name = "核销金额",sort = 18)
    @Schema(description = "核销金额")
    private BigDecimal recognizedMoney;

    @Excel(name = "未核销金额",sort = 19)
    @Schema(description = "未核销金额")
    private BigDecimal unrecognizedMoney;

    @Excel(name = "预计归还时间",localDateTimeFormat = ("yyyy-MM-dd"),sort = 20)
    @Schema(description = "预计归还时间")
    private LocalDateTime repaymentDueDate;

    @Excel(name = "催收反馈",sort = 21)
    @Schema(description = "催收反馈")
    private String collectionFeedback;

    @Excel(name = "流程进度",sort = 22)
    @Schema(description = "流程进度")
    private String ProcessProgress;
}
