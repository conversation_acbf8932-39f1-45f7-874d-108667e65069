package com.topsec.crm.flow.api;

import com.topsec.crm.framework.common.constant.ServiceNameConstants;
import com.topsec.crm.stats.api.entity.home.HomeRankContractForecastVO;
import com.topsec.tbscommon.JsonObject;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @version V1.0
 * @Description: 远程调用合同预测服务
 * @ClassName: com.topsec.crm.flow.api.RemoteContractForecastService.java
 * @Copyright 天融信 - Powered By 企业软件研发中心
 * @author: leo
 * @date: 2025-07-31 10:40
 */
@FeignClient(contextId = "remoteContractForecastService", value = ServiceNameConstants.FLOW_SERVICE,url = ServiceNameConstants.FLOW_SERVICE_URL)
public interface RemoteContractForecastService {

    //自动发起指定期数的合同预测流程
    @GetMapping("/hidden/contractForecast/sendContractForecastFlow")
    JsonObject<Boolean> sendContractForecastFlow(@RequestParam String month);

    // 查询合同预测未办结数据排名
    @GetMapping("/hidden/contractForecast/unfinishedContractForecastRank")
    JsonObject<List<HomeRankContractForecastVO>> unfinishedContractForecastRank(@RequestParam Integer type);

    // 自动完成合同预测01步
    @PostMapping("/hidden/contractForecast/autoFinishContractForecastFlow01")
    JsonObject<Boolean> autoFinishContractForecastFlow01(@RequestBody List<String> month);

    // 自动完成合同预测02步
    @PostMapping("/hidden/contractForecast/autoFinishContractForecastFlow02")
    JsonObject<Boolean>  autoFinishContractForecastFlow02(@RequestBody List<String> month);
}
