package com.topsec.crm.flow.api.vo.handoverProcess;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 评审内容交接信息表
 * 
 * <AUTHOR>
 * @email 
 * @date 2025-07-22 14:30:20
 */
@Data
public class HandoverReviewContentVo implements Serializable {
	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 交接记录ID
	 */
	@Schema(description = "交接记录ID")
	private String id;
	/**
	 * 流程实例ID
	 */
	@Schema(description = "流程实例ID")
	private String processInstanceId;
	/**
	 * 节点id
	 */
	@Schema(description = "节点id")
	private String activityId;
	/**
	 * 评审内容
	 */
	@Schema(description = "评审内容")
	private String reviewContent;
	/**
	 * 评审结果(0-不通过,1-通过)
	 */
	@Schema(description = "评审结果(0-不通过,1-通过)")
	private Integer reviewResult;
	/**
	 * 评审备注
	 */
	@Schema(description = "评审备注")
	private String reviewNotes;

	/**
	 * 评审人
	 */
	@Schema(description = "评审人")
	private String personId;


	@Schema(description = "评审人姓名")
	private String personName;

	/**
	 * 评审部门
	 */
	@Schema(description = "评审部门")
	private String deptId;


}
