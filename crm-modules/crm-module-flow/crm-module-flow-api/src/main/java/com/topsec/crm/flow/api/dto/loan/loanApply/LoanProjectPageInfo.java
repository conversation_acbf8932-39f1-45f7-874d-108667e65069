package com.topsec.crm.flow.api.dto.loan.loanApply;

import com.topsec.crm.framework.common.web.page.PageDomain;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
public class LoanProjectPageInfo extends PageDomain {

    @Schema(description = "查询参数")
    private String param;

    @Schema(description = "项目ID")
    private String businessId;

    @Schema(description = "项目编号")
    private String projectNo;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "最终用户")
    private String finalCustomer;

    @Schema(description = "项目金额")
    private BigDecimal projectAmount;

    @Schema(description = "项目负责人")
    private String projectLeader;

}
