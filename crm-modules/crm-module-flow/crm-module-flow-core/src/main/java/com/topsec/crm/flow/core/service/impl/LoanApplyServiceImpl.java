package com.topsec.crm.flow.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.topsec.crm.account.api.client.RemoteAccountService;
import com.topsec.crm.flow.api.dto.loan.loanApply.*;
import com.topsec.crm.flow.api.dto.loan.loanToCost.LoanToCostFlowBaseInfoLaunchDTO;
import com.topsec.crm.flow.api.dto.loan.loanToCost.LoanToCostPageQuery;
import com.topsec.crm.flow.api.dto.loan.loanVerification.LoanVerificationQuery;
import com.topsec.crm.flow.api.dto.loan.loanVerification.VerificationPageInfo;
import com.topsec.crm.flow.api.vo.ProcessFileInfoVO;
import com.topsec.crm.flow.api.vo.ProcessFileInput;
import com.topsec.crm.flow.api.vo.ThreeProcurementReviewQuery;
import com.topsec.crm.flow.api.vo.handoverProcess.HandoverLoanVo;
import com.topsec.crm.flow.core.entity.FlowBaseEntity;
import com.topsec.crm.flow.core.entity.LoanApply;
import com.topsec.crm.flow.core.entity.ProcessExtensionInfo;
import com.topsec.crm.flow.core.entity.ThreeProcurementReviewMain;
import com.topsec.crm.flow.core.mapper.LoanApplyMapper;
import com.topsec.crm.flow.core.process.ProcessTypeEnum;
import com.topsec.crm.flow.core.service.LoanApplyService;
import com.topsec.crm.flow.core.service.LoanVerificationService;
import com.topsec.crm.flow.core.service.ProcessFileInfoService;
import com.topsec.crm.flow.core.service.ThreeProcurementReviewMainService;
import com.topsec.crm.framework.common.bean.FileInput;
import com.topsec.crm.framework.common.bean.FlowPerson;
import com.topsec.crm.framework.common.bean.HandoverProcessQuery;
import com.topsec.crm.framework.common.enums.LoanEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.AccountAccquireUtils;
import com.topsec.crm.framework.common.util.CommonUtils;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.operation.api.RemoteContractReviewConfigService;
import com.topsec.crm.operation.api.entity.ContractReviewConfig.ContractSignCompanyVO;
import com.topsec.crm.project.api.client.RemoteProjectDirectlyClient;
import com.topsec.crm.project.api.client.RemoteProjectMemberClient;
import com.topsec.crm.project.api.entity.CrmProjectDirectlyVo;
import com.topsec.enums.ApprovalStatusEnum;
import com.topsec.enums.ProcessDefinitionKeyEnum;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.tbsapi.client.TbsDepartmentClient;
import com.topsec.tbscommon.vo.DepartmentVO;
import com.topsec.tbscommon.vo.PersonVO;
import com.topsec.tfs.api.client.TfsNodeClient;
import com.topsec.tos.api.client.TosEmployeeClient;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.tos.common.query.PersonQuery;
import com.topsec.tos.common.vo.EmployeeVO;
import com.topsec.vo.node.ApproveNode;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class LoanApplyServiceImpl extends ServiceImpl<LoanApplyMapper, LoanApply> implements LoanApplyService {

    private final RemoteProjectDirectlyClient projectDirectlyClient;

    private final RemoteProjectMemberClient projectMemberClient;

    private final ThreeProcurementReviewMainService purchaseService;

    private final ProcessFileInfoService processFileInfoService;

    private final RemoteAccountService accountService;

    private final TbsDepartmentClient departmentClient;

    private final RemoteContractReviewConfigService contractReviewConfigService;

    private final TfsNodeClient tfsNodeClient;

    private final TosEmployeeClient employeeClient;

    private final LoanVerificationService loanVerificationService;

    @Override
    public PageUtils<LoanApplyDTO> loanApplyPage(LoanApplyQuery query) {
        PageUtils<LoanApplyDTO> pageUtils = new PageUtils<>();
        List<LoanApply> loanApplies = baseMapper.selectList(
                // 通用查询条件
                buildQueryWrapper(query)
                        // 查询借款分类流程
                        .eq(LoanApply::getProcessDefinitionKey, ProcessTypeEnum.LOAN_APPLY.getProcessDefinitionKey().getValue())
        );
        List<LoanApplyDTO> loanApplyDTOS = HyperBeanUtils.copyListProperties(loanApplies, LoanApplyDTO::new);
        // 填充数据
        fillLoanDTOs(loanApplyDTOS);
        pageUtils.setList(loanApplyDTOS);
        pageUtils.setTotalCount((int) new PageInfo<>(loanApplies).getTotal());
        return pageUtils;
    }

    @Override
    public PageUtils<LoanApplyDTO> loanToCostPage(LoanToCostPageQuery toCostQuery) {
        LoanApplyQuery query = HyperBeanUtils.copyPropertiesByJackson(toCostQuery,LoanApplyQuery.class);
        PageUtils<LoanApplyDTO> pageUtils = new PageUtils<>();
        List<LoanApply> loanApplies = baseMapper.selectList(
                // 通用查询条件
                buildQueryWrapper(query)
                        // 查询借款分类流程
                        .eq(LoanApply::getProcessDefinitionKey, ProcessDefinitionKeyEnum.BORROWING_EXPENSE_TRANSFER_KEY.getValue())
                        .eq(StringUtils.isNotEmpty(toCostQuery.getParentProcessNumber()),LoanApply::getParentProcessNumber,toCostQuery.getParentProcessNumber())
        );
        List<LoanApplyDTO> loanApplyDTOS = HyperBeanUtils.copyListProperties(loanApplies, LoanApplyDTO::new);
        // 填充数据
        fillLoanDTOs(loanApplyDTOS);
        pageUtils.setList(loanApplyDTOS);
        pageUtils.setTotalCount((int) new PageInfo<>(loanApplies).getTotal());
        return pageUtils;
    }

    @Override
    public PageUtils<VerificationPageInfo> loanVerificationPage(LoanVerificationQuery query) {

        LoanApplyQuery loanApplyQuery = HyperBeanUtils.copyProperties(query, LoanApplyQuery::new);
        PageUtils<VerificationPageInfo> pageUtils = new PageUtils<>();
        List<LoanApply> loanApplies = baseMapper.selectList(
                buildQueryWrapper(loanApplyQuery)
                        .eq(StringUtils.isNotBlank(query.getToCostProcessNumber()),LoanApply::getProcessNumber, query.getToCostProcessNumber())
        );
        List<LoanApplyDTO> loanApplyDTOS = HyperBeanUtils.copyListProperties(loanApplies, LoanApplyDTO::new);
        // 填充借款基本数据
        fillLoanDTOs(loanApplyDTOS);
        // TODO 填充核销相关数据
        List<VerificationPageInfo> verificationPageInfos = HyperBeanUtils.copyListProperties(loanApplyDTOS, VerificationPageInfo::new);
        pageUtils.setList(verificationPageInfos);
        pageUtils.setTotalCount((int) new PageInfo<>(loanApplies).getTotal());
        return pageUtils;
    }

    private void fillLoanDTOs(List<LoanApplyDTO> loanApplyDTOS) {
        if (loanApplyDTOS == null || loanApplyDTOS.isEmpty()){
            return;
        }
        // 提取用户ID、签约公司ID、核算部门ID，出纳人员ID,业务ID列表
        List<String> loanUserIds = loanApplyDTOS.stream().map(LoanApplyDTO::getLoanUser).distinct().toList();
        List<String> signCompanyIds = loanApplyDTOS.stream().map(LoanApplyDTO::getSignCompanyId).distinct().toList();
        List<String> checkDeptIds = loanApplyDTOS.stream().map(LoanApplyDTO::getCheckDeptId).distinct().toList();
        List<String> loanTellerIds = loanApplyDTOS.stream().map(LoanApplyDTO::getLoanTellerPersonId).filter(Objects::nonNull).distinct().toList();
        List<String> businessIds = loanApplyDTOS.stream().map(LoanApplyDTO::getBusinessId).distinct().toList();
        List<String> processInstanceIds = loanApplyDTOS.stream().map(LoanApplyDTO::getProcessInstanceId).distinct().toList();

        try {

            // 查询已核销金额
            CompletableFuture<Map<String, BigDecimal>> amountFuture = CompletableFuture.supplyAsync(() ->
                    loanVerificationService.getAmountByProcessInstanceIds(processInstanceIds)
            );

            // 查询流程进度
            CompletableFuture<Map<String, Set<ApproveNode>>> flowFuture = CompletableFuture.supplyAsync(() ->
                    tfsNodeClient.queryNodeByProcessInstanceIdList(loanApplyDTOS.stream().map(LoanApplyDTO::getProcessInstanceId).toList()).getObjEntity()
            );

            // 借款人信息
            CompletableFuture<Map<String, PersonVO>> personFuture = CompletableFuture.supplyAsync(() ->
                    accountService.queryPersonByIdsWithDept(loanUserIds)
                            .getObjEntity().stream()
                            .collect(Collectors.toMap(PersonVO::getUuid, item -> item))
            );

            // 签订公司信息
            CompletableFuture<Map<String, ContractSignCompanyVO>> signCompanyFuture = CompletableFuture.supplyAsync(() ->
                    contractReviewConfigService.getBySignCompanyIds(signCompanyIds)
                            .getObjEntity().stream()
                            .collect(Collectors.toMap(ContractSignCompanyVO::getId, item -> item))
            );

            // 核算部门信息
            CompletableFuture<Map<String, DepartmentVO>> checkDeptFuture = CompletableFuture.supplyAsync(() ->
                    departmentClient.listByIds(checkDeptIds)
                            .getObjEntity().stream()
                            .collect(Collectors.toMap(DepartmentVO::getUuid, item -> item))
            );

            // 项目信息
            CompletableFuture<Map<String, CrmProjectDirectlyVo>> projectFuture = CompletableFuture.supplyAsync(() ->
                    projectDirectlyClient.listSimpleDataByIds(businessIds)
                            .getObjEntity().stream()
                            .collect(Collectors.toMap(CrmProjectDirectlyVo::getId, item -> item))
            );

            // 出纳人员信息
            CompletableFuture<Map<String, FlowPerson>> cashierFuture = CompletableFuture.supplyAsync(() -> {
                List<FlowPerson> flowPeople = AccountAccquireUtils.convertGetByPersonIdTbs(loanTellerIds);
                return flowPeople.stream()
                        .collect(Collectors.toMap(FlowPerson::getPersonId, Function.identity()));
            });

            // 等待所有任务完成
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                    personFuture,
                    signCompanyFuture,
                    checkDeptFuture,
                    flowFuture,
                    cashierFuture,
                    projectFuture,
                    amountFuture
            );
            allFutures.join();

            // 获取结果
            Map<String, PersonVO> personVOMap = personFuture.get();
            Map<String, ContractSignCompanyVO> signCompanyVOMap = signCompanyFuture.get();
            Map<String, DepartmentVO> checkDeptMap = checkDeptFuture.get();
            Map<String, Set<ApproveNode>> approveNodeMap = flowFuture.get();
            Map<String, FlowPerson> cashierMap = cashierFuture.get();
            Map<String, CrmProjectDirectlyVo> projectMap = projectFuture.get();
            Map<String, BigDecimal> amountMap = amountFuture.get();

            // 填充数据
            loanApplyDTOS.forEach(item -> {
                String loanUserId = item.getLoanUser();
                String signCompanyId = item.getSignCompanyId();
                String checkDeptId = item.getCheckDeptId();
                String loanTellerPersonId = item.getLoanTellerPersonId();
                String businessId = item.getBusinessId();
                String processInstanceId = item.getProcessInstanceId();

                // 获取未核销金额
                item.setUnVerifiedAmount(item.getLoanAmount().subtract(amountMap.getOrDefault(processInstanceId, BigDecimal.ZERO)));

                // 获取出纳人员信息
                item.setLoanTeller(cashierMap.get(loanTellerPersonId));

                // 获取项目名称
                item.setProjectName(Optional.ofNullable(projectMap.get(businessId))
                        .map(CrmProjectDirectlyVo::getProjectName)
                        .orElse("未知"));

                // 获取 PersonVO
                PersonVO personVO = personVOMap.get(loanUserId);
                if (personVO != null) {
                    item.setLoanUserName(personVO.getName());
                    DepartmentVO deptVO = personVO.getDepartmentVO();
                    if (deptVO != null) {
                        item.setLoanDeptName(deptVO.getDeptName());
                        item.setLoanDeptId(deptVO.getUuid());
                    }
                }

                // 获取核算部门名称
                item.setCheckDeptName(Optional.ofNullable(checkDeptMap.get(checkDeptId))
                        .map(DepartmentVO::getDeptName)
                        .orElse("未知"));

                // 获取签约公司简称
                item.setSignCompanyName(Optional.ofNullable(signCompanyVOMap.get(signCompanyId))
                        .map(ContractSignCompanyVO::getCompanyNameShort)
                        .orElse("未知"));

                // 填充流程进度
                item.setApproveNodes(approveNodeMap.get(item.getProcessInstanceId()));

            });
        } catch (Exception e) {
            throw new CrmException("填充借款信息时发生错误：" + e.getMessage());
        }
    }


    // 借款流程查询条件
    private LambdaQueryWrapper<LoanApply> buildQueryWrapper(LoanApplyQuery query) {
        return new LambdaQueryWrapper<LoanApply>()
                .eq(LoanApply::getDelFlag, false)
                .in(query.getLoanUserList() != null && !query.getLoanUserList().isEmpty(), LoanApply::getLoanUser, query.getLoanUserList())
                .in(query.getLoanType() != null && !query.getLoanType().isEmpty() ,LoanApply::getLoanType, query.getLoanType())
                .like(StringUtils.isNotBlank(query.getPayerName()), LoanApply::getPayerName, query.getPayerName())
                .like(query.getBankAccount() != null, LoanApply::getBankAccount, query.getBankAccount())
                .eq(StringUtils.isNotBlank(query.getProjectNo()), LoanApply::getBusinessNo, query.getProjectNo())
                .eq(StringUtils.isNotBlank(query.getPurchaseNo()),LoanApply::getBusinessNo, query.getPurchaseNo())
                .like(StringUtils.isNotBlank(query.getLinkman()), LoanApply::getLinkman, query.getLinkman())
                .like(StringUtils.isNotBlank(query.getProcessNumber()), LoanApply::getProcessNumber, query.getProcessNumber());
    }

    @Override
    public List<LoanApplyDTO> loanApplyExport(LoanApplyQuery query) {
        List<LoanApply> loanApplies = baseMapper.selectList(
                buildQueryWrapper(query)
                .eq(LoanApply::getProcessDefinitionKey, ProcessTypeEnum.LOAN_APPLY.getProcessDefinitionKey().getValue())
        );
        List<LoanApplyDTO> loanApplyDTOS = HyperBeanUtils.copyListProperties(loanApplies, LoanApplyDTO::new);
        fillLoanDTOs(loanApplyDTOS);
        return loanApplyDTOS;
    }

    @Override
    public LoanApplyDTO queryByProcessInstanceId(String processInstanceId) {
        // 查询借款申请信息
        LoanApply loanApply = baseMapper.selectOne(new LambdaQueryWrapper<LoanApply>()
                .eq(LoanApply::getDelFlag, false)
                .eq(LoanApply::getProcessInstanceId, processInstanceId)
        );

        return buildLoanApplyDTO(loanApply);
    }

    @Override
    public List<LoanApplyDTO> queryByProcessInstanceIdList(List<String> processInstanceIdList) {
        List<LoanApply> loanApplies = baseMapper.selectList(new LambdaQueryWrapper<LoanApply>()
                .eq(LoanApply::getDelFlag, false)
                .in(LoanApply::getProcessInstanceId, processInstanceIdList)
        );
        List<LoanApplyDTO> loanApplyDTOS = HyperBeanUtils.copyListProperties(loanApplies, LoanApplyDTO::new);
        fillLoanDTOs(loanApplyDTOS);
        return loanApplyDTOS;
    }

    @Override
    public LoanApplyDTO queryById(String id) {
        // 查询借款申请信息
        LoanApply loanApply = baseMapper.selectOne(new LambdaQueryWrapper<LoanApply>()
                .eq(LoanApply::getDelFlag, false)
                .eq(LoanApply::getId, id)
        );
        return buildLoanApplyDTO(loanApply);
    }

    // 填充借款详情信息
    private LoanApplyDTO buildLoanApplyDTO(LoanApply loanApply) {

        LoanApplyDTO loanApplyDTO = HyperBeanUtils.copyProperties(loanApply, LoanApplyDTO::new);
        if (loanApply == null){
            return null;
        }
        fillLoanDTOs(List.of(loanApplyDTO));
        String processInstanceId = loanApply.getProcessInstanceId();

        if (processInstanceId == null) {
            loanApplyDTO.setApplyBasisFiles(Collections.emptyList());
            loanApplyDTO.setOtherFiles(Collections.emptyList());
            loanApplyDTO.setLoanProofFiles(Collections.emptyList());
            return loanApplyDTO;
        }

        // 查询流程文件信息
        List<ProcessFileInfoVO> processFileInfoVOS = processFileInfoService.queryByProcessInstanceId(processInstanceId, null, null);
        if (processFileInfoVOS == null) {
            processFileInfoVOS = Collections.emptyList();
        }

        // 获取附件类型集合
        List<String> basisAttachments = LoanEnum.LoanAttachmentTypeEnum.getApplyBasisAttachments();
        List<String> otherAttachments = LoanEnum.LoanAttachmentTypeEnum.getOtherAttachments();
        List<String> loanProofAttachments = LoanEnum.LoanAttachmentTypeEnum.getLoanProofAttachments();

        // 过滤不同类型的文件
        loanApplyDTO.setApplyBasisFiles(filterFilesByTypes(processFileInfoVOS, basisAttachments));
        loanApplyDTO.setOtherFiles(filterFilesByTypes(processFileInfoVOS, otherAttachments));
        loanApplyDTO.setLoanProofFiles(filterFilesByTypes(processFileInfoVOS, loanProofAttachments));

        return loanApplyDTO;
    }

    /**
     * 根据指定的文件类型筛选文件信息
     */
    private List<ProcessFileInfoVO> filterFilesByTypes(List<ProcessFileInfoVO> files, List<String> types) {
        if (files == null || types == null || types.isEmpty()) {
            return Collections.emptyList();
        }
        return files.stream()
                .filter(file -> file != null && types.contains(file.getFileType()))
                .toList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveSnapshot(ProcessExtensionInfo processInfo, LoanApplyLaunchDTO req) {
        if (req.getLoanApplyInfo() == null) {
            throw new CrmException("借款申请信息为空");
        }

        try {
            LoanApply loanApply = HyperBeanUtils.copyProperties(req.getLoanApplyInfo(), LoanApply::new);
            loanApply.setLoanUser(UserInfoHolder.getCurrentPersonId());
            loanApply.setProcessDefinitionKey(ProcessTypeEnum.LOAN_APPLY.getProcessDefinitionKey().getValue());

            List<FileInput> applyAttachments = safeCopyFileList(req.getLoanApplyInfo().getApplyBasisFiles());
            List<FileInput> otherAttachments = safeCopyFileList(req.getLoanApplyInfo().getOtherFiles());

            processFileInfoService.uploadProcessFileByFileId(new ProcessFileInput(processInfo.getProcessInstanceId(), applyAttachments));
            processFileInfoService.uploadProcessFileByFileId(new ProcessFileInput(processInfo.getProcessInstanceId(), otherAttachments));

            save(loanApply);
            return loanApply.getId();
        } catch (Exception e) {
            throw new CrmException("发起失败");
        }
    }

    private List<FileInput> safeCopyFileList(List<ProcessFileInfoVO> files) {
        if (files == null) {
            return Collections.emptyList();
        }
        return HyperBeanUtils.copyListProperties(files, FileInput::new);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean uploadLoanProofAttachments(String processInstanceId, List<ProcessFileInfoVO> fileInput, String loanTellerPersonId) {
        LoanApply loanApply = baseMapper.selectOne(new LambdaQueryWrapper<LoanApply>()
                .eq(LoanApply::getProcessInstanceId, processInstanceId)
                .eq(LoanApply::getDelFlag, false)
        );
        if (loanApply == null) {
            return false;
        }
        loanApply.setLoanTellerPersonId(loanTellerPersonId);
        updateById(loanApply);
        List<FileInput> copiedFileList = safeCopyFileList(fileInput);
        return processFileInfoService.uploadProcessFileByFileId(
                new ProcessFileInput(processInstanceId, copiedFileList)
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean fillRepaymentDate(String processInstanceId, LocalDateTime repaymentDate,List<ProcessFileInfoVO> fileInput) {
        LoanApply loanApply = baseMapper.selectOne(new LambdaQueryWrapper<LoanApply>()
                .eq(LoanApply::getDelFlag, false)
                .eq(LoanApply::getProcessInstanceId, processInstanceId)
        );
        if (loanApply == null) {
            return false;
        }
        loanApply.setRepaymentDate(repaymentDate);
        updateById(loanApply);
        List<FileInput> copiedFileList = safeCopyFileList(fileInput);
        return processFileInfoService.uploadProcessFileByFileId(
                new ProcessFileInput(processInstanceId, copiedFileList)
        );
    }

    @Override
    public Boolean isCanToCost(String processInstanceId) {

        if (StringUtils.isBlank(processInstanceId)){
            throw new CrmException("流程实例ID不能为空");
        }

        // 判断条件 and关系 全部通过即为true
        // 1、借款流程已办结；
        // 2、借款类型为“营销类/投标保证金”；
        // 3、登录人为该流程借款人；
        // 4、未核销金额>0
        LoanApply loanApply = baseMapper.selectOne(new LambdaQueryWrapper<LoanApply>()
                .eq(LoanApply::getDelFlag, false)
                .eq(LoanApply::getProcessInstanceId, processInstanceId)
                .eq(LoanApply::getLoanType, LoanEnum.LoanTypeEnum.MARKETING_BID_BOND.getCode())
                .eq(FlowBaseEntity::getProcessState, ApprovalStatusEnum.YSP.getCode())
                .eq(LoanApply::getLoanUser, UserInfoHolder.getCurrentPersonId())
        );
        if (loanApply == null){
            return false;
        }
        Map<String, BigDecimal> amount = loanVerificationService.getAmountByProcessInstanceIds(List.of(loanApply.getProcessInstanceId()));
        // 未核销金额 = 借款金额-核销金额(包含转费用金额)
        BigDecimal unVerifiedAmount = loanApply.getLoanAmount().subtract(amount.getOrDefault(loanApply.getProcessInstanceId(), BigDecimal.ZERO));
        return unVerifiedAmount.compareTo(BigDecimal.ZERO) > 0;
    }

    @Override
    public PageUtils<LoanProjectPageInfo> projectSelectPage(LoanProjectPageInfo req) {

        // 获取项目成员信息
        List<String> projectIds = Optional.ofNullable(
                projectMemberClient.queryProjectIdsByProjectLeader(
                        UserInfoHolder.getCurrentPersonId()).getObjEntity()
        ).orElse(Collections.emptyList());

        if (projectIds.isEmpty()) {
            return new PageUtils<>(Collections.emptyList(), 0, req.getPageSize(), req.getPageNum());
        }

        // 获取项目信息
        List<CrmProjectDirectlyVo> projectInfoList = Optional.ofNullable(
                projectDirectlyClient.listByIds(projectIds).getObjEntity()
        ).orElse(Collections.emptyList());

        // 根据参数筛选：项目名称或项目ID
        if (StringUtils.isNotBlank(req.getParam())){
            projectInfoList = projectInfoList.stream()
                    .filter(project -> project.getProjectName().contains(req.getParam()) || project.getProjectNo().contains(req.getParam()))
                    .toList();
        }

        // 转换为 LoanProjectPageInfo 列表
        List<LoanProjectPageInfo> loanProjectPageInfos = projectInfoList.stream()
                .map(project -> {
                    LoanProjectPageInfo info = new LoanProjectPageInfo();
                    info.setBusinessId(project.getId());
                    info.setProjectNo(project.getProjectNo());
                    info.setProjectName(project.getProjectName());
                    info.setFinalCustomer(project.getFinalCustomer());
                    info.setProjectAmount(project.getProjectBudget());
                    info.setProjectLeader(UserInfoHolder.getCurrentPersonName());
                    return info;
                })
                .toList();
        List<LoanProjectPageInfo> pagedList = CommonUtils.subListPage(loanProjectPageInfos, req.getPageSize(), req.getPageNum());
        return new PageUtils<>(pagedList, loanProjectPageInfos.size(), req.getPageSize(), req.getPageNum());
    }

    @Override
    public PageUtils<LoanPurchasePageInfo> purchaseSelectPage(String param) {
        ThreeProcurementReviewQuery queryVO = new ThreeProcurementReviewQuery();
        queryVO.setProcessNumber( param);
        queryVO.setCreateUser(UserInfoHolder.getCurrentPersonId());
        IPage<ThreeProcurementReviewMain> threeProcurementIPage = purchaseService.selectCompletedProcurementWithoutPayment(queryVO);
        PageUtils<LoanPurchasePageInfo> result = new PageUtils<>();
        result.setList(HyperBeanUtils.copyListProperties(threeProcurementIPage.getRecords(), LoanPurchasePageInfo::new));
        result.setTotalCount((int) threeProcurementIPage.getTotal());
        return result;
    }

    @Override
    public PageUtils<HandoverLoanVo> handoverLoanSelectPage(HandoverProcessQuery query) {

        if (query == null || StringUtils.isBlank(query.getPersonId())) {
            throw new CrmException("交接人ID不能为空");
        }
        PageUtils<HandoverLoanVo> pageUtils = new PageUtils<>();
        List<LoanApply> loanApplies = baseMapper.selectList(buildQueryWrapper(query));
        List<HandoverLoanVo> handoverLoanVos = convertToHandoverLoanVos(loanApplies);
        pageUtils.setList(handoverLoanVos);
        pageUtils.setTotalCount((int) new PageInfo<>(loanApplies).getTotal());
        return pageUtils;

    }

    @Override
    public List<HandoverLoanVo> handoverLoanSelectList(HandoverProcessQuery query) {
        if (query == null || StringUtils.isBlank(query.getPersonId())) {
            throw new CrmException("交接人ID不能为空");
        }
        LambdaQueryWrapper<LoanApply> wrapper = buildQueryWrapper(query);
        List<LoanApply> loanApplies = baseMapper.selectList(wrapper);
        return convertToHandoverLoanVos(loanApplies);
    }

    @Override
    public List<String> processingList(String personId) {
        return baseMapper.selectList(new LambdaQueryWrapper<LoanApply>()
                .eq(LoanApply::getDelFlag, false)
                .eq(LoanApply::getProcessState, ApprovalStatusEnum.SPZ.getCode())
        ).stream().map(FlowBaseEntity::getProcessNumber).toList();
    }

    @Override
    public Boolean updateLoanInfo(LoanApplyDTO loanApplyDTO) {

        if (loanApplyDTO == null || loanApplyDTO.getId() == null) {
            log.warn("LoanApplyDTO 或其 ID 为空，无法更新");
            return false;
        }

        LoanApply byId = getById(loanApplyDTO.getId());
        if (byId == null) {
            return false;
        }

        byId.setPayerName(loanApplyDTO.getPayerName());
        byId.setRepaymentDueDate(loanApplyDTO.getRepaymentDueDate());

        boolean updateResult = updateById(byId);
        if (!updateResult) {
            throw new CrmException("更新失败");
        }

        return true;
    }

    @Override
    public Boolean upLoanToCostInfo(LoanToCostFlowBaseInfoLaunchDTO infoLaunchDTO) {
        Optional.ofNullable(infoLaunchDTO.getProcessInstanceId()).orElseThrow(()->new CrmException("流程ID不能为空"));
        LoanApply loanApply = getOne(new LambdaQueryWrapper<LoanApply>().eq(LoanApply::getProcessInstanceId,infoLaunchDTO.getProcessInstanceId()));
        Optional.ofNullable(loanApply).orElseThrow(()->new CrmException("流程信息不存在"));
        return update(null,new LambdaUpdateWrapper<LoanApply>().set(infoLaunchDTO.getToCostType()!=null,LoanApply::getToCostType,infoLaunchDTO.getToCostType())
                .set(infoLaunchDTO.getToCostAmount()!=null,LoanApply::getToCostAmount,infoLaunchDTO.getToCostAmount())
                .set(infoLaunchDTO.getRepaymentDueDate()!=null,LoanApply::getRepaymentDueDate,infoLaunchDTO.getRepaymentDueDate())
                .set(!StringUtils.isEmpty(infoLaunchDTO.getRemarks()),LoanApply::getRemarks,infoLaunchDTO.getRemarks()).eq(LoanApply::getProcessInstanceId,infoLaunchDTO.getProcessInstanceId()));
    }

    @Override
    public List<LoanTellerVO> loanTellerSelectList() {
        Map<String, String> tellerMap = LoanEnum.LoanTellerEnum.getAllTellerMap();
        List<String> values = new ArrayList<>(tellerMap.values());

        PersonQuery query = new PersonQuery();
        query.setNameJobNos(values);
        query.setPageSize(1000);

        Page<EmployeeVO> page = employeeClient.crmPage(query).getObjEntity();
        List<EmployeeVO> list = page.getRecords();
        List<FlowPerson> flowPeople = AccountAccquireUtils.convertGetAccount(list);

        Map<String, FlowPerson> flowPersonMap = flowPeople.stream()
                .collect(Collectors.toMap(
                        flowPerson -> {
                            String name = flowPerson.getPersonName() == null ? "" : flowPerson.getPersonName();
                            String jobNo = flowPerson.getJobNo() == null ? "" : flowPerson.getJobNo();
                            return name + jobNo;
                        },
                        Function.identity(),
                        (existing, replacement) -> existing
                ));

        ArrayList<LoanTellerVO> result = new ArrayList<>();
        for (String key : tellerMap.keySet()) {
            String value = tellerMap.get(key);
            FlowPerson person = flowPersonMap.get(value);
            if (person != null) {
                LoanTellerVO loanTellerVO = new LoanTellerVO();
                loanTellerVO.setCompanyName(key);
                loanTellerVO.setPerson(person);
                result.add(loanTellerVO);
            }
        }
        return result;
    }


    /**
     * 借款人员交接构建查询条件
     */
    private LambdaQueryWrapper<LoanApply> buildQueryWrapper(HandoverProcessQuery query) {
        return new LambdaQueryWrapper<LoanApply>()
                .eq(LoanApply::getDelFlag, false)
                .in(query.getBusinessIdList() != null, LoanApply::getId, query.getBusinessIdList())
                .like(StringUtils.isNotBlank(query.getKeyWord()), LoanApply::getProcessNumber, query.getKeyWord())
                .like(StringUtils.isNotBlank(query.getKeyWord()), LoanApply::getPayerName, query.getKeyWord())
                .eq(LoanApply::getLoanUser, query.getPersonId());
    }

    /**
     * 转换 LoanApply 到 HandoverLoanVo
     */
    private List<HandoverLoanVo> convertToHandoverLoanVos(List<LoanApply> loanApplies) {
        List<HandoverLoanVo> vos = new ArrayList<>();
        List<String> list = loanApplies.stream().map(FlowBaseEntity::getProcessInstanceId).toList();
        Map<String, BigDecimal> verificationMap = loanVerificationService.getAmountByProcessInstanceIds(list);
        Map<String, BigDecimal> toCostAmount = loanToCostAmount(list);
        for (LoanApply loanApply : loanApplies) {
            HandoverLoanVo vo = new HandoverLoanVo();
            vo.setBusinessId(loanApply.getId());
            vo.setPersonId(loanApply.getLoanUser());
            vo.setProcessNumber(loanApply.getProcessNumber());
            vo.setApplicationDate(loanApply.getCreateTime());
            vo.setPaymentTarget(loanApply.getPayerName());
            vo.setLoanAmount(loanApply.getLoanAmount().subtract(toCostAmount.getOrDefault(loanApply.getProcessInstanceId(), BigDecimal.ZERO)));
            vo.setProjectNo(loanApply.getBusinessNo());
            vo.setWriteOffAmount(verificationMap.getOrDefault(loanApply.getProcessInstanceId(), BigDecimal.ZERO));
            vo.setWriteOffAmount(BigDecimal.ZERO);
            vos.add(vo);
        }
        return vos;
    }

    /**
     * 获取借款流程 转费用总金额
     * @param loanProcessInstanceIdList 借款流程实例ID列表
     * @return 转费用总金额
     */
    private Map<String, BigDecimal> loanToCostAmount(List<String> loanProcessInstanceIdList) {

        if (loanProcessInstanceIdList == null || loanProcessInstanceIdList.isEmpty()) {
            return Collections.emptyMap();
        }

        List<LoanApply> loanApplies = baseMapper.selectList(new LambdaQueryWrapper<LoanApply>()
                .in(LoanApply::getParentProcessInstanceId, loanProcessInstanceIdList)
                .eq(LoanApply::getDelFlag, false));

        if (loanApplies.isEmpty()) {
            return Collections.emptyMap();
        }

        return loanApplies.stream()
                .filter(apply -> apply.getToCostAmount() != null)
                .collect(Collectors.groupingBy(
                        LoanApply::getParentProcessInstanceId,
                        Collectors.reducing(BigDecimal.ZERO, LoanApply::getToCostAmount, BigDecimal::add)
                ));
    }

}
