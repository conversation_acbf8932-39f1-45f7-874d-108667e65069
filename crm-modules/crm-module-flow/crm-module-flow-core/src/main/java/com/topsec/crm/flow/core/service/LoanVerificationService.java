package com.topsec.crm.flow.core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.topsec.crm.flow.api.dto.loan.loanVerification.LoanVerificationDTO;
import com.topsec.crm.flow.core.entity.LoanVerification;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface LoanVerificationService extends IService<LoanVerification> {

    /**
     * 根据借款流程实例ID查询核销列表
     */
    List<LoanVerificationDTO> getListByLoanProcessId(String loanProcessId);

    /**
     * 保存核销信息
     */
    Boolean saveVerification(LoanVerificationDTO loanVerification);

    /**
     * 修改核销信息
     */
    Boolean updateVerification(LoanVerificationDTO loanVerification);

    /**
     * 删除核销信息
     * @param verificationId 核销ID
     */
    Boolean deleteVerification(String verificationId);

    /**
     * 处理借款转费用办结数据
     * @param processInstanceId 借款流程实例ID
     * @param toCostAmount 转费用金额
     */
    Boolean handleToCostData(String processInstanceId, BigDecimal toCostAmount);

    /**
     * 根据流程实例ID列表获取核销金额
     */
    Map<String, BigDecimal> getAmountByProcessInstanceIds(List<String> processInstanceIdList);

}
