package com.topsec.crm.flow.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.topsec.crm.contract.api.RemoteContractExecuteService;
import com.topsec.crm.contract.api.RemoteContractReviewService;
import com.topsec.crm.contract.api.RemoteKeyIndustrySerialAssignService;
import com.topsec.crm.contract.api.entity.CrmContractBaseInfoVO;
import com.topsec.crm.contract.api.entity.CrmContractProductOwnVO;
import com.topsec.crm.contract.api.entity.CrmContractProductThirdVO;
import com.topsec.crm.contract.api.entity.contractexecute.CrmContractExecuteVO;
import com.topsec.crm.contract.api.entity.keyindustryproduct.CrmKeyIndustrySerialAssignVO;
import com.topsec.crm.contract.api.entity.response.CrmProductSnInDTO;
import com.topsec.crm.flow.api.dto.contractreview.ContractReviewMainBaseInfoDTO;
import com.topsec.crm.flow.api.dto.contractreview.productinfo.ContractProductOwnDTO;
import com.topsec.crm.flow.api.dto.contractreview.productinfo.ContractReviewSpecialCodeDTO;
import com.topsec.crm.flow.api.dto.contractreview.projectinfo.CrmProjectProductMaintainInvestmentVO;
import com.topsec.crm.flow.api.dto.contractreview.request.SpecialCodePageQuery;
import com.topsec.crm.flow.api.dto.contractreview.sninfo.ContractProductSnVO;
import com.topsec.crm.flow.api.dto.returnexchange.*;
import com.topsec.crm.flow.api.vo.handoverProcess.HandoverContractVo;
import com.topsec.crm.flow.core.entity.*;
import com.topsec.crm.flow.core.mapper.ContractReviewReturnExchangeMapper;
import com.topsec.crm.flow.core.mapstruct.ReturnExchangeConvertor;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.flow.core.util.PriceUtils;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.util.sql.SqlUtil;
import com.topsec.crm.framework.common.web.page.PageDomain;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.framework.common.web.page.TableSupport;
import com.topsec.crm.project.api.RemoteApprovalConfigService;
import com.topsec.crm.project.api.client.RemoteProjectDirectlyClient;
import com.topsec.crm.project.api.client.RemoteProjectProductOwnClient;
import com.topsec.crm.project.api.entity.*;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsNodeClient;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.vo.node.ApproveNode;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ContractReviewReturnExchangeServiceImpl extends ServiceImpl<ContractReviewReturnExchangeMapper, ContractReviewReturnExchange> implements ContractReviewReturnExchangeService {

    private final RemoteContractExecuteService contractExecuteService;
    private final RemoteContractReviewService remoteContractReviewService;
    private final RemoteProjectDirectlyClient remoteProjectDirectlyClient;
    private final ReturnExchangeProductService returnExchangeProductService;
    private final ReturnExchangeProductThirdService returnExchangeProductThirdService;
    private final ContractReviewSpecialCodeService contractReviewSpecialCodeService;
    private final RemoteApprovalConfigService remoteApprovalConfigService;
    private final RemoteProjectProductOwnClient ownClient;
    private final ContractReviewMainService contractReviewMainService;
    private final TfsNodeClient tfsNodeClient;
    private final RemoteKeyIndustrySerialAssignService remoteKeyIndustrySerialAssignService;

    @Override
    public List<ReturnExchangeProductVO> queryContractProductOwnList(String contractNumber) {
        // 先取这个合同是否是重点行业的备货合同
        List<ContractReviewMainBaseInfoDTO> baseInfoDTOS = contractReviewMainService.getByContractNumber(contractNumber);
        if (CollectionUtils.isEmpty(baseInfoDTOS)) {
            throw new CrmException("合同号有误");
        }
        // 是否进货合同
        Boolean isImportantContractIn = baseInfoDTOS.get(0).getIsImportantContractIn();

        // 有负行
        JsonObject<List<CrmContractProductOwnVO>> result = contractExecuteService.getProductOwnByContractNumber(contractNumber);
        List<CrmContractProductOwnVO> owns = result.getObjEntity();
        // a.产品行存在分摊的外包服务费； 20250527 产品确认不过滤了
        // b.租赁服务：租赁方案熊总还没确定，暂不处理（代码：6005003000002、6001002000002）
        // c.服务代码：【安服-重保服务：603005003002、6003005005002】、【安服-HW服务：6003005002002、6003030000002】、【安服-驻场服务：6001017000002、6003006001002、6003031000002、6003032000002】、【安服-安全运营服务：6003003006002】、【安服-通用安全服务：6003005001002、6003018000002、6004001011002、6005002001002】、【安服-安全咨询服务：6002004001002、6003001001002】、【售后-驻场服务：6001004002002】、【云服务-云WAF服务：5998010000201】
        owns = owns.stream().filter(own -> !ReturnExchangeProductServiceImpl.disableCode.contains(own.getStuffCode())).toList();

        if (CollectionUtils.isEmpty(owns)) {
            return Collections.emptyList();
        }

        Map<String, List<ContractProductSnVO>> snMapByContractNumber = new HashMap<>();

        // 取合同关联的序列号
        if (isImportantContractIn) {
            snMapByContractNumber = getImportantSnMapByContractNumber(contractNumber);

        } else {
            // 取out表
            snMapByContractNumber = contractExecuteService.getSnMapByContractNumber(contractNumber).getObjEntity();
        }
        Set<String> recordIds = owns.stream().map(CrmContractProductOwnVO::getProjectProductOwnId).collect(Collectors.toSet());
        List<ReturnExchangeProduct> oldByRecordIdBatch = returnExchangeProductService.getOldByRecordIdBatch(recordIds);
        Map<String, List<ReturnExchangeProduct>> oldByRecordId = oldByRecordIdBatch.stream().collect(Collectors.groupingBy(ReturnExchangeProduct::getRecordId));

        Map<String, List<ContractProductSnVO>> finalSnMapByContractNumber = snMapByContractNumber;
        return owns.stream().map(p0 -> {
            ReturnExchangeProductVO item = ReturnExchangeConvertor
                    .INSTANCE.contractToReturnExchangeProductVO(p0);
            item.setOldProductNum(item.getProductNum());
            item.setProductNum(null);
            // 已退数量
            List<ReturnExchangeProduct> returnExchangeProducts = oldByRecordId.get(p0.getProjectProductOwnId());
            item.setReturnNum(returnExchangeProducts == null ? 0 : returnExchangeProducts.stream().mapToInt(ReturnExchangeProduct::getProductNum).sum());
            Integer oldProductNum = item.getOldProductNum() == null ? 0 : item.getOldProductNum();
            // 负行 直接跳过
            if (oldProductNum < 0) {
                return null;
            }
            // 如果退完了 直接跳过
            if (item.getReturnNum() >= oldProductNum) {
                return null;
            }
            List<ContractProductSnVO> contractProductSnVOS = finalSnMapByContractNumber.get(p0.getId());
            if (CollectionUtils.isNotEmpty(contractProductSnVOS)) {
                List<ReturnExchangeSnVO> list = contractProductSnVOS.stream().map(p1 -> {
                    ReturnExchangeSnVO crmProjectProductSnVO = new ReturnExchangeSnVO();
                    crmProjectProductSnVO.setType(p1.getType());
                    crmProjectProductSnVO.setRecordId(p1.getProjectRecordId());
                    crmProjectProductSnVO.setPsn(p1.getSn());
                    return crmProjectProductSnVO;
                }).toList();
                item.setPsn(list);
            }
            return item;
        }).filter(Objects::nonNull).toList();
    }

    public Map<String, List<ContractProductSnVO>> getImportantSnMapByContractNumber(String contractNumber){
        // 取in表
        List<CrmContractBaseInfoVO> crmContractBaseInfoVOS = remoteContractReviewService.getByContractNumberBatch(List.of(contractNumber)).getObjEntity();
        // 合同服务的id
        Set<String> contractServiceId = ListUtils.emptyIfNull(crmContractBaseInfoVOS).stream().map(CrmContractBaseInfoVO::getId).collect(Collectors.toSet());
        List<CrmProductSnInDTO> returnChangeSnList = remoteKeyIndustrySerialAssignService.getReturnChangeSnList(contractServiceId).getObjEntity();;
        List<String> sns = ListUtils.emptyIfNull(returnChangeSnList).stream().map(CrmProductSnInDTO::getSn).toList();
        Map<String, CrmKeyIndustrySerialAssignVO.SerialNumberStateEnum> stateBySn = null;
        if (CollectionUtils.isEmpty(sns)) {
            stateBySn = Collections.emptyMap();
        } else {
            stateBySn = remoteKeyIndustrySerialAssignService.querySnState(sns).getObjEntity();
        }

        Map<String, CrmKeyIndustrySerialAssignVO.SerialNumberStateEnum> finalStateBySn = stateBySn;
        return ListUtils.emptyIfNull(returnChangeSnList).stream()
                .filter(inDTO -> {
                    String sn = inDTO.getSn();
                    CrmKeyIndustrySerialAssignVO.SerialNumberStateEnum serialNumberStateEnum = finalStateBySn.get(sn);
                    // 过滤掉已经出货的 等于 2 3 的都不要
                    return serialNumberStateEnum == null || serialNumberStateEnum.getCode() != 2 && serialNumberStateEnum.getCode() != 3;
                }).map(item -> HyperBeanUtils.copyProperties(item, ContractProductSnVO::new))
                .collect(Collectors.groupingBy(ContractProductSnVO::getContractRecordId));

    }

    @Override
    public List<ReturnExchangeProductThirdVO> queryContractProductThirdList(String contractNumber) {
        JsonObject<List<CrmContractProductThirdVO>> result = contractExecuteService.getProductThirdByContractNumber(contractNumber);
        List<CrmContractProductThirdVO> thirds = result.getObjEntity();
        Set<String> recordIds = thirds.stream().map(CrmContractProductThirdVO::getProjectProductThirdId).collect(Collectors.toSet());
        List<ReturnExchangeProductThird> oldByRecordIdBatch = returnExchangeProductThirdService.getOldByRecordIdBatch(recordIds);
        Map<String, List<ReturnExchangeProductThird>> oldByRecordId = oldByRecordIdBatch.stream().collect(Collectors.groupingBy(ReturnExchangeProductThird::getRecordId));

        return thirds.stream().map(item  -> {
            ReturnExchangeProductThirdVO vo = ReturnExchangeConvertor.INSTANCE.contractToReturnExchangeProductThirdVO(item);
            vo.setOldProductNum(vo.getProductNum());
            vo.setProductNum(null);
            // 已退数量
            List<ReturnExchangeProductThird> returnExchangeProducts = oldByRecordId.get(item.getProjectProductThirdId());
            vo.setReturnNum(returnExchangeProducts == null ? 0 : returnExchangeProducts.stream().mapToInt(ReturnExchangeProductThird::getProductNum).sum());
            Integer oldProductNum = vo.getOldProductNum() == null ? 0 : vo.getOldProductNum();
            // 负行 直接跳过
            if (oldProductNum < 0) {
                return null;
            }
            // 如果退完了 直接跳过
            if (vo.getReturnNum() >= oldProductNum) {
                return null;
            }
            return vo;
        }).filter(Objects::nonNull).toList();
    }

    @Override
    public ContractReviewReturnExchange returnExchangeContractInfo(String processInstanceId) {
        return HyperBeanUtils.copyProperties(getOne(new LambdaQueryWrapper<ContractReviewReturnExchange>()
                .eq(ContractReviewReturnExchange::getProcessInstanceId, processInstanceId)
                .last("limit 1")), ContractReviewReturnExchange::new);
    }

    @Override
    public Pair<BigDecimal, BigDecimal> getAmountByProcessInstanceId(String processInstanceId) {
        ContractReviewReturnExchange returnExchangeBaseVO = returnExchangeContractInfo(processInstanceId);
        BigDecimal contractAmount = returnExchangeBaseVO.getContractAmount();
        // 取本次退换货的金额
        BigDecimal oldProductAmount = returnExchangeProductService.getOldProductAmount(processInstanceId);
        BigDecimal newProductAmount = returnExchangeProductService.getNewProductAmount(processInstanceId);
        BigDecimal after = contractAmount.add(newProductAmount).subtract(oldProductAmount);
        return Pair.of(contractAmount, after);
    }


    @Override
    public boolean writeSpecialCode(String processInstanceId) {
        // 退换货新产品
        List<ReturnExchangeProduct> owns = returnExchangeProductService.listNewProductByProcessInstanceId(processInstanceId);
        if (CollectionUtils.isEmpty(owns)) {
            return true;
        }
        // 项目产品共用的退换货的产品id
        List<String> projectRecordIds = owns.stream().map(ReturnExchangeProduct::getRecordId).toList();
        List<ContractReviewSpecialCodeDTO> list = contractReviewSpecialCodeService.getInfoByReturnExchangeProcessInstanceId(processInstanceId);
        // 退换货主表
        ContractReviewReturnExchange contractReviewReturnExchange = this.returnExchangeContractInfo(processInstanceId);
        assert contractReviewReturnExchange != null;
        // 取合同id
        String contractId = contractReviewReturnExchange.getContractId();
        // 根据合同id获取项目id
        ContractReviewMainBaseInfoDTO contractReviewMainBaseInfoDTO = contractReviewMainService.getByContractId(contractId);
        assert contractReviewMainBaseInfoDTO != null;
        String projectId = contractReviewMainBaseInfoDTO.getProjectId();

        List<String> productOwnIds = owns.stream().map(ReturnExchangeProduct::getId).toList();
        // 需要删除的数据
        List<ContractReviewSpecialCodeDTO> deleteRows = list.stream().filter(contractReviewSpecialCode -> !productOwnIds.contains(contractReviewSpecialCode.getReturnProductOwnId())).toList();
        deleteRows.forEach(row -> contractReviewSpecialCodeService.update(new LambdaUpdateWrapper<ContractReviewSpecialCode>().set(ContractReviewSpecialCode::getDelFlag, 1)
                .eq(ContractReviewSpecialCode::getId, row.getId())));

        // 构建Map 方便判断原来是否存在这个产品
        Map<String, ContractReviewSpecialCodeDTO> specialCodeMap = ListUtils.emptyIfNull(list).stream().collect(Collectors.toMap(ContractReviewSpecialCodeDTO::getReturnProductOwnId, row -> row));

        List<CrmProjectProductOwnVO> result = ownClient.queryProjectProductOwnTiled(projectId, projectRecordIds).getObjEntity();
        Map<String, CrmProjectProductOwnVO> ownByRecordId = result.stream().collect(Collectors.toMap(CrmProjectProductOwnVO::getId, row -> row));

        // 根据物料代码代码算出合同中是否有需要发03J的代码
        owns.forEach(own -> {
            JsonObject<CrmSpecialCodeConfigVo> configByMaterialCode = remoteApprovalConfigService.findConfigByMaterialCode(own.getStuffCode(),1);
            if (!configByMaterialCode.isSuccess() || configByMaterialCode.getObjEntity() == null || CollectionUtils.isEmpty(configByMaterialCode.getObjEntity().getRelList())) {
                return;
            }
            List<CrmSpecialCodeConfigRelVo> relList = configByMaterialCode.getObjEntity().getRelList();
            if (CollectionUtils.isEmpty(relList)) {
                return;
            }
            // save or update 根据productId
            ContractReviewSpecialCodeDTO specialCodeRow = specialCodeMap.get(own.getId());
            if (specialCodeRow != null) {
                // 已经写过了 直接跳过
                return;
            }
            ContractReviewSpecialCode contractReviewSpecialCode = new ContractReviewSpecialCode();

            // 这个地方徐全良 提供的接口只有一个返回值 取第一个
            CrmSpecialCodeConfigRelVo crmSpecialCodeConfigRelVo = relList.get(0);
            // 写入合同评审特殊代码确认表

            contractReviewSpecialCode.setReturnExchangeProcessInstanceId(processInstanceId);
            contractReviewSpecialCode.setProductOwnId(own.getId());
            contractReviewSpecialCode.setStuffCode(own.getStuffCode());
            contractReviewSpecialCode.setProductName(own.getProductName());
            contractReviewSpecialCode.setApproveSpecialOperation(crmSpecialCodeConfigRelVo.getOperation());
            CrmProjectProductOwnVO productOwnVO = ownByRecordId.get(own.getRecordId());
            CrmProjectProductMaintainVO crmProjectProductMaintain;
            if (productOwnVO != null) {
                crmProjectProductMaintain = productOwnVO.getCrmProjectProductMaintain();
            } else {
                crmProjectProductMaintain = new CrmProjectProductMaintainVO();
            }
            if (crmSpecialCodeConfigRelVo.getOperation() == 1) {
                contractReviewSpecialCode.setEvaluationResult(Optional.ofNullable(crmProjectProductMaintain).map(CrmProjectProductMaintainVO::getEvaluation).orElse(null));
            }

            if (crmSpecialCodeConfigRelVo.getOperation() == 2) {
                contractReviewSpecialCode.setOnSiteSupportMode(Optional.ofNullable(crmProjectProductMaintain).map(CrmProjectProductMaintainVO::getSupportMode).orElse(null));
                contractReviewSpecialCode.setOperationMaintenanceWorkloadRatio(Optional.ofNullable(crmProjectProductMaintain).map(CrmProjectProductMaintainVO::getOperationMaintenanceRatio).orElse(null));
            }

            if (crmSpecialCodeConfigRelVo.getOperation() == 3) {
                contractReviewSpecialCode.setEvaluationResult(Optional.ofNullable(crmProjectProductMaintain).map(CrmProjectProductMaintainVO::getEvaluation).orElse(null));
                contractReviewSpecialCode.setRelatedDeptId(Optional.ofNullable(crmProjectProductMaintain).map(CrmProjectProductMaintainVO::getRelatedDeptId).orElse(null));
                contractReviewSpecialCode.setMaintainInvestment(HyperBeanUtils.copyListProperties(ListUtils.emptyIfNull(Optional.ofNullable(crmProjectProductMaintain).map(CrmProjectProductMaintainVO::getCrmProjectProductMaintainInvestment).orElse(Collections.emptyList())), CrmProjectProductMaintainInvestmentVO::new));
            }
            contractReviewSpecialCodeService.save(contractReviewSpecialCode);
        });
        return false;
    }

    @Override
    public TableDataInfo pageAfterSpecialCode(SpecialCodePageQuery query) {
        String processInstanceId = query.getProcessInstanceId();
        assert processInstanceId != null;
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        if (pageNum != null && pageSize != null) {
            String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
            PageHelper.startPage(pageNum, pageSize, orderBy);
        }
        List<ContractReviewSpecialCode> list = contractReviewSpecialCodeService.list(new LambdaQueryWrapper<ContractReviewSpecialCode>()
                .eq(ContractReviewSpecialCode::getReturnExchangeProcessInstanceId, processInstanceId)
                .and(ObjectUtils.isEmpty(query.getCondition()),
                        item -> item.like(ContractReviewSpecialCode::getStuffCode, query.getCondition())
                                .or()
                                .like(ContractReviewSpecialCode::getProductName, query.getCondition()))
                .eq(ContractReviewSpecialCode::getDelFlag, 0)
        );
        TableDataInfo tableDataInfo = new TableDataInfo();
        tableDataInfo.setTotalCount(new PageInfo<>(list).getTotal());
        List<String> ownIds = ListUtils.emptyIfNull(list).stream().map(ContractReviewSpecialCode::getReturnProductOwnId).toList();
        List<ReturnExchangeProduct> productInfoBatch = returnExchangeProductService.listNewProductByProcessInstanceId(processInstanceId);
        Map<String, ReturnExchangeProduct> productMap = ListUtils.emptyIfNull(productInfoBatch).stream().collect(Collectors.toMap(ReturnExchangeProduct::getId, item -> item));
        List<ContractReviewSpecialCodeDTO> result = HyperBeanUtils.copyListPropertiesByJackson(list, ContractReviewSpecialCodeDTO.class);
        ListUtils.emptyIfNull(result).forEach(item -> {
            item.setOwnInfo(HyperBeanUtils.copyProperties(productMap.get(item.getProductOwnId()), ContractProductOwnDTO::new));
            if(CollectionUtils.isNotEmpty(item.getMaintainInvestment())){
                item.getMaintainInvestment().sort(Comparator.comparing(CrmProjectProductMaintainInvestmentVO::getUserLevel));
            }
        });
        tableDataInfo.setList(result);
        return tableDataInfo;
    }

    @Override
    public boolean hasSpecialCode(String processInstanceId) {
        // 判断特殊代码表是否有对应的数据
        List<ContractReviewSpecialCode> list = contractReviewSpecialCodeService.list(new LambdaQueryWrapper<ContractReviewSpecialCode>()
                .eq(ContractReviewSpecialCode::getReturnExchangeProcessInstanceId, processInstanceId)
                .eq(ContractReviewSpecialCode::getDelFlag, 0)
        );
        return !org.springframework.util.CollectionUtils.isEmpty(list);
    }

    @Override
    public List<ContractReviewReturnExchange> getInProcessReturnExchange() {
        return list(new LambdaQueryWrapper<ContractReviewReturnExchange>()
                .eq(ContractReviewReturnExchange::getProcessState, 1)
                .eq(ContractReviewReturnExchange::getDelFlag, 0));
    }

    @Override
    public PageUtils<ReturnExchangeItem> page(ReturnExchangePageQuery query) {
        Page<ContractReviewReturnExchange> reviewReturnExchangePage = baseMapper.selectPage(new Page<>(query.getPageNum(), query.getPageSize()),
                new LambdaQueryWrapper<ContractReviewReturnExchange>()
                        .like(StringUtils.isNotBlank(query.getProcessNumber()), ContractReviewReturnExchange::getProcessNumber,
                                query.getProcessNumber())
                        .like(StringUtils.isNotBlank(query.getContractCompanyName()), ContractReviewReturnExchange::getContractCompanyName,
                                query.getContractCompanyName())
                        .like(StringUtils.isNotBlank(query.getFinalCustomerName()), ContractReviewReturnExchange::getFinalCustomerName,
                                query.getFinalCustomerName())
                        .in(CollectionUtils.isNotEmpty(query.getPersonIds()), ContractReviewReturnExchange::
                                getSaleId,query.getPersonIds())
                        .eq(StringUtils.isNotBlank(query.getType()), ContractReviewReturnExchange::getType, query.getType())
                        .eq(StringUtils.isNotBlank(query.getSaleDeptId()), ContractReviewReturnExchange::getSaleDeptId, query.getSaleDeptId())
                        .eq(StringUtils.isNotBlank(query.getSaleId()), ContractReviewReturnExchange::getSaleId, query.getSaleId())
                        .orderBy(true,false, ContractReviewReturnExchange::getCreateTime)
        );

        Set<String> processInstanceIds = ListUtils.emptyIfNull(reviewReturnExchangePage.getRecords())
                .stream().map(FlowBaseEntity::getProcessInstanceId).collect(Collectors.toSet());

        List<ReturnExchangeProduct> productList =  returnExchangeProductService
                .list(new QueryWrapper<ReturnExchangeProduct>()
                        .in(CollectionUtils.isNotEmpty(processInstanceIds), "process_instance_id",
                                processInstanceIds));
        List<ReturnExchangeProductThird> productThirdList = returnExchangeProductThirdService.list(new QueryWrapper<ReturnExchangeProductThird>()
                .in(CollectionUtils.isNotEmpty(processInstanceIds), "process_instance_id",
                        processInstanceIds));

        Map<String, Set<ApproveNode>> processNodeMap = Optional.ofNullable(tfsNodeClient.queryNodeByProcessInstanceIdList(new ArrayList<>(processInstanceIds)))
                .map(JsonObject::getObjEntity)
                .orElse(Collections.emptyMap());
        Map<String, List<ReturnExchangeProduct>> productByProcessInstanceId = ListUtils.emptyIfNull(productList).stream().collect(Collectors.groupingBy(ReturnExchangeProduct::getProcessInstanceId));
        Map<String, List<ReturnExchangeProductThird>> productThirdByProcessInstanceId = ListUtils.emptyIfNull(productThirdList).stream().collect(Collectors.groupingBy(ReturnExchangeProductThird::getProcessInstanceId));
        PageUtils<ContractReviewReturnExchange> contractReviewReturnExchangePageUtils = new PageUtils<>(reviewReturnExchangePage);


        return contractReviewReturnExchangePageUtils.convert(contractReviewReturnExchange -> {
            String processInstanceId = contractReviewReturnExchange.getProcessInstanceId();
            ReturnExchangeItem returnExchangeItem = ReturnExchangeConvertor.INSTANCE.toReturnExchangeItem(contractReviewReturnExchange);
            List<ReturnExchangeProduct> products = ListUtils.emptyIfNull(productByProcessInstanceId.get(processInstanceId));
            Map<Boolean, List<ReturnExchangeProduct>> partition = products.stream().collect(Collectors.partitioningBy(item -> {
                Integer type = item.getType();
                return Objects.equals(type, 4);
            }));
            List<ReturnExchangeProduct> newP = partition.get(true);
            List<ReturnExchangeProduct> oldP = partition.get(false);
            List<ReturnExchangeProductThird> productThirds = ListUtils.emptyIfNull(productThirdByProcessInstanceId.get(processInstanceId));
            Map<Boolean, List<ReturnExchangeProductThird>> partitionThird = productThirds.stream().collect(Collectors.partitioningBy(item -> {
                Integer type = item.getType();
                return Objects.equals(type, 4);

            }));
            List<ReturnExchangeProductThird> newThirdP = partitionThird.get(true);
            List<ReturnExchangeProductThird> oldThirdP = partitionThird.get(false);

            returnExchangeItem.setNewProductAmount(PriceUtils.getReturnExchangeTotalPrice(newP,newThirdP));
            returnExchangeItem.setReProductAmount(PriceUtils.getReturnExchangeTotalPrice(oldP,oldThirdP));

            Set<ApproveNode> approveNodes = processNodeMap.get(processInstanceId);
            returnExchangeItem.setApprovalNode(approveNodes);
            return returnExchangeItem;
        });
    }

    @Override
    public boolean isShowThreeProcurement(String processInstanceId, String personId) {
        // 当退换货合同为非SM和非产品线产品，且登录人为退换货发起人，显示第三方采购发起入口；
        ContractReviewReturnExchange contractReviewReturnExchange = returnExchangeContractInfo(processInstanceId);
        if (!contractReviewReturnExchange.getCreateUser().equals(personId)) {
            return false;
        }

        String contractNumber = contractReviewReturnExchange.getContractNumber();
        // 是否有新第三方产品
        List<ReturnExchangeProductThird> thirds = returnExchangeProductThirdService.listNewProductByProcessInstanceId(processInstanceId);
        // 取合同执行
        CrmContractExecuteVO crmContractExecuteVO = contractExecuteService.getByContractNumber(contractNumber).getObjEntity();
        Integer projectSource = crmContractExecuteVO.getProjectSource();
        if (projectSource != null && projectSource != 1) {
            // zd项目 没有是否涉密 只需要判断第三方产品是否为空就行 如果有第三方产品 返回true
            return CollectionUtils.isNotEmpty(thirds);
        }
        // 公司项目 取这个
        String contractId = crmContractExecuteVO.getContractId();
        List<CrmContractBaseInfoVO> baseInfos = remoteContractReviewService.getByContractIdBatch(List.of(contractId)).getObjEntity();
        CrmContractBaseInfoVO crmContractBaseInfoVO = baseInfos.stream().findFirst().orElseThrow(() -> new CrmException("合同不存在"));
        String projectId = crmContractBaseInfoVO.getProjectId();
        // 项目信息
        CrmProjectDirectlyVo projectDirectlyVo = Optional.ofNullable(remoteProjectDirectlyClient.getProjectInfo(projectId))
                .map(JsonObject::getObjEntity).orElse(new CrmProjectDirectlyVo());
        Integer smType = projectDirectlyVo.getSmType();
        if (smType == 1) {
            // 涉密
            return false;
        }
        // 非涉密 判断第三方产品是否为空
        return CollectionUtils.isNotEmpty(thirds);
    }

    @Override
    public List<ReturnExchangeDetailVO> queryCancelByContractNumber(List<String> contractNumbers) {
        if (CollectionUtils.isEmpty(contractNumbers)) {
            return Collections.emptyList();
        }
        return HyperBeanUtils.copyListProperties(list(new LambdaQueryWrapper<ContractReviewReturnExchange>()
                .in(ContractReviewReturnExchange::getContractNumber, contractNumbers)
                .eq(ContractReviewReturnExchange::getDelFlag, false)
                .eq(ContractReviewReturnExchange::getType, 2)
        ), ReturnExchangeDetailVO::new);
    }

    @Override
    public Boolean updateContractOwner(List<HandoverContractVo> handoverContractVo) {
        // key 人 value personVo
        Map<String, HandoverContractVo> handoverContractVoMap = new HashMap<>();
        Map<String, Set<String>> contractMap = handoverContractVo.stream().filter(item -> {
            if (StringUtils.isNotEmpty(item.getReceiverId())) {
                handoverContractVoMap.put(item.getReceiverId(), item);
                return true;
            }
            return false;
        }).collect(Collectors.groupingBy(HandoverContractVo::getReceiverId,
                Collectors.collectingAndThen(Collectors.toList(), vos -> vos.stream().map(HandoverContractVo::getContractNumber).filter(Objects::nonNull).collect(Collectors.toSet()))));

        // 循环
        if (MapUtils.isEmpty(contractMap)) {
            return true;
        }
        contractMap.forEach((id, contractNumbers) -> {
            if (CollectionUtils.isEmpty(contractNumbers)) {
                return;
            }
            HandoverContractVo contractVo = handoverContractVoMap.get(id);
            String receiverId = contractVo.getReceiverId();
            String receiverName = contractVo.getReceiverName();
            String deptId = contractVo.getDeptId();
            String deptName = contractVo.getDeptName();
            // update
            this.update(new LambdaUpdateWrapper<ContractReviewReturnExchange>()
                    .set(ContractReviewReturnExchange::getSaleId, receiverId)
                    .set(ContractReviewReturnExchange::getSaleName, receiverName)
                    .set(ContractReviewReturnExchange::getSaleDeptId, deptId)
                    .set(ContractReviewReturnExchange::getSaleDeptName, deptName)
                    .in(ContractReviewReturnExchange::getContractNumber, contractNumbers));
        });
        return true;
    }


}
