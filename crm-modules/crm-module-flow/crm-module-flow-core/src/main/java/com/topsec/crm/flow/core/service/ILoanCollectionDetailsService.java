package com.topsec.crm.flow.core.service;

import com.topsec.crm.flow.api.dto.loan.loanCollection.LoanCollectionMainQueryPageDTO;
import com.topsec.crm.flow.api.dto.loan.loanCollection.vo.LoanCollectionDetailsVO;
import com.topsec.crm.flow.api.dto.targetedinventorypreparation.vo.TargetedInventoryPreparationStockVO;
import com.topsec.crm.flow.core.entity.LoanCollectionDetails;
import com.baomidou.mybatisplus.extension.service.IService;
import com.topsec.crm.framework.common.util.PageUtils;

import java.util.List;
import java.util.Set;

/**
 *  服务类接口
 *
 * <AUTHOR>
 * @since 2025-07-28 18:58
 */
public interface ILoanCollectionDetailsService extends IService<LoanCollectionDetails> {

    PageUtils<LoanCollectionDetailsVO> Page(LoanCollectionMainQueryPageDTO loanCollectionMainQueryPageDTO, Set<String> personIdsOfPermission);

    List<LoanCollectionDetailsVO> export(LoanCollectionMainQueryPageDTO loanCollectionMainQueryPageDTO, Set<String> personIdsOfPermission);
}
