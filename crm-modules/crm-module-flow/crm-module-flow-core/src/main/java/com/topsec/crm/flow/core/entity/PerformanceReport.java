package com.topsec.crm.flow.core.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.topsec.crm.framework.common.bean.AgentPrePaymentVerificationVO;
import com.topsec.crm.flow.api.dto.performancereport.PerformanceReportRefuseOrderReasonsDTO;
import com.topsec.crm.flow.core.util.RefuseOrderReasonsListTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 业绩上报主表
 * @TableName performance_report
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="performance_report",autoResultMap = true)
@Data
public class PerformanceReport extends FlowBaseEntity implements Serializable {
    /**
     *
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 流程id
     */
    private String processInstanceId;

    /**
     * 流程编号
     */
    private String processNumber;

    /**
     * 流程状态
     */
    private Integer processState;

    /**
     * 项目ID
     */
    private String projectId;

    // @ApiModelProperty("项目类型（1：直签项目，2：渠道项目）")
    private Integer projectType;
    // @Schema(description = "是否样机  0：否；1：是")
    private Integer modelMachine;
    //    @ApiModelProperty("供货商类型 1-国代 2-省代")
    private Integer supplierType;
    /**
     * 供货商ID
     */
    private String supplierId;

    /**
     * 供货商名称
     */
    private String supplierName;

    /**
     *  签约模式 0-我们直签 1-支持渠道
     */
    private Integer channelType;

    /**
     * 报备编号
     */
    private String reportNumber;

    /**
     * 销售人员ID
     */
    private String saleId;

    /**
     * 签约代理公司ID
     */
    private String channelCompanyId;

    /**
     * 签约代理公司
     */
    private String channelCompanyName;

    /**
     * 授信级别
     */
    private String creditLevel;

    /**
     * 首付比例
     */
    private BigDecimal paymentRatio;

    /**
     * 申请账期-(月)
     */
    private Float timeLimit;
    /**
     * 签约方注册地址
     */
    private String channelRegisterAddress;
    /**
     * 统一信用代码
     */
    private String channelUnifiedCode;
    /**
     * 签约方联系人
     */
    private String channelContactName;

    /**
     * 签约方电话
     */
    private String channelPhone;
    /**
     * 签约方联系人Id
     */
    private String channelContactId;

    /**
     * 签约方联系人的联系方式
     */
    private String channelContactWay;
    /**
     * 签约方联系人的邮箱
     */
    private String channelContactEmail;
    /**
     * 签约方联系人的通讯地址
     */
    private String channelContactAddress;
    /**
     * 签约方开户行
     */
    private String channelBankName;

    /**
     * 签约方银行账号
     */
    private String channelBankAccount;

    /**
     * 客户ID
     */
    private String customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 一级行业
     */
    private String firstIndustry;

    /**
     * 二级行业
     */
    private String secondIndustry;

    /**
     * 省份ID
     */
    private String provinceId;

    /**
     * 城市ID
     */
    private String cityId;

    /**
     * 客户地址
     */
    private String customerAddress;

    /**
     * 客户联系人ID
     */
    private String customerContactId;

    // @ApiModelProperty(value = "客户联系人名称")   @NotEmpty
    private String customerContactName;
    /**
     * 区县ID
     */
    private String countyId;

    /**
     * 应付款金额--预付款总额，不可编辑
     */
    private BigDecimal totalAmount;

    /**
     * 可用预付款
     */
    private BigDecimal prepaidAmount;

    /**
     * 使用的预付金额（本次使用预付款）
     */
    private BigDecimal usedPrepaidAmount;


    /**
     * 是否接单 0：否；1：是
     */
    private Integer acceptOrder;

    /**
     * 是否使用返点代替OS消耗 0：否；1：是
     */
    private Integer rebateReplaceOs;

    /**
     * 供应商名称;拒绝接单类型 1：特价订单 2：账期订单;拒绝接单原因
     */
    @TableField(typeHandler = RefuseOrderReasonsListTypeHandler.class)
    private List<PerformanceReportRefuseOrderReasonsDTO> refuseOrderReasons;

    /**
     * 业绩上报状态 0-申请 1-生效 2-拒单 3-作废
     */
    private Integer reportStatus;

    /**
     * 生效时间
     */
    private LocalDateTime effectiveTime;

    /**
     * 签订合同号
     */
    private String signingContractNumber;

    /**
     * 审批通过时间
     */
    private LocalDateTime processPassTime;

    /**
     * 合同额
     */
    private BigDecimal contractAmount;

    // @Schema(description = " 合同币种 0-人民币 1-港币 2-美元")
    private Integer contractCurrency;
    /**
     * 负责人id
     */
    private String ownId;
    /**
     * 负责人名字
     */
    private String ownName;

    /**
     * 删除标记 0-未删除 1-已删除
     */
    @TableLogic
    private Integer delFlag;


    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<AgentPrePaymentVerificationVO> prePaymentVerificationList;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
