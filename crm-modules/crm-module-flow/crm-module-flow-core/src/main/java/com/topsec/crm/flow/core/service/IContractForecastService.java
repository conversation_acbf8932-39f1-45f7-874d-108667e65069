package com.topsec.crm.flow.core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.topsec.crm.flow.core.entity.ContractForecast;
import com.topsec.crm.flow.core.entity.ContractForecastStatistics;
import com.topsec.crm.flow.core.entity.ContractForecastUserProjectProduct;
import com.topsec.crm.stats.api.entity.home.HomeRankContractForecastVO;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-26
 */
public interface IContractForecastService extends IService<ContractForecast> {

    /**
     * 发送合同预测流程
     * @param month
     * @return
     */
    Boolean sendContractForecastFlow(String month);

    /**
     * 查询合同预测流程详情
     * @param processInstanceId
     * @return
     */
    ContractForecast contractForecastDetail(String processInstanceId);

    /**
     * 获取合同预测图表数据
     * @param processInstanceId
     * @return
     */
    ContractForecastStatistics contractForecastChartData(String processInstanceId);

    /**
     * 修改项目产品的预测数据
     * @param productForecastData
     * @param processInstanceId
     * @return
     */
    Boolean updateProductForecastData(List<ContractForecastUserProjectProduct> productForecastData, String processInstanceId);

    /**
     * 获取合同预测未办结排名
     * @param type
     * @return
     */
    List<HomeRankContractForecastVO> unfinishedContractForecastRank(Integer type);

    /**
     * 自动完成合同预测流程01
     * @param month
     * @return
     */
    Boolean autoFinishContractForecastFlow01(List<String> month);

    /**
     * 自动完成合同预测流程02
     * @param month
     * @return
     */
    Boolean autoFinishContractForecastFlow02(List<String> month);
}
