package com.topsec.crm.flow.core.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.topsec.crm.agent.api.RemoteAgentService;
import com.topsec.crm.agent.api.RemoteCrmAgentAuthenticationService;
import com.topsec.crm.agent.api.entity.*;
import com.topsec.crm.contract.api.RemoteContractExecuteService;
import com.topsec.crm.contract.api.RemoteCrmAgentPaymentVerificationService;
import com.topsec.crm.contract.api.RemoteCrmPerformanceReportService;
import com.topsec.crm.contract.api.entity.crmagentpayment.agentpaymentverification.PrepaymentSummaryVO;
import com.topsec.crm.flow.api.dto.agentauthentication.*;
import com.topsec.crm.flow.api.enums.AreaEnum;
import com.topsec.crm.flow.api.vo.sealApplication.SealApplicationInfoVO;
import com.topsec.crm.flow.core.entity.*;
import com.topsec.crm.flow.core.mapper.*;
import com.topsec.crm.flow.core.mapstruct.AgentAuthenticationConvertor;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.framework.common.bean.AddressVO;
import com.topsec.crm.framework.common.bean.AreaVO;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.enums.AgentEnum;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.name.NameUtils;
import com.topsec.crm.framework.common.util.CommonUtils;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.operation.api.RemoteAreaService;
import com.topsec.crm.operation.api.RemoteIndustryService;
import com.topsec.crm.operation.api.entity.CrmIndustryVO;
import com.topsec.crm.project.api.RemoteProjectAgentDirectorConfigService;
import com.topsec.crm.project.api.client.RemoteProjectAgentClient;
import com.topsec.crm.project.api.client.RemoteProjectDirectlyClient;
import com.topsec.crm.project.api.entity.CrmProjectAgentDirectorConfigVO;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.api.client.TosEmployeeClient;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.tos.common.vo.EmployeeVO;
import com.topsec.tos.common.vo.process.DetailBaseVO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.topsec.jwt.UserInfoHolder.getCurrentAgentId;

@Service
@RequiredArgsConstructor
public class AgentAuthenticationServiceImpl extends ServiceImpl<AgentAuthenticationMapper, AgentAuthentication> implements AgentAuthenticationService {
    private final AgentAuthenticationMapper agentAuthenticationMapper;
    private final RemoteAreaService remoteAreaService;
    private final AgentAuthenticationAreaService agentAuthenticationAreaService;
    private final AgentAuthenticationIndustryService agentAuthenticationIndustryService;
    private final AgentAuthenticationRegionMapper agentAuthenticationRegionMapper;
    private final AgentAuthenticationTaskAmountMapper agentAuthenticationTaskAmountMapper;
    private final TosEmployeeClient tosEmployeeClient;
    private final RemoteAgentService remoteAgentService;
    private final AgentAuthenticationTechnicianMapper agentAuthenticationTechnicianMapper;
    private final AgentAuthenticationRegCapitalMapper agentAuthenticationRegCapitalMapper;
    private final AgentAuthenticationExchangeRateMapper agentAuthenticationExchangeRateMapper;
    private final RemoteIndustryService remoteIndustryService;
    private final AgentAuthenticationDocService agentAuthenticationDocService;
    private final RemoteCrmPerformanceReportService remoteCrmPerformanceReportService;
    private final PerformanceReportMapper performanceReportMapper;
    private final RemoteProjectAgentClient remoteProjectAgentClient;
    private final RemoteProjectDirectlyClient remoteProjectDirectlyClient;
    private final RemoteCrmAgentAuthenticationService remoteCrmAgentAuthenticationService;
    private final SealApplicationService sealApplicationService;
    private final RemoteProjectAgentDirectorConfigService remoteProjectAgentDirectorConfigService;
    private final PerformanceExecuteService performanceExecuteService;
    private final RemoteContractExecuteService remoteContractExecuteService;

    private final RemoteCrmAgentPaymentVerificationService remoteCrmAgentPaymentVerificationService;

    @Override
    public AgentAuthenticationDetailVO selectAgentAuthenticationDetailByProcessInstanceId(String processInstanceId) {
        AgentAuthentication agentAuthentication = getOne(new QueryWrapper<AgentAuthentication>().eq("process_instance_id", processInstanceId).eq("del_flag", 0));
        if (agentAuthentication == null) {
            throw new CrmException("该流程不存在");
        }
        CrmAgentVo crmAgentVo = remoteAgentService.getAgentInfo(agentAuthentication.getAgentId()).getObjEntity();
        AgentAuthenticationDetailVO agentAuthenticationDetailVO = queryAgentAuthenticationInfoByProcessInstanceId(processInstanceId);
        agentAuthenticationDetailVO.setCrmAgentVo(crmAgentVo);
        // 印鉴申请列表
        List<SealApplicationInfoVO> sealApplicationInfoVOS = sealApplicationService.listSealInfoByParentProcessInstanceId(processInstanceId);
        if (CollectionUtils.isNotEmpty(sealApplicationInfoVOS)){
            agentAuthenticationDetailVO.setSealApplicationInfoVOS(sealApplicationInfoVOS);
        }
        return agentAuthenticationDetailVO;
    }

    @Override
    public AgentAuthenticationDetailVO selectCrmAgentAuthenticationDetailByProcessInstanceId(String processInstanceId, String agentId) {
        AgentAuthentication agentAuthentication = getOne(new QueryWrapper<AgentAuthentication>().eq("process_instance_id", processInstanceId).eq("del_flag", 0));
        if (agentAuthentication == null) {
            throw new CrmException("该流程不存在");
        }
        // 权限
        if (!agentId.equals(agentAuthentication.getAgentId())) {
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }

        AgentAuthenticationDetailVO agentAuthenticationDetailVO = queryAgentAuthenticationInfoByProcessInstanceId(processInstanceId);
        // 印鉴申请列表
        List<SealApplicationInfoVO> sealApplicationInfoVOS = sealApplicationService.listSealInfoByParentProcessInstanceId(processInstanceId);
        if (CollectionUtils.isNotEmpty(sealApplicationInfoVOS)){
            agentAuthenticationDetailVO.setSealApplicationInfoVOS(sealApplicationInfoVOS);
        }
        return agentAuthenticationDetailVO;
    }

    /**
     * 渠道认证详情
     */
    @Override
    public AgentAuthenticationDetailVO queryAgentAuthenticationInfoByProcessInstanceId(String processInstanceId) {
        AgentAuthenticationDetailVO agentAuthenticationDetailVO = new AgentAuthenticationDetailVO();
        // 渠道认证信息
        AgentAuthentication agentAuthentication = getOne(new QueryWrapper<AgentAuthentication>().eq("process_instance_id", processInstanceId).eq("del_flag", 0));
        AgentAuthenticationFlowLaunchDTO agentAuthenticationFlowLaunchDTO = HyperBeanUtils.copyProperties(agentAuthentication, AgentAuthenticationFlowLaunchDTO::new);
        // 赋值 创建人修改人姓名
        NameUtils.setName(agentAuthenticationFlowLaunchDTO);
        if (agentAuthenticationFlowLaunchDTO.getGeneralAgentId() != null){
            agentAuthenticationFlowLaunchDTO.setGeneralAgentLevel(remoteAgentService.getAgentInfo(agentAuthenticationFlowLaunchDTO.getGeneralAgentId()).getObjEntity().getLevel());
        }
        // 渠道-欠款金额 超期应收
        AgentArrearsAmountVO agentArrearsAmountVO = selectAgentArrearsAmount(agentAuthentication.getAgentId());
        agentAuthenticationFlowLaunchDTO.setAgentArrearsAmountVO(agentArrearsAmountVO);
        // 认证代理区域
        List<AgentAuthenticationArea> authenticationAreas = agentAuthenticationAreaService.list(new QueryWrapper<AgentAuthenticationArea>().eq("process_instance_id", processInstanceId));
        List<AgentAuthenticationAreaDTO> agentAuthenticationAreaDTOS = HyperBeanUtils.copyListPropertiesByJackson(authenticationAreas, AgentAuthenticationAreaDTO.class);
        List<AreaVO> areaVOList = remoteAreaService.queryByCodes(authenticationAreas.stream().map(AgentAuthenticationArea::getAreaCode).collect(Collectors.toList())).getObjEntity();
        AddressVO addressVO = remoteAreaService.queryAreaChain(authenticationAreas.get(0).getAreaCode()).getObjEntity();
        for (AgentAuthenticationAreaDTO authenticationArea : agentAuthenticationAreaDTOS) {
            // 代理区域
            if (authenticationArea.getAreaType() == 1){
                authenticationArea.setAreaName(areaVOList.stream().filter(i -> Objects.equals(i.getCode(), authenticationArea.getAreaCode())).map(AreaVO::getName).findFirst().orElse(""));
            }else if (authenticationArea.getAreaType() == 2){// 市
                authenticationArea.setAreaName(addressVO.getProvinceName() + areaVOList.stream().filter(i -> Objects.equals(i.getCode(), authenticationArea.getAreaCode())).map(AreaVO::getName).findFirst().orElse(""));
            }else if (authenticationArea.getAreaType() == 3){// 区县
                authenticationArea.setAreaName(addressVO.getProvinceName() + addressVO.getCityName() + areaVOList.stream().filter(i -> Objects.equals(i.getCode(), authenticationArea.getAreaCode())).map(AreaVO::getName).findFirst().orElse(""));
            }
        }
        agentAuthenticationFlowLaunchDTO.setAgentAuthenticationAreaDTOS(agentAuthenticationAreaDTOS);
        // 代理行业
        List<AgentAuthenticationIndustry> authenticationIndustries = agentAuthenticationIndustryService.list(new QueryWrapper<AgentAuthenticationIndustry>().eq("process_instance_id", processInstanceId));
        List<AgentAuthenticationIndustryDTO> agentAuthenticationIndustryDTOS = HyperBeanUtils.copyListPropertiesByJackson(authenticationIndustries, AgentAuthenticationIndustryDTO.class);
        List<CrmIndustryVO> crmIndustryVOS = remoteIndustryService.industyByUuids(agentAuthenticationIndustryDTOS.stream().map(AgentAuthenticationIndustryDTO::getIndustryId).collect(Collectors.toList())).getObjEntity();
        List<CrmIndustryVO> crmIndustryVOS1 = remoteIndustryService.industyByUuids(crmIndustryVOS.stream().map(CrmIndustryVO::getPid).collect(Collectors.toList())).getObjEntity();
        for (AgentAuthenticationIndustryDTO agentAuthenticationIndustryDTO : agentAuthenticationIndustryDTOS) {
            // 二级行业
            CrmIndustryVO crmIndustryVO = crmIndustryVOS.stream().filter(i -> Objects.equals(i.getUuid(), agentAuthenticationIndustryDTO.getIndustryId())).findFirst().orElse(null);
            if (agentAuthenticationIndustryDTO.getIndustryType() == 2){
                agentAuthenticationIndustryDTO.setIndustryName(crmIndustryVOS1.stream().filter(i -> Objects.equals(i.getUuid(), crmIndustryVO.getPid())).map(CrmIndustryVO::getName).findFirst().orElse("") + "-" + crmIndustryVO.getName());
            }else {
                agentAuthenticationIndustryDTO.setIndustryName(crmIndustryVO.getName());
            }
        }
        agentAuthenticationFlowLaunchDTO.setAgentAuthenticationIndustryDTO(agentAuthenticationIndustryDTOS);
        // 渠道联系人
        CrmAgentContactsVo agentContactsVo = remoteAgentService.getInfo(agentAuthenticationFlowLaunchDTO.getAgentContactId()).getObjEntity();
        agentAuthenticationFlowLaunchDTO.setCrmAgentContactsVo(agentContactsVo);
        // 上传文件
        List<AgentAuthenticationDoc> authenticationDocs = agentAuthenticationDocService.list(new QueryWrapper<AgentAuthenticationDoc>().eq("process_instance_id", processInstanceId));
        agentAuthenticationFlowLaunchDTO.setAgentAuthenticationDocDTOS(HyperBeanUtils.copyListPropertiesByJackson(authenticationDocs, AgentAuthenticationDocDTO.class));
        agentAuthenticationDetailVO.setAgentAuthenticationFlowLaunchDTO(agentAuthenticationFlowLaunchDTO);
        // 代理任务
        AgentAuthenticationTaskVO agentAuthenticationTaskVO = selectAgentSalesAmount(agentAuthentication);
        agentAuthenticationDetailVO.setAgentAuthenticationTaskVO(agentAuthenticationTaskVO);
        // 认证省代显示 当年任务完成详情
        if (Objects.equals(agentAuthentication.getAgentClassification(), AgentEnum.AgentLevelEnum.PROVINCIAL_DISTRIBUTOR.getLevel())){
            AgentAuthenticationTaskResponse agentAuthenticationTaskResponse = selectAgentAuthenticationTaskResponse(agentAuthentication);
            agentAuthenticationDetailVO.setAgentAuthenticationTaskResponse(agentAuthenticationTaskResponse);
        }
        // 历史认证信息
        List<AgentAuthenticationManageVO> agentAuthenticationManageVOS = agentAuthenticationMapper.selectHistoryAgentAuthentication(agentAuthenticationFlowLaunchDTO.getAgentId(), processInstanceId);
        ListUtils.emptyIfNull(agentAuthenticationManageVOS).forEach(agentAuthenticationManageVO1 -> {
            // 代理任务
            AgentAuthentication agentAuthenticationHistory = getOne(new QueryWrapper<AgentAuthentication>().eq("process_instance_id", agentAuthenticationManageVO1.getProcessInstanceId()).eq("del_flag", 0));
            agentAuthenticationManageVO1.setAgentAuthenticationTaskVO(selectAgentSalesAmount(agentAuthenticationHistory));
        });
        // 赋值 创建人修改人姓名
        NameUtils.setName(agentAuthenticationManageVOS);
        agentAuthenticationDetailVO.setAgentAuthenticationManageVOS(agentAuthenticationManageVOS);
        processFiles(authenticationDocs,agentAuthenticationFlowLaunchDTO);

        return agentAuthenticationDetailVO;

    }

    private void processFiles(List<AgentAuthenticationDoc> authenticationDocs,AgentAuthenticationFlowLaunchDTO launchDTO) {
        //附件给上传人赋值
        List<String> empIdList = authenticationDocs.stream().map(AgentAuthenticationDoc::getCreateUser).toList();
        Map<String, String> empNameMap = Optional.ofNullable(tosEmployeeClient.findByIds(empIdList))
                .map(JsonObject::getObjEntity)
                .orElse(Collections.emptyList())
                .stream().collect(Collectors.toMap(DetailBaseVO::getUuid, v -> {
                    return v.getName() + v.getJobNo();
                }));
        for (AgentAuthenticationDoc doc : authenticationDocs) {
            doc.setCreateUser(empNameMap.get(doc.getCreateUser()));
        }
        List<AgentAuthenticationDocDTO> agentAuthenticationDocDTOS = AgentAuthenticationConvertor.INSTANCE.toDocDTOList(authenticationDocs);

        Map<Integer, List<AgentAuthenticationDocDTO>> docMap = agentAuthenticationDocDTOS.stream().collect(Collectors.groupingBy(v -> {
            String type = v.getType();
            if ("授权证书".equals(type) || "协议原件".equals(type)) {
                return 3;
            } else if ("付款凭证".equals(type) || "盖章协议".equals(type)) {
                return 2;
            } else {
                return 1;
            }
        }));
        launchDTO.setAgentAuthenticationDocDTOS(docMap.get(1));
        launchDTO.setAgentAuthenticationPaymentDocs(docMap.get(2));
        launchDTO.setAgentAuthenticationAuthorizationDocs(docMap.get(3));
    }

    /**
     * 查询代理期销售额
     */
    public AgentAuthenticationTaskVO selectAgentSalesAmount(AgentAuthentication agentAuthentication){
        AgentAuthenticationTaskVO agentAuthenticationTaskVO = new AgentAuthenticationTaskVO();
        CrmAgentAuthenticationSalesAmount crmAgentAuthenticationSalesAmount = new CrmAgentAuthenticationSalesAmount();
        crmAgentAuthenticationSalesAmount.setAgentId(agentAuthentication.getAgentId());
        crmAgentAuthenticationSalesAmount.setAgentStartTime(agentAuthentication.getAgentStartTime());
        crmAgentAuthenticationSalesAmount.setAgentEndTime(agentAuthentication.getAgentEndTime());
        BigDecimal salesAmount = remoteCrmPerformanceReportService.selectAgentSalesAmount(crmAgentAuthenticationSalesAmount).getObjEntity();
        // 任务金额
        BigDecimal taskAmount = agentAuthentication.getTaskAmount();
        // 完成率
        BigDecimal completionRate = salesAmount.divide(taskAmount, 4, RoundingMode.HALF_UP);
        agentAuthenticationTaskVO.setTaskAmount(taskAmount);
        agentAuthenticationTaskVO.setSalesAmount(salesAmount);
        agentAuthenticationTaskVO.setCompletionRate(completionRate);
        return agentAuthenticationTaskVO;
    }

    public AgentAuthenticationTaskResponse selectAgentAuthenticationTaskResponse(AgentAuthentication agentAuthentication){
        AgentAuthenticationTaskResponse agentAuthenticationTaskResponse = new AgentAuthenticationTaskResponse();
        BigDecimal taskAmount = agentAuthentication.getTaskAmount();
        agentAuthenticationTaskResponse.setQ1TaskAmount(taskAmount.multiply(new BigDecimal("0.15")));
        agentAuthenticationTaskResponse.setQ2TaskAmount(taskAmount.multiply(new BigDecimal("0.20")));
        agentAuthenticationTaskResponse.setQ3TaskAmount(taskAmount.multiply(new BigDecimal("0.30")));
        agentAuthenticationTaskResponse.setQ4TaskAmount(taskAmount.multiply(new BigDecimal("0.35")));
        // 销售额
        CrmAgentAuthenticationSalesAmount crmAgentAuthenticationSalesAmount = new CrmAgentAuthenticationSalesAmount();
        crmAgentAuthenticationSalesAmount.setAgentId(agentAuthentication.getAgentId());
        crmAgentAuthenticationSalesAmount.setAgentStartTime(agentAuthentication.getAgentStartTime());
        JsonObject<List<CrmAgentSalesAmountQuery>> salesAmountJsonObject = remoteCrmPerformanceReportService.selectAgentQuarterSalesAmount(crmAgentAuthenticationSalesAmount);
        if (salesAmountJsonObject.isSuccess()&& Objects.nonNull(salesAmountJsonObject.getObjEntity())) {
            List<CrmAgentSalesAmountQuery> salesAmountList = salesAmountJsonObject.getObjEntity();
            List<BigDecimal> q1SalesAmount = salesAmountList.stream().filter(crmAgentSalesAmountQuery -> crmAgentSalesAmountQuery.getQuarter() == 1).map(CrmAgentSalesAmountQuery::getAmount).collect(Collectors.toList());
            List<BigDecimal> q2SalesAmount = salesAmountList.stream().filter(crmAgentSalesAmountQuery -> crmAgentSalesAmountQuery.getQuarter() == 2).map(CrmAgentSalesAmountQuery::getAmount).collect(Collectors.toList());
            List<BigDecimal> q3SalesAmount = salesAmountList.stream().filter(crmAgentSalesAmountQuery -> crmAgentSalesAmountQuery.getQuarter() == 3).map(CrmAgentSalesAmountQuery::getAmount).collect(Collectors.toList());
            List<BigDecimal> q4SalesAmount = salesAmountList.stream().filter(crmAgentSalesAmountQuery -> crmAgentSalesAmountQuery.getQuarter() == 4).map(CrmAgentSalesAmountQuery::getAmount).collect(Collectors.toList());

            agentAuthenticationTaskResponse.setQ1SellinAmount(q1SalesAmount.stream().reduce(BigDecimal.ZERO, BigDecimal::add));
            agentAuthenticationTaskResponse.setQ2SellinAmount(q2SalesAmount.stream().reduce(BigDecimal.ZERO, BigDecimal::add));
            agentAuthenticationTaskResponse.setQ3SellinAmount(q3SalesAmount.stream().reduce(BigDecimal.ZERO, BigDecimal::add));
            agentAuthenticationTaskResponse.setQ4SellinAmount(q4SalesAmount.stream().reduce(BigDecimal.ZERO, BigDecimal::add));

        }else {
            agentAuthenticationTaskResponse.setQ1SellinAmount(BigDecimal.ZERO);
            agentAuthenticationTaskResponse.setQ2SellinAmount(BigDecimal.ZERO);
            agentAuthenticationTaskResponse.setQ3SellinAmount(BigDecimal.ZERO);
            agentAuthenticationTaskResponse.setQ4SellinAmount(BigDecimal.ZERO);
        }
        BigDecimal q1CompletionRate = agentAuthenticationTaskResponse.getQ1SellinAmount().divide(agentAuthenticationTaskResponse.getQ1TaskAmount(), 4, RoundingMode.HALF_UP);
        BigDecimal q2CompletionRate = agentAuthenticationTaskResponse.getQ2SellinAmount().divide(agentAuthenticationTaskResponse.getQ2TaskAmount(), 4, RoundingMode.HALF_UP);
        BigDecimal q3CompletionRate = agentAuthenticationTaskResponse.getQ3SellinAmount().divide(agentAuthenticationTaskResponse.getQ3TaskAmount(), 4, RoundingMode.HALF_UP);
        BigDecimal q4CompletionRate = agentAuthenticationTaskResponse.getQ4SellinAmount().divide(agentAuthenticationTaskResponse.getQ4TaskAmount(), 4, RoundingMode.HALF_UP);

        agentAuthenticationTaskResponse.setQ1CompletionRate(q1CompletionRate);
        agentAuthenticationTaskResponse.setQ2CompletionRate(q2CompletionRate);
        agentAuthenticationTaskResponse.setQ3CompletionRate(q3CompletionRate);
        agentAuthenticationTaskResponse.setQ4CompletionRate(q4CompletionRate);
        return agentAuthenticationTaskResponse;
    }

    @Override
    public List<AgentClassificationVO> selectAgentClassification(String agentId) {
        CrmAgentVo crmAgentVo = remoteAgentService.getAgentInfo(agentId).getObjEntity();
        List<AgentEnum.AgentLevelEnum> AgentEnumList = Arrays.stream(AgentEnum.AgentLevelEnum.values()).filter(agentLevelEnum -> agentLevelEnum.getLevel() < 9).collect(Collectors.toList());
        // 渠道分类
        List<AgentClassificationVO> agentClassificationVOS = getAgentClassificationVOS(AgentEnumList);
        // 是否限制认证等级
        if (crmAgentVo.getIsLimitAuthenticationLevel() == 0){
            return agentClassificationVOS;
        }
        // 查询上一年认证记录
        AgentAuthentication agentAuthentication = agentAuthenticationMapper.selectAgentAuthenticationInfo(agentId);
        if (agentAuthentication == null){
            return agentClassificationVOS;
        }else {
            AgentAuthenticationTaskVO agentAuthenticationTaskVO = selectAgentSalesAmount(agentAuthentication);
            BigDecimal completionRate = agentAuthenticationTaskVO.getCompletionRate();
            // 去年认证铂金 金牌 银牌
            if (Objects.equals(agentAuthentication.getAgentClassification(), AgentEnum.AgentLevelEnum.PLATINUM_AGENT.getLevel())){
                if (completionRate.compareTo(BigDecimal.valueOf(0.2)) < 0){
                    agentClassificationVOS.remove(AgentEnum.AgentLevelEnum.PLATINUM_AGENT.getLevel());
                }
            }else if (Objects.equals(agentAuthentication.getAgentClassification(), AgentEnum.AgentLevelEnum.GOLD_AGENT.getLevel())){
                if (completionRate.compareTo(BigDecimal.valueOf(0.2)) < 0){
                    agentClassificationVOS.remove(AgentEnum.AgentLevelEnum.PLATINUM_AGENT.getLevel());
                    agentClassificationVOS.remove(AgentEnum.AgentLevelEnum.GOLD_AGENT.getLevel());
                }else if (completionRate.compareTo(BigDecimal.valueOf(0.8)) < 0){
                    agentClassificationVOS.remove(AgentEnum.AgentLevelEnum.PLATINUM_AGENT.getLevel());
                }
            }else if (Objects.equals(agentAuthentication.getAgentClassification(), AgentEnum.AgentLevelEnum.SILVER_AGENT.getLevel())){
                if (completionRate.compareTo(BigDecimal.valueOf(0.2)) < 0){
                    agentClassificationVOS.remove(AgentEnum.AgentLevelEnum.PLATINUM_AGENT.getLevel());
                    agentClassificationVOS.remove(AgentEnum.AgentLevelEnum.GOLD_AGENT.getLevel());
                    agentClassificationVOS.remove(AgentEnum.AgentLevelEnum.SILVER_AGENT.getLevel());
                }else if (completionRate.compareTo(BigDecimal.valueOf(0.8)) < 0){
                    agentClassificationVOS.remove(AgentEnum.AgentLevelEnum.PLATINUM_AGENT.getLevel());
                    agentClassificationVOS.remove(AgentEnum.AgentLevelEnum.GOLD_AGENT.getLevel());
                }else {
                    agentClassificationVOS.remove(AgentEnum.AgentLevelEnum.PLATINUM_AGENT.getLevel());
                }
            }else if (Objects.equals(agentAuthentication.getAgentClassification(), AgentEnum.AgentLevelEnum.PROVINCIAL_DISTRIBUTOR.getLevel())){
                if (completionRate.compareTo(BigDecimal.valueOf(0.5)) < 0){
                    agentClassificationVOS.remove(AgentEnum.AgentLevelEnum.PROVINCIAL_DISTRIBUTOR.getLevel());
                }
            }else if (Objects.equals(agentAuthentication.getAgentClassification(), AgentEnum.AgentLevelEnum.NATIONAL_STRATEGIC_PARTNER.getLevel()) ||
                    Objects.equals(agentAuthentication.getAgentClassification(), AgentEnum.AgentLevelEnum.REGIONAL_STRATEGIC_PARTNER.getLevel()) ||
                    Objects.equals(agentAuthentication.getAgentClassification(), AgentEnum.AgentLevelEnum.INDUSTRY_STRATEGIC_PARTNER.getLevel())){
                if (completionRate.compareTo(BigDecimal.valueOf(0.2)) < 0){
                    agentClassificationVOS.remove(AgentEnum.AgentLevelEnum.NATIONAL_STRATEGIC_PARTNER.getLevel());
                    agentClassificationVOS.remove(AgentEnum.AgentLevelEnum.REGIONAL_STRATEGIC_PARTNER.getLevel());
                    agentClassificationVOS.remove(AgentEnum.AgentLevelEnum.INDUSTRY_STRATEGIC_PARTNER.getLevel());
                }
            }
        }
        return agentClassificationVOS;
    }

    @Override
    public List<AgentClassificationVO> selectManageListAgentClassification() {
        // 渠道分类
        List<AgentEnum.AgentLevelEnum> AgentEnumList = Arrays.stream(AgentEnum.AgentLevelEnum.values()).filter(agentLevelEnum -> agentLevelEnum.getLevel() != 9 && agentLevelEnum.getLevel() != 10).collect(Collectors.toList());
        return getAgentClassificationVOS(AgentEnumList);
    }

    @Override
    public List<AgentClassificationVO> selectAgentAuthenticationClassification() {
        List<AgentEnum.AgentLevelEnum> AgentEnumList = Arrays.stream(AgentEnum.AgentLevelEnum.values()).filter(agentLevelEnum -> agentLevelEnum.getLevel() < 9).collect(Collectors.toList());
        return getAgentClassificationVOS(AgentEnumList);
    }

    private List<AgentClassificationVO> getAgentClassificationVOS(List<AgentEnum.AgentLevelEnum> agentEnumList) {
        List<AgentClassificationVO> agentClassificationVOS = new ArrayList<>();
        agentEnumList.forEach(agentLevelEnum -> {
            AgentClassificationVO agentClassificationVO = new AgentClassificationVO();
            agentClassificationVO.setLevel(agentLevelEnum.getLevel());
            agentClassificationVO.setName(agentLevelEnum.getName());
            agentClassificationVOS.add(agentClassificationVO);
        });
        return agentClassificationVOS;
    }

    @Override
    public List<AreaVO> selectAgentAreaProvince(AgentAuthenticationQuery agentAuthenticationQuery) {
        CrmAgentVo crmAgentVo = remoteAgentService.getAgentInfo(agentAuthenticationQuery.getAgentId()).getObjEntity();
        if (queryArea(crmAgentVo.getProvinceCode())){
            return remoteAreaService.queryProvinceArea(false).getObjEntity();
        }else {
            return remoteAreaService.queryProvinceArea(true).getObjEntity();
        }
    }

    @Override
    public List<AreaVO> selectAgentAreaCity(AgentAuthenticationQuery agentAuthenticationQuery) {
        List<AreaVO> areaVOList;
        if (agentAuthenticationQuery.getAreaCode() != null){
            areaVOList = remoteAreaService.queryChildrenArea(agentAuthenticationQuery.getAreaCode()).getObjEntity();
        }else {
            throw new CrmException("地区不存在");
        }
        return areaVOList;
    }

    @Override
    public AgentAuthenticationTaskAmountDTO selectAgentTaskAmount(AgentAuthenticationQuery agentAuthenticationQuery) {
        // 任务金额
        AgentAuthenticationRegion authenticationRegion = agentAuthenticationRegionMapper.selectOne(new QueryWrapper<AgentAuthenticationRegion>().eq("area_code", agentAuthenticationQuery.getCityCode()));
        if (authenticationRegion == null){
            authenticationRegion = agentAuthenticationRegionMapper.selectOne(new QueryWrapper<AgentAuthenticationRegion>().eq("area_code", agentAuthenticationQuery.getAreaCode()));
        }
        if (authenticationRegion == null){
            throw new CrmException("代理区域任务金额为空，请联系管理员。");
        }
        AgentAuthenticationTaskAmount agentAuthenticationTaskAmount = agentAuthenticationTaskAmountMapper.selectOne(new QueryWrapper<AgentAuthenticationTaskAmount>().eq("agent_classification", agentAuthenticationQuery.getAgentClassification()).eq("region_name", authenticationRegion.getRegionName()));
        AgentAuthenticationTaskAmountDTO agentAuthenticationTaskAmountDTO = HyperBeanUtils.copyProperties(agentAuthenticationTaskAmount, AgentAuthenticationTaskAmountDTO::new);
        // 是否港澳台
        /*Boolean aBoolean = queryArea(agentAuthenticationQuery.getAreaCode());
        if (aBoolean && (Objects.equals(agentAuthenticationQuery.getAgentClassification(), AgentEnum.AgentLevelEnum.NATIONAL_DISTRIBUTOR.getLevel()) || Objects.equals(agentAuthenticationQuery.getAgentClassification(), AgentEnum.AgentLevelEnum.PROVINCIAL_DISTRIBUTOR.getLevel()))){
            agentAuthenticationTaskAmountDTO.setTaskAmount(BigDecimal.valueOf(10000000));
        }else */if (Objects.equals(agentAuthenticationQuery.getAgentClassification(), AgentEnum.AgentLevelEnum.REGIONAL_STRATEGIC_PARTNER.getLevel())){
            agentAuthenticationTaskAmountDTO.setTaskAmount(BigDecimal.valueOf(5000000));
        }
        return agentAuthenticationTaskAmountDTO;
    }

    @Override
    public List<AgentDirectorVO> selectAgentDirector(String agentId) {
        // 渠道信息
        CrmAgentVo crmAgentVo = remoteAgentService.getAgentInfo(agentId).getObjEntity();
        // 渠道总监
        CrmProjectAgentDirectorConfigVO objEntity = remoteProjectAgentDirectorConfigService.getAgentDirectorByAreaId(crmAgentVo.getProvinceCode()).getObjEntity();
        if (objEntity == null || CollectionUtils.isEmpty(objEntity.getApprover())){
            throw new CrmException("该渠道所在地区未配置渠道总监");
        }
        List<String> list = objEntity.getApprover().stream().map(CrmProjectAgentDirectorConfigVO.Person::getId).toList();
        List<EmployeeVO> employeeVOList = tosEmployeeClient.findByIdsByPost(list).getObjEntity();
        List<AgentDirectorVO> agentDirectorVOS = new ArrayList<>();
        ListUtils.emptyIfNull(employeeVOList).forEach(employeeVO -> {
            AgentDirectorVO agentDirectorVO = new AgentDirectorVO();
            agentDirectorVO.setUuid(employeeVO.getUuid());
            agentDirectorVO.setName(employeeVO.getName() + employeeVO.getJobNo());
            agentDirectorVO.setDepartmentVO(employeeVO.getDept());
            agentDirectorVO.setMobile(employeeVO.getMobile());
            agentDirectorVO.setEmail(employeeVO.getEmail());
            agentDirectorVOS.add(agentDirectorVO);
        });
        return agentDirectorVOS;
    }

    /**
     * 是否港澳台
     */
    public Boolean queryArea(String code){
        return Objects.equals(code, AreaEnum.XIANG_GANG.getName()) || Objects.equals(code, AreaEnum.AO_MEN.getName()) || Objects.equals(code, AreaEnum.TAI_WAN.getName());
    }

    @Override
    public boolean getAgentAreaInfo(String agentId) {
        AgentAuthentication agentAuthentication = agentAuthenticationMapper.selectAgentAuthenticationThisYear(agentId);
        if (agentAuthentication == null || agentAuthentication.getAgentClassification() != 2){
            return false;
        }
        // 认证代理区域
        List<AgentAuthenticationArea> authenticationAreas = agentAuthenticationAreaService.list(new QueryWrapper<AgentAuthenticationArea>().eq("process_instance_id", agentAuthentication.getProcessInstanceId()));
        AgentAuthenticationArea agentAuthenticationAreaDTO = authenticationAreas.get(0);
        AreaVO areaVO2 = remoteAreaService.queryByCode(agentAuthenticationAreaDTO.getAreaCode()).getObjEntity();
        // 上级地区
        AreaVO areaVO = remoteAreaService.queryParentArea(agentAuthenticationAreaDTO.getAreaCode()).getObjEntity();
        String code = "";
        if (agentAuthenticationAreaDTO.getAreaType() == 1) {
            code = areaVO2.getCode();
        } else if (agentAuthenticationAreaDTO.getAreaType() == 2) {// 市
            code = areaVO.getCode();
        } else if (agentAuthenticationAreaDTO.getAreaType() == 3) {// 区县
            AreaVO areaVO1 = remoteAreaService.queryParentArea(areaVO.getCode()).getObjEntity();
            code = areaVO1.getCode();
        }
        return Objects.equals(code, AreaEnum.XIANG_GANG.getName()) || Objects.equals(code, AreaEnum.AO_MEN.getName());

    }

    @Override
    public AgentAuthenticationTechnicianDTO selectAgentAuthenticationTechnician(AgentAuthenticationQuery agentAuthenticationQuery) {
        Boolean aBoolean = queryArea(agentAuthenticationQuery.getAreaCode());
        // 技术人员数量
        AgentAuthenticationTechnician agentAuthenticationTechnician;

        if (aBoolean){
            agentAuthenticationTechnician = agentAuthenticationTechnicianMapper.selectOne(new QueryWrapper<AgentAuthenticationTechnician>().eq("agent_classification", agentAuthenticationQuery.getAgentClassification()).in("area_type", 0, 2));
        }else {
            if (Objects.equals(agentAuthenticationQuery.getAgentClassification(), AgentEnum.AgentLevelEnum.PROVINCIAL_DISTRIBUTOR.getLevel())){
                // 省内所有省代
                List<CrmAgentAuthenticationVO> crmAgentAuthenticationVOS = remoteCrmAgentAuthenticationService.getGeneralAgentByCode(agentAuthenticationQuery.getAreaCode()).getObjEntity();
                agentAuthenticationTechnician = new AgentAuthenticationTechnician();
                if (crmAgentAuthenticationVOS.isEmpty()){
                    agentAuthenticationTechnician.setTspp(5);
                    agentAuthenticationTechnician.setTscp(3);
                }else {
                    agentAuthenticationTechnician.setTspp(2);
                    agentAuthenticationTechnician.setTscp(1);
                }
            }else {
                agentAuthenticationTechnician = agentAuthenticationTechnicianMapper.selectOne(new QueryWrapper<AgentAuthenticationTechnician>().eq("agent_classification", agentAuthenticationQuery.getAgentClassification()).in("area_type", 0, 1));

            }
        }
        return HyperBeanUtils.copyProperties(agentAuthenticationTechnician, AgentAuthenticationTechnicianDTO::new);
    }

    @Override
    public boolean selectAgentAuthenticationRegCapital(AgentAuthenticationQuery agentAuthenticationQuery) {
        AgentAuthenticationRegCapital authenticationRegCapital = agentAuthenticationRegCapitalMapper.selectOne(new QueryWrapper<AgentAuthenticationRegCapital>().eq("agent_classification", agentAuthenticationQuery.getAgentClassification()));
        CrmAgentVo crmAgentVo = remoteAgentService.getAgentInfo(agentAuthenticationQuery.getAgentId()).getObjEntity();
        JSONObject crmAgentBusinessInfoVo = crmAgentVo.getCrmAgentBusinessInfoVo();
        if (crmAgentBusinessInfoVo == null){
            throw new CrmException("渠道工商信息为空");
        }
        //String[] regCapital = crmAgentBusinessInfoVo.getRegCapital().split("元");
        //String[] actualCapital = crmAgentBusinessInfoVo.getActualCapital().split("元");
        AgentAuthenticationExchangeRate regCapitalCurrency = agentAuthenticationExchangeRateMapper.selectOne(new QueryWrapper<AgentAuthenticationExchangeRate>().eq("currency", crmAgentBusinessInfoVo.getString("regCapitalCurrency").trim()));
        AgentAuthenticationExchangeRate actualCapitalCurrency = agentAuthenticationExchangeRateMapper.selectOne(new QueryWrapper<AgentAuthenticationExchangeRate>().eq("currency", crmAgentBusinessInfoVo.getString("actualCapitalCurrency").trim()));

        if (regCapitalCurrency == null || actualCapitalCurrency == null){
            throw new CrmException("注册资金或实缴资金币种不存在");
        }


        BigDecimal regCapitalAmount = BigDecimal.valueOf(convertToAmount(crmAgentBusinessInfoVo.getString("regCapitalAmount"), crmAgentBusinessInfoVo.getString("regCapitalUnit"))*regCapitalCurrency.getExchangeRate());
        BigDecimal actualCapitalAmount = BigDecimal.valueOf(convertToAmount(crmAgentBusinessInfoVo.getString("actualCapitalAmount"), crmAgentBusinessInfoVo.getString("actualCapitalUnit"))*actualCapitalCurrency.getExchangeRate());

        if (regCapitalAmount.compareTo(authenticationRegCapital.getRegCapital()) < 0 || actualCapitalAmount.compareTo(authenticationRegCapital.getActualCapital()) < 0){
            throw new CrmException(crmAgentVo.getAgentName() + "的注册资金或实缴资金低于该认证级别的标准");
        }
        return true;
    }


    public static double convertToAmount(String actualCapitalAmount,String moneyStr) {
        double amount = 0.0;
        if (moneyStr == null || moneyStr.equals("") || moneyStr.equals("元")){
            amount = Double.parseDouble(actualCapitalAmount);
        } else if (moneyStr.contains("亿")) {
            amount = Double.parseDouble(actualCapitalAmount) * 100000000;
        } else if (moneyStr.contains("万")) {
            amount = Double.parseDouble(actualCapitalAmount) * 10000;
        } else{
            amount = Double.parseDouble(moneyStr);
        }
        return amount;
    }

    @Override
    public AgentArrearsAmountVO selectAgentArrearsAmount(String agentId) {
        AgentArrearsAmountVO agentArrearsAmountVO = new AgentArrearsAmountVO();
        if (StringUtils.isNotEmpty(getCurrentAgentId())){
            List<AgentAuthentication> agentAuthenticationList = list(new LambdaQueryWrapper<AgentAuthentication>().eq(AgentAuthentication::getAgentId, agentId).eq(AgentAuthentication::getDelFlag, 0).isNotNull(AgentAuthentication::getEffectiveTime));
            if (CollectionUtils.isNotEmpty(agentAuthenticationList)) {
                return performanceExecuteService.queryDebtAmountByAgentId(agentId);
            }
        }
        return agentArrearsAmountVO;
    }

    @Override
    public boolean selectAgentAdvancePaymentBalance(String processInstanceId) {
        AgentAuthentication agentAuthentication = getOne(new QueryWrapper<AgentAuthentication>().eq("process_instance_id", processInstanceId).eq("del_flag", 0));
        Integer agentClassification = agentAuthentication.getAgentClassification();
        AgentEnum.AgentLevelEnum agentLevelEnum = AgentEnum.AgentLevelEnum.fromLevel(agentClassification);
        //非金、金、银，直接返回true
        if (!(agentLevelEnum== AgentEnum.AgentLevelEnum.PLATINUM_AGENT||
                agentLevelEnum== AgentEnum.AgentLevelEnum.GOLD_AGENT||
                agentLevelEnum== AgentEnum.AgentLevelEnum.SILVER_AGENT)){
            return true;
        }
        // todo 查询渠道预付款余额
        BigDecimal agentAdvancePaymentBalance = BigDecimal.valueOf(1000000);
        if (agentAdvancePaymentBalance.compareTo(Optional.ofNullable(agentAuthentication.getAdvancePayment()).orElse(BigDecimal.ZERO)) < 0){
            throw new CrmException("抱歉，当前预付款余额不足，请及时缴纳预付款并发起付款申请");
        }
        return true;
    }

    @Override
    public List<String> selectAgentAuthenticationProvince(List<String> areaCodeList) {
        List<String> areaList = new ArrayList<>();
        for (String areaCode : areaCodeList) {
            AgentAuthentication agentAuthentication = agentAuthenticationMapper.selectAgentAuthenticationProvince(areaCode);
            if (agentAuthentication != null){
                areaList.add(remoteAreaService.queryByCode(areaCode).getObjEntity().getName());
            }
        }
        return areaList;
    }

    @Override
    public PageUtils<AgentPaymentDisburseVO> selectAgentPaymentDisburseList(AgentPaymentDisburseQuery agentPaymentDisburseQuery, DataScopeParam dataScopeParam) {
        agentPaymentDisburseQuery = selectAgentPaymentDisburseByDataScopeParam(agentPaymentDisburseQuery, dataScopeParam);
        // 判断发起人是渠道还是销售
        /*String agentId = getCurrentAgentId();
        if (StringUtils.isNotEmpty(agentId)) {
            agentPaymentDisburseQuery.setAgentId(agentId);
        }else {
            List<String> strings = new ArrayList<>();
            String currentPersonId = getCurrentPersonId();
            JsonObject<EmployeeVO> byId = tosEmployeeClient.findById(currentPersonId);
            if (byId.isSuccess()&& Objects.nonNull(byId.getObjEntity())) {
                strings.add(currentPersonId);
                strings.addAll(byId.getObjEntity().getSubordinates().stream().map(EmployeeVO::getUuid).toList());
                agentPaymentDisburseQuery.setSalesId(strings);
            }
        }*/

        List<AgentPaymentDisburseVO> agentPaymentDisburseVOS = agentAuthenticationMapper.selectAgentPaymentDisburseList(agentPaymentDisburseQuery);
        List<AgentPaymentDisburseVO> agentPaymentDisburseVOS1 = CommonUtils.subListPage(agentPaymentDisburseVOS, agentPaymentDisburseQuery.getPageSize(), agentPaymentDisburseQuery.getPageNum());

        return new PageUtils<>(agentPaymentDisburseVOS1, agentPaymentDisburseVOS.size(), agentPaymentDisburseQuery.getPageSize(), agentPaymentDisburseQuery.getPageNum());

    }

    public AgentPaymentDisburseQuery selectAgentPaymentDisburseByDataScopeParam(AgentPaymentDisburseQuery agentPaymentDisburseQuery, DataScopeParam dataScopeParam){
        Set<String> personIdsOfPermission = dataScopeParam != null ? dataScopeParam.getPersonIdList() : Collections.EMPTY_SET;
        String currentAgentId = dataScopeParam != null ? dataScopeParam.getAgentId() : null;

        if (StringUtils.isNotEmpty(currentAgentId)){
            CrmAgentVo crmAgentVo = remoteAgentService.getAgentInfo(currentAgentId).getObjEntity();
            if (crmAgentVo.getLevel() == 3 || crmAgentVo.getLevel() == 4 || crmAgentVo.getLevel() == 5){
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
            agentPaymentDisburseQuery.setAgentId(currentAgentId);
        }else if (CollectionUtils.isNotEmpty(personIdsOfPermission)){
            List<String> strings = new ArrayList<>(personIdsOfPermission);
            JsonObject<List<EmployeeVO>> byIds = tosEmployeeClient.findByIds(strings);
            if (byIds.isSuccess()&& Objects.nonNull(byIds.getObjEntity())) {
                for (EmployeeVO employeeVO : byIds.getObjEntity()) {
                    strings.addAll(ListUtils.emptyIfNull(employeeVO.getSubordinates()).stream().map(EmployeeVO::getUuid).toList());
                }
            }
            agentPaymentDisburseQuery.setSalesId(strings);
        }
        return agentPaymentDisburseQuery;
    }

    @Override
    public AgentPaymentDisburseVO selectAgentPaymentDisburseInfo(AgentPaymentDisburseQuery agentPaymentDisburseQuery, DataScopeParam dataScopeParam) {
        agentPaymentDisburseQuery = selectAgentPaymentDisburseByDataScopeParam(agentPaymentDisburseQuery, dataScopeParam);

        List<AgentPaymentDisburseVO> agentPaymentDisburseVOS = agentAuthenticationMapper.selectAgentPaymentDisburseList(agentPaymentDisburseQuery);
        if (agentPaymentDisburseVOS == null || agentPaymentDisburseVOS.isEmpty()){
            throw new CrmException("无数据");
        }
        AgentPaymentDisburseVO agentPaymentDisburseVO = agentPaymentDisburseVOS.get(0);
        // 获取回款核销中冻结预付款的总和
        JsonObject<PrepaymentSummaryVO> prepaymentSummaryVOJsonObject = remoteCrmAgentPaymentVerificationService.verificationCount(agentPaymentDisburseVO.getAgentName(), agentPaymentDisburseVO.getGeneralAgentName());
        if (prepaymentSummaryVOJsonObject.isSuccess() || ObjectUtils.isEmpty(prepaymentSummaryVOJsonObject.getObjEntity())){
            BigDecimal allFrezzAmount = prepaymentSummaryVOJsonObject.getObjEntity().getAllFrezzAmount();
            agentPaymentDisburseVO.setAllFrezzAmount(allFrezzAmount);
            agentPaymentDisburseVO.setAvailablePayment(agentPaymentDisburseVO.getAvailablePayment().subtract(allFrezzAmount));
        }
        //List<AgentPaymentDisburseVO.UseAdvancePaymentInfo> useAdvancePaymentInfos = new ArrayList<>();
//        List<PerformanceReport> performanceReports = performanceReportMapper.selectUsedAdvancePaymentInfo(agentPaymentDisburseQuery.getAgentName(), agentPaymentDisburseQuery.getGeneralAgentName());
//        ListUtils.emptyIfNull(performanceReports).forEach(performanceReport -> {
//            AgentPaymentDisburseVO.UseAdvancePaymentInfo useAdvancePaymentInfo = new AgentPaymentDisburseVO.UseAdvancePaymentInfo();
//            if (performanceReport.getProjectType() == 1){
//                CrmProjectDirectlyVo crmProjectDirectlyVo = remoteProjectDirectlyClient.getProjectInfo(performanceReport.getProjectId()).getObjEntity();
//                useAdvancePaymentInfo.setProjectNo(crmProjectDirectlyVo.getProjectNo());
//            }else if (performanceReport.getProjectType() == 2){
//                CrmProjectAgentVo crmProjectAgentVo = remoteProjectAgentClient.getInfo(performanceReport.getProjectId()).getObjEntity();
//                useAdvancePaymentInfo.setProjectNo(crmProjectAgentVo.getProjectNo());
//            }
//            if (performanceReport.getProcessState() == 1){
//                useAdvancePaymentInfo.setState("申请");
//            }else if (performanceReport.getProcessState() == 2){
//                useAdvancePaymentInfo.setState("生效");
//            }
//            useAdvancePaymentInfo.setPerformanceReportNumber(performanceReport.getProcessNumber());
//            useAdvancePaymentInfo.setPayoutCompanyName(performanceReport.getChannelCompanyName());
//            useAdvancePaymentInfo.setRecipientCompanyName(performanceReport.getSupplierName());
//            useAdvancePaymentInfo.setUseAdvancePayment(performanceReport.getUsedPrepaidAmount());
//            //useAdvancePaymentInfos.add(useAdvancePaymentInfo);
//        });
        //agentPaymentDisburseVO.setUseAdvancePaymentInfos(useAdvancePaymentInfos);
        return agentPaymentDisburseVO;
    }

    @Override
    public String selectSaleName(String saleName, String agentDirectorName) {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(saleName)){
            String saleId =  Optional.ofNullable(tosEmployeeClient.existNameJobNo(saleName))
                    .map(JsonObject::getObjEntity).orElse(null);
            if (org.apache.commons.lang3.StringUtils.isBlank(saleId)){
                return "您填写的【公司销售人员】错误，请正确填写！";
            }else {
                return null;
            }
        }

        if (org.apache.commons.lang3.StringUtils.isNotBlank(agentDirectorName)){
            String agentDirectorId =  Optional.ofNullable(tosEmployeeClient.existNameJobNo(agentDirectorName))
                    .map(JsonObject::getObjEntity).orElse(null);
            if (org.apache.commons.lang3.StringUtils.isBlank(agentDirectorId)){
                return "您填写的【关联渠道总监】错误，请正确填写！";
            }else {
                return null;
            }
        }
        return null;
    }
}
