package com.topsec.crm.flow.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.topsec.crm.flow.api.dto.loan.loanCollection.LoanCollectionMainQueryPageDTO;
import com.topsec.crm.flow.api.dto.loan.loanCollection.vo.LoanCollectionDetailsVO;
import com.topsec.crm.flow.api.dto.targetedinventorypreparation.vo.TargetedInventoryPreparationStockVO;
import com.topsec.crm.flow.core.entity.LoanCollectionMain;
import com.topsec.crm.flow.core.entity.SubLoanCollection;
import com.topsec.crm.flow.core.mapper.LoanCollectionMainMapper;
import com.topsec.crm.flow.core.mapper.SubLoanCollectionMapper;
import com.topsec.crm.flow.core.service.ILoanCollectionDetailsService;
import com.topsec.crm.flow.core.entity.LoanCollectionDetails;
import com.topsec.crm.flow.core.mapper.LoanCollectionDetailsMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.tos.common.HyperBeanUtils;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 *  服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-28 18:58
 */
@Service
public class LoanCollectionDetailsServiceImpl extends ServiceImpl<LoanCollectionDetailsMapper, LoanCollectionDetails> implements ILoanCollectionDetailsService {
    @Resource
    LoanCollectionMainMapper loanCollectionMainMapper;
    @Resource
    SubLoanCollectionMapper subLoanCollectionMapper;
    @Override
    public PageUtils<LoanCollectionDetailsVO> Page(LoanCollectionMainQueryPageDTO loanCollectionMainQueryPageDTO, Set<String> personIdsOfPermission) {
        // 构建主查询条件
        LambdaQueryWrapper<LoanCollectionDetails> wrapper = new LambdaQueryWrapper<>();

        // 基础条件
        wrapper.eq(LoanCollectionDetails::getDelFlag, 0)
                .eq(StringUtils.isNotBlank(loanCollectionMainQueryPageDTO.getLoanApplyProcessNumber()) &&
                                StringUtils.isNotEmpty(loanCollectionMainQueryPageDTO.getLoanApplyProcessNumber()),
                        LoanCollectionDetails::getLoanApplyProcessNumber, loanCollectionMainQueryPageDTO.getLoanApplyProcessNumber())
                .eq(StringUtils.isNotBlank(loanCollectionMainQueryPageDTO.getLoanToCostNumber()) &&
                                StringUtils.isNotEmpty(loanCollectionMainQueryPageDTO.getLoanToCostNumber()),
                        LoanCollectionDetails::getLoanToCostNumber, loanCollectionMainQueryPageDTO.getLoanToCostNumber());

        // 使用分步查询方式避免复杂的嵌套查询参数问题
        // 第一步：查询符合条件的LoanCollectionMain记录
        LambdaQueryWrapper<LoanCollectionMain> mainWrapper = new LambdaQueryWrapper<>();
        mainWrapper.eq(LoanCollectionMain::getDelFlag, 0);

        if (loanCollectionMainQueryPageDTO.getProcessState() != null) {
            mainWrapper.eq(LoanCollectionMain::getProcessState, loanCollectionMainQueryPageDTO.getProcessState());
        }

        if (StringUtils.isNotBlank(loanCollectionMainQueryPageDTO.getProcessNumber()) &&
                StringUtils.isNotEmpty(loanCollectionMainQueryPageDTO.getProcessNumber())) {
            mainWrapper.like(LoanCollectionMain::getProcessNumber, loanCollectionMainQueryPageDTO.getProcessNumber());
        }

        if (StringUtils.isNotBlank(loanCollectionMainQueryPageDTO.getDepName()) &&
                StringUtils.isNotEmpty(loanCollectionMainQueryPageDTO.getDepName())) {
            mainWrapper.like(LoanCollectionMain::getDepName, loanCollectionMainQueryPageDTO.getDepName());
        }

        List<LoanCollectionMain> mains = loanCollectionMainMapper.selectList(mainWrapper);
        if (mains.isEmpty()) {
            return new PageUtils<>();
        }

        Set<String> parentProcessInstanceIds = mains.stream()
                .map(LoanCollectionMain::getProcessInstanceId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (parentProcessInstanceIds.isEmpty()) {
            return new PageUtils<>();
        }

        // 第二步：基于主表结果查询SubLoanCollection
        LambdaQueryWrapper<SubLoanCollection> subWrapper = new LambdaQueryWrapper<>();
        subWrapper.eq(SubLoanCollection::getDelFlag, 0)
                .in(io.seata.common.util.CollectionUtils.isNotEmpty(personIdsOfPermission),
                        SubLoanCollection::getLoanUserId, personIdsOfPermission)
                .in(io.seata.common.util.CollectionUtils.isNotEmpty(loanCollectionMainQueryPageDTO.getLoanUserId()),
                        SubLoanCollection::getLoanUserId, loanCollectionMainQueryPageDTO.getLoanUserId())
                .in(SubLoanCollection::getParentProcessInstanceId, parentProcessInstanceIds);

        List<SubLoanCollection> subs = subLoanCollectionMapper.selectList(subWrapper);
        if (subs.isEmpty()) {
            return new PageUtils<>();
        }

        Set<String> processInstanceIds = subs.stream()
                .map(SubLoanCollection::getProcessInstanceId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (processInstanceIds.isEmpty()) {
            return new PageUtils<>();
        }

        // 第三步：基于上述结果查询LoanCollectionDetails并分页
        wrapper.in(LoanCollectionDetails::getProcessInstanceId, processInstanceIds);

        Page<LoanCollectionDetails> page = new Page<>(loanCollectionMainQueryPageDTO.getPageNum(), loanCollectionMainQueryPageDTO.getPageSize(), true);
        IPage<LoanCollectionDetails> resultPage =baseMapper.selectPage(page,wrapper);
        if (resultPage.getTotal()==0){
            return new PageUtils();
        }

        Map<String, LoanCollectionMain> loanCollectionMains=loanCollectionMainMapper.selectList(new LambdaQueryWrapper<LoanCollectionMain>()
                .eq(LoanCollectionMain::getDelFlag,0)
                .in(LoanCollectionMain::getProcessInstanceId,resultPage.getRecords().stream().map(LoanCollectionDetails::getParentProcessInstanceId).distinct().toList()))
                .stream().collect(Collectors.toMap(LoanCollectionMain::getProcessInstanceId,(o->o)));

        Map<String,SubLoanCollection> subLoanCollections=subLoanCollectionMapper.selectList(new LambdaQueryWrapper<SubLoanCollection>()
                .eq(SubLoanCollection::getDelFlag,0)
                .in(SubLoanCollection::getProcessInstanceId,resultPage.getRecords().stream().map(LoanCollectionDetails::getProcessInstanceId).distinct().toList()))
                .stream().collect(Collectors.toMap(SubLoanCollection::getProcessInstanceId,(o->o)));


        List<LoanCollectionDetailsVO> loanCollectionDetailsVOS = HyperBeanUtils.copyListProperties(resultPage.getRecords(),LoanCollectionDetailsVO::new);

        for (LoanCollectionDetailsVO loanCollectionDetailsVO : loanCollectionDetailsVOS) {
            LoanCollectionMain loanCollectionMain=loanCollectionMains.get(loanCollectionDetailsVO.getParentProcessInstanceId());
            loanCollectionDetailsVO.setProcessNumber(loanCollectionMain.getProcessNumber());
            loanCollectionDetailsVO.setGenerateTime(loanCollectionMain.getGenerateTime());
            loanCollectionDetailsVO.setProcessEndTime(loanCollectionMain.getProcessEndTime());
            loanCollectionDetailsVO.setDepName(loanCollectionMain.getDepName());
            SubLoanCollection subLoanCollection=subLoanCollections.get(loanCollectionDetailsVO.getProcessInstanceId());
            loanCollectionDetailsVO.setLoanUserName(subLoanCollection.getLoanUserName());
        }
        PageUtils<LoanCollectionDetailsVO> pageUtils=PageUtils.paginate(loanCollectionDetailsVOS,loanCollectionMainQueryPageDTO.getPageSize(),loanCollectionMainQueryPageDTO.getPageNum());
        pageUtils.setTotalCount((int) resultPage.getTotal());
        pageUtils.setTotalPage((int) resultPage.getPages());
        return pageUtils;
    }

    @Override
    public List<LoanCollectionDetailsVO> export(LoanCollectionMainQueryPageDTO loanCollectionMainQueryPageDTO, Set<String> personIdsOfPermission) {
        // 构建主查询条件
        LambdaQueryWrapper<LoanCollectionDetails> wrapper = new LambdaQueryWrapper<>();

        // 基础条件
        wrapper.eq(LoanCollectionDetails::getDelFlag, 0)
                .eq(StringUtils.isNotBlank(loanCollectionMainQueryPageDTO.getLoanApplyProcessNumber()) &&
                                StringUtils.isNotEmpty(loanCollectionMainQueryPageDTO.getLoanApplyProcessNumber()),
                        LoanCollectionDetails::getLoanApplyProcessNumber, loanCollectionMainQueryPageDTO.getLoanApplyProcessNumber())
                .eq(StringUtils.isNotBlank(loanCollectionMainQueryPageDTO.getLoanToCostNumber()) &&
                                StringUtils.isNotEmpty(loanCollectionMainQueryPageDTO.getLoanToCostNumber()),
                        LoanCollectionDetails::getLoanToCostNumber, loanCollectionMainQueryPageDTO.getLoanToCostNumber());

        // 使用分步查询方式避免复杂的嵌套查询参数问题
        // 第一步：查询符合条件的LoanCollectionMain记录
        LambdaQueryWrapper<LoanCollectionMain> mainWrapper = new LambdaQueryWrapper<>();
        mainWrapper.eq(LoanCollectionMain::getDelFlag, 0);

        if (loanCollectionMainQueryPageDTO.getProcessState() != null) {
            mainWrapper.eq(LoanCollectionMain::getProcessState, loanCollectionMainQueryPageDTO.getProcessState());
        }

        if (StringUtils.isNotBlank(loanCollectionMainQueryPageDTO.getProcessNumber()) &&
                StringUtils.isNotEmpty(loanCollectionMainQueryPageDTO.getProcessNumber())) {
            mainWrapper.like(LoanCollectionMain::getProcessNumber, loanCollectionMainQueryPageDTO.getProcessNumber());
        }

        if (StringUtils.isNotBlank(loanCollectionMainQueryPageDTO.getDepName()) &&
                StringUtils.isNotEmpty(loanCollectionMainQueryPageDTO.getDepName())) {
            mainWrapper.like(LoanCollectionMain::getDepName, loanCollectionMainQueryPageDTO.getDepName());
        }

        List<LoanCollectionMain> mains = loanCollectionMainMapper.selectList(mainWrapper);
        if (mains.isEmpty()) {
            return null;
        }

        Set<String> parentProcessInstanceIds = mains.stream()
                .map(LoanCollectionMain::getProcessInstanceId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (parentProcessInstanceIds.isEmpty()) {
            return null;
        }

        // 第二步：基于主表结果查询SubLoanCollection
        LambdaQueryWrapper<SubLoanCollection> subWrapper = new LambdaQueryWrapper<>();
        subWrapper.eq(SubLoanCollection::getDelFlag, 0)
                .in(io.seata.common.util.CollectionUtils.isNotEmpty(personIdsOfPermission),
                        SubLoanCollection::getLoanUserId, personIdsOfPermission)
                .in(io.seata.common.util.CollectionUtils.isNotEmpty(loanCollectionMainQueryPageDTO.getLoanUserId()),
                        SubLoanCollection::getLoanUserId, loanCollectionMainQueryPageDTO.getLoanUserId())
                .in(SubLoanCollection::getParentProcessInstanceId, parentProcessInstanceIds);

        List<SubLoanCollection> subs = subLoanCollectionMapper.selectList(subWrapper);
        if (subs.isEmpty()) {
            return null;
        }

        Set<String> processInstanceIds = subs.stream()
                .map(SubLoanCollection::getProcessInstanceId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (processInstanceIds.isEmpty()) {
            return null;
        }

        // 第三步：基于上述结果查询LoanCollectionDetails并分页
        wrapper.in(LoanCollectionDetails::getProcessInstanceId, processInstanceIds);

        Page<LoanCollectionDetails> page = new Page<>(loanCollectionMainQueryPageDTO.getPageNum(), loanCollectionMainQueryPageDTO.getPageSize(), true);
        IPage<LoanCollectionDetails> resultPage =baseMapper.selectPage(page,wrapper);
        if (resultPage.getTotal()==0){
            return null;
        }

        Map<String, LoanCollectionMain> loanCollectionMains=loanCollectionMainMapper.selectList(new LambdaQueryWrapper<LoanCollectionMain>()
                        .eq(LoanCollectionMain::getDelFlag,0)
                        .in(LoanCollectionMain::getProcessInstanceId,resultPage.getRecords().stream().map(LoanCollectionDetails::getParentProcessInstanceId).distinct().toList()))
                .stream().collect(Collectors.toMap(LoanCollectionMain::getProcessInstanceId,(o->o)));

        Map<String,SubLoanCollection> subLoanCollections=subLoanCollectionMapper.selectList(new LambdaQueryWrapper<SubLoanCollection>()
                        .eq(SubLoanCollection::getDelFlag,0)
                        .in(SubLoanCollection::getProcessInstanceId,resultPage.getRecords().stream().map(LoanCollectionDetails::getProcessInstanceId).distinct().toList()))
                .stream().collect(Collectors.toMap(SubLoanCollection::getProcessInstanceId,(o->o)));


        List<LoanCollectionDetailsVO> loanCollectionDetailsVOS = HyperBeanUtils.copyListProperties(resultPage.getRecords(),LoanCollectionDetailsVO::new);
        for (LoanCollectionDetailsVO loanCollectionDetailsVO : loanCollectionDetailsVOS) {
            LoanCollectionMain loanCollectionMain=loanCollectionMains.get(loanCollectionDetailsVO.getParentProcessInstanceId());
            loanCollectionDetailsVO.setProcessNumber(loanCollectionMain.getProcessNumber());
            loanCollectionDetailsVO.setGenerateTime(loanCollectionMain.getGenerateTime());
            loanCollectionDetailsVO.setProcessEndTime(loanCollectionMain.getProcessEndTime());
            loanCollectionDetailsVO.setDepName(loanCollectionMain.getDepName());
            SubLoanCollection subLoanCollection=subLoanCollections.get(loanCollectionDetailsVO.getProcessInstanceId());
            loanCollectionDetailsVO.setLoanUserName(subLoanCollection.getLoanUserName());
        }
        return loanCollectionDetailsVOS;
    }
}
