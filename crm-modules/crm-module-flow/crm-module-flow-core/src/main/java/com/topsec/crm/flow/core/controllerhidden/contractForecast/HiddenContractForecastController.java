package com.topsec.crm.flow.core.controllerhidden.contractForecast;

import com.topsec.crm.flow.core.service.IContractForecastService;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.stats.api.entity.home.HomeRankContractForecastVO;
import com.topsec.tbscommon.JsonObject;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @version V1.0
 * @Description: 合同预测内部调用接口
 * @ClassName: com.topsec.crm.flow.core.controllerhidden.contractForecast.HiddenContractForecastController.java
 * @Copyright 天融信 - Powered By 企业软件研发中心
 * @author: leo
 * @date: 2025-07-26 11:47
 */
@RestController
@RequestMapping("/hidden/contractForecast")
@Validated
public class HiddenContractForecastController extends BaseController {

    @Resource
    private IContractForecastService contractForecastService;

    // 发起合同预测流程
    @GetMapping("/sendContractForecastFlow")
    public JsonObject<Boolean> sendContractForecastFlow(@RequestParam String month) {
        Boolean result = contractForecastService.sendContractForecastFlow(month);
        return new JsonObject<>(result);
    }

    // 查询合同预测未办结数据排名
    @GetMapping("/unfinishedContractForecastRank")
    JsonObject<List<HomeRankContractForecastVO>> unfinishedContractForecastRank(@RequestParam Integer type){
        List<HomeRankContractForecastVO> result = contractForecastService.unfinishedContractForecastRank(type);
        return new JsonObject<>(result);
    }

    // 自动完成合同预测01步
    @PostMapping("/hidden/contractForecast/autoFinishContractForecastFlow01")
    JsonObject<Boolean> autoFinishContractForecastFlow01(@RequestBody List<String> month){
        Boolean result = contractForecastService.autoFinishContractForecastFlow01(month);
        return new JsonObject<>(result);
    }

    // 自动完成合同预测02步
    @PostMapping("/hidden/contractForecast/autoFinishContractForecastFlow02")
    JsonObject<Boolean>  autoFinishContractForecastFlow02(@RequestBody List<String> month){
        Boolean result = contractForecastService.autoFinishContractForecastFlow02(month);
        return new JsonObject<>(result);
    }
}
