package com.topsec.crm.flow.core.process.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.topsec.crm.flow.api.dto.handoverProcess.HandoverProcessReviewFlowLaunchDTO;
import com.topsec.crm.flow.core.entity.HandoverProcessReviewMain;
import com.topsec.crm.flow.core.entity.HandoverReviewConfig;
import com.topsec.crm.flow.core.entity.ProcessExtensionInfo;
import com.topsec.crm.flow.core.process.AbstractProcessService;
import com.topsec.crm.flow.core.process.ProcessTypeEnum;
import com.topsec.crm.flow.core.service.HandoverProcessReviewMainService;
import com.topsec.crm.flow.core.service.HandoverReviewConfigService;
import com.topsec.crm.framework.common.bean.FlowPerson;
import com.topsec.crm.framework.common.util.AccountAccquireUtils;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsNodeClient;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.vo.FlowStateInfoVo;
import com.topsec.vo.node.ApproveNode;
import com.topsec.vo.node.TfsNodeRuleVo;
import com.topsec.vo.task.Leader;
import com.topsec.vo.task.TfsTaskVo;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@RequiredArgsConstructor
public class HandoverProcessService extends AbstractProcessService<HandoverProcessReviewFlowLaunchDTO> {

    private final HandoverProcessReviewMainService handoverProcessReviewMainService;
    private final HandoverReviewConfigService handoverReviewConfigService;
    private final TfsNodeClient tfsNodeClient;

    @Override
    protected void preProcess(HandoverProcessReviewFlowLaunchDTO launchable) {
        String type = launchable.getBaseInfo().getHandoverType()==1? "离职":"调动";
        launchable.setMatter(ProcessTypeEnum.HANDOVER_PERSONNEL.getProcessName()+"【"+type+"】");
        Map<String,Object> variableMap= new HashMap<>();
        //是否有交接内容
        variableMap.put("hasPendingHandover",true);
        //接手人员是否全部同一部门
        variableMap.put("leader",true);
        TfsNodeRuleVo tfsNodeRuleVo = new TfsNodeRuleVo();
        tfsNodeRuleVo.setProcessDefinitionKey(ProcessTypeEnum.HANDOVER_PERSONNEL.getProcessDefinitionKey());
        Set<String> stringSet = handoverReviewConfigService.getNodeIds();
        tfsNodeRuleVo.setNodeIdList(stringSet.stream().toList());
        JsonObject<Map<String, ApproveNode>> nodeObject = tfsNodeClient.findByNodeIdList(tfsNodeRuleVo);
        Map<String, ApproveNode> map = Optional.ofNullable(nodeObject).map(JsonObject::getObjEntity).orElse(Collections.emptyMap());
        List<String> assigneeList = new ArrayList<>();

        List<HandoverReviewConfig> filteredRequiredHandoverReviewConfigList = handoverReviewConfigService.filterRequiredHandoverReviewConfig(launchable);
        Map<String, Boolean> handoverReviewContentGateWay = handoverReviewConfigService.generateHandoverReviewContentGateWay(filteredRequiredHandoverReviewConfigList);

        //网关条件
        variableMap.putAll(handoverReviewContentGateWay);
        //审批人员
        for (HandoverReviewConfig handoverReviewConfig : filteredRequiredHandoverReviewConfigList) {
            String nodeId = handoverReviewConfig.getNodeId();
            ApproveNode approveNode = map.get(nodeId);
            if (approveNode != null){
                List<String> accountIds = Optional.ofNullable(approveNode.getCurrentApprovedList())
                        .orElse(Collections.emptyList())
                        .stream().map(Leader::getValue).toList();
                if(!accountIds.isEmpty()){
                    variableMap.put(approveNode.getExpression(),accountIds);
                }
            }
        }

        launchable.setVariables(variableMap);
        FlowPerson flowPerson = AccountAccquireUtils.convertGetAccountSingle(launchable.getBaseInfo().getPersonId());
        assigneeList.add(flowPerson.getAccountId());
        launchable.setAssigneeList(CollectionUtil.toTreeSet(assigneeList,Comparator.naturalOrder()));


    }

    @Override
    protected String afterEngineLaunch(ProcessExtensionInfo processInfo, HandoverProcessReviewFlowLaunchDTO launchable) {
        HandoverProcessReviewMain main = HyperBeanUtils.copyPropertiesByJackson(launchable.getBaseInfo(),HandoverProcessReviewMain.class);
        main.setProcessInstanceId(processInfo.getProcessInstanceId());
        main.setCreateUser(launchable.getCreatorPersonId());
        handoverProcessReviewMainService.save(main);
        return main.getId();
    }

    @Override
    public void handleProcessing(FlowStateInfoVo flowInfo) {

    }

    @Override
    public void handlePass(FlowStateInfoVo flowInfo) {

    }

    @Override
    public void handlePassBack(FlowStateInfoVo flowInfo) {

    }

    @Override
    public void handleProcessingBack(FlowStateInfoVo flowInfo) {

    }

    @Override
    public ProcessTypeEnum processType() {
        return ProcessTypeEnum.HANDOVER_PERSONNEL;
    }

    @Override
    public Pair<Boolean, String> checkFilling(TfsTaskVo taskVo) {
        return null;
    }

}
