package com.topsec.crm.flow.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.topsec.crm.flow.api.dto.handoverProcess.HandoverProcessReviewFlowLaunchDTO;
import com.topsec.crm.flow.api.handoverReview.HandoverFilterParams;
import com.topsec.crm.flow.core.entity.HandoverReviewConfig;
import com.topsec.crm.flow.core.mapper.HandoverReviewConfigMapper;
import com.topsec.crm.flow.core.service.HandoverReviewConfigService;
import com.topsec.tos.api.client.TosDepartmentClient;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.lang.reflect.Constructor;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 *
 */
@Service
@Slf4j
public class HandoverReviewConfigServiceImpl extends ServiceImpl<HandoverReviewConfigMapper, HandoverReviewConfig>
implements HandoverReviewConfigService{

    TosDepartmentClient tosDepartmentClient;

    @Override
    public List<HandoverReviewConfig> filterRequiredHandoverReviewConfig(HandoverProcessReviewFlowLaunchDTO launchable) {
        Integer handoverType = launchable.getBaseInfo().getHandoverType();
        List<HandoverReviewConfig> firstFilteredConfigList = baseMapper.selectList(new LambdaUpdateWrapper<HandoverReviewConfig>()
                .eq(handoverType == 1, HandoverReviewConfig::getQuitRequired, 1)
                .eq(handoverType == 0, HandoverReviewConfig::getTransferRequired, 1)
        );
        HandoverFilterParams handoverFilterParams=new HandoverFilterParams();
        Map<String, HandoverReviewEnum> enumMap = Arrays.stream(HandoverReviewEnum.values()).collect(Collectors.toMap(HandoverReviewEnum::getActivityId, handoverReviewEnum -> handoverReviewEnum));
        List<HandoverReviewConfig> secondFilteredConfigList = firstFilteredConfigList.stream()
                .filter(handoverReviewConfig -> {
                    HandoverReviewEnum handoverReviewEnum = enumMap.get(handoverReviewConfig.getNodeId());
                    if (handoverReviewEnum == null){
                        log.error("enum不存在的节点：{}", handoverReviewConfig.getNodeId());
                        return true;
                    }
                    try {
                        Class<? extends Predicate<HandoverFilterParams>> predicateClass = handoverReviewEnum
                                .getPredicateClass();
                        if (predicateClass == null) {
                            return true;
                        }
                        Constructor<? extends Predicate<HandoverFilterParams>> declaredConstructor = predicateClass.getDeclaredConstructor();
                        Predicate<HandoverFilterParams> predicate = declaredConstructor.newInstance();
                        return predicate.test(handoverFilterParams);
                    } catch (Exception e) {
                        log.error("异常", e);
                        return false;
                    }
                }).collect(Collectors.toList());

        return secondFilteredConfigList;
    }

    @Override
    public Set<String> getNodeIds() {
        List<HandoverReviewConfig> handoverReviewConfigs = baseMapper.selectList(new LambdaUpdateWrapper<HandoverReviewConfig>());
        return handoverReviewConfigs.stream().map(HandoverReviewConfig::getNodeId).collect(Collectors.toSet());
    }
    @Override
    public String getContentByNodeId(String nodeId){
        return Optional.ofNullable(baseMapper.selectOne(new LambdaUpdateWrapper<HandoverReviewConfig>().eq(HandoverReviewConfig::getNodeId, nodeId)))
                .map(HandoverReviewConfig::getContent).orElse(null);
    }

    @Override
    public Map<String, Boolean> generateHandoverReviewContentGateWay(List<HandoverReviewConfig> filteredHandoverReviewConfigList) {
        return filteredHandoverReviewConfigList.stream()
                .collect(Collectors.toMap(HandoverReviewConfig::getId, v -> true));
    }

    /**
     * todo dr filter 逻辑
     * <AUTHOR>
     */
    @Getter
    private enum HandoverReviewEnum {
    // -- 05A1 人力资源部审批
    //     INSERT INTO crm_flow.handover_review_config (id, node_id, content, quit_required, transfer_required)
    //     VALUES ('1', 'personnelHandoverProcess_05A1', '外派培训及培训确认', 1, 1);
    //
    // -- 05A2 人力资源部审批
    //     INSERT INTO crm_flow.handover_review_config (id, node_id, content, quit_required, transfer_required)
    //     VALUES ('2', 'personnelHandoverProcess_05A2', '复核《工作居住证》办理情况', 1, 0);
    //
    // -- 05B1 供应商管理部审批
    //     INSERT INTO crm_flow.handover_review_config (id, node_id, content, quit_required, transfer_required)
    //     VALUES ('3', 'personnelHandoverProcess_05B1', '供应商管理部确认人员销售合同以及第三方定向采购流程', 1, 0);
    //
    // -- 05C1 信息管理部审批
    //     INSERT INTO crm_flow.handover_review_config (id, node_id, content, quit_required, transfer_required)
    //     VALUES ('4', 'personnelHandoverProcess_05C1', '处理相关帐号', 1, 1);
    //
    // -- 05C2 信息管理部审批
    //     INSERT INTO crm_flow.handover_review_config (id, node_id, content, quit_required, transfer_required)
    //     VALUES ('5', 'personnelHandoverProcess_05C2', '处理相关帐号', 0, 1);
    //
    // -- 05D1 合同管理部审批
    //     INSERT INTO crm_flow.handover_review_config (id, node_id, content, quit_required, transfer_required)
    //     VALUES ('6', 'personnelHandoverProcess_05D1', '确认未执行完合同交接情况', 1, 1);
    //
    // -- 05E1 商务部审批
    //     INSERT INTO crm_flow.handover_review_config (id, node_id, content, quit_required, transfer_required)
    //     VALUES ('7', 'personnelHandoverProcess_05E1', '库存设备交接情况（提交清单）', 1, 1);
    //
    // -- 05E2 商务部审批
    //     INSERT INTO crm_flow.handover_review_config (id, node_id, content, quit_required, transfer_required)
    //     VALUES ('8', 'personnelHandoverProcess_05E2', '内部借试用确认', 1, 1);
    //
    // -- 05E3 商务部审批
    //     INSERT INTO crm_flow.handover_review_config (id, node_id, content, quit_required, transfer_required)
    //     VALUES ('9', 'personnelHandoverProcess_05E3', '商务部领导确认（包括罚款情况）', 1, 1);
    //
    // -- 05F1 产品协同管理部审批
    //     INSERT INTO crm_flow.handover_review_config (id, node_id, content, quit_required, transfer_required)
    //     VALUES ('10', 'personnelHandoverProcess_05F1', '研发采购设备及硬件部的样机确认', 1, 1);
    //
    // -- 05H1 保密办公室审批
    //     INSERT INTO crm_flow.handover_review_config (id, node_id, content, quit_required, transfer_required)
    //     VALUES ('11', 'personnelHandoverProcess_05H1', '是否为涉密人员', 1, 0);
    //
    // -- 05I1 产品规划管理部审批
    //     INSERT INTO crm_flow.handover_review_config (id, node_id, content, quit_required, transfer_required)
    //     VALUES ('12', 'personnelHandoverProcess_05I1', '专利成果确认', 1, 0);
    //
    // -- 05J1 客户服务部审批
    //     INSERT INTO crm_flow.handover_review_config (id, node_id, content, quit_required, transfer_required)
    //     VALUES ('13', 'personnelHandoverProcess_05J1', '早期借用的备机归还情况', 1, 0);
    //
    // -- 05J2 客户服务部审批
    //     INSERT INTO crm_flow.handover_review_config (id, node_id, content, quit_required, transfer_required)
    //     VALUES ('14', 'personnelHandoverProcess_05J2', '售后人员工作交接情况', 1, 0);
    //
    // -- 05J3 售后服务中心审批
    //     INSERT INTO crm_flow.handover_review_config (id, node_id, content, quit_required, transfer_required)
    //     VALUES ('15', 'personnelHandoverProcess_05J3', '确认售后服务业务情况', 1, 0);
    //
    // -- 05J4 售后服务中心审批
    //     INSERT INTO crm_flow.handover_review_config (id, node_id, content, quit_required, transfer_required)
    //     VALUES ('16', 'personnelHandoverProcess_05J4', '确认售后服务业务情况', 1, 1);
    //
    // -- 05K1 配置管理员审批
    //     INSERT INTO crm_flow.handover_review_config (id, node_id, content, quit_required, transfer_required)
    //     VALUES ('17', 'personnelHandoverProcess_05K1', '配置管理员确认', 1, 1);
    //
    // -- 05L1 行政部审批
    //     INSERT INTO crm_flow.handover_review_config (id, node_id, content, quit_required, transfer_required)
    //     VALUES ('18', 'personnelHandoverProcess_05L1', '公租房（北京总部）', 1, 0);
    //
    // -- 05L2 行政部审批
    //     INSERT INTO crm_flow.handover_review_config (id, node_id, content, quit_required, transfer_required)
    //     VALUES ('19', 'personnelHandoverProcess_05L2', '武汉全员，成都研发中心、西安研发中心确认固定资产', 1, 0);
    //
    // -- 05L3 行政部审批
    //     INSERT INTO crm_flow.handover_review_config (id, node_id, content, quit_required, transfer_required)
    //     VALUES ('20', 'personnelHandoverProcess_05L3', '固定资产', 1, 0);
    //
    // -- 05L4 行政部审批
    //     INSERT INTO crm_flow.handover_review_config (id, node_id, content, quit_required, transfer_required)
    //     VALUES ('21', 'personnelHandoverProcess_05L4', '收回胸卡，门禁卡，钥匙', 1, 0);
    //
    // -- 05M1 财务部审批
    //     INSERT INTO crm_flow.handover_review_config (id, node_id, content, quit_required, transfer_required)
    //     VALUES ('22', 'personnelHandoverProcess_05M1', '网络安全公司（确认借款及补助发放截止月）', 1, 1);
    //
    // -- 05M2 财务部审批
    //     INSERT INTO crm_flow.handover_review_config (id, node_id, content, quit_required, transfer_required)
    //     VALUES ('23', 'personnelHandoverProcess_05M2', '科技公司、软件公司（确认借款及补助发放截止月）', 1, 1);
    //
    // -- 05M3 财务部审批
    //     INSERT INTO crm_flow.handover_review_config (id, node_id, content, quit_required, transfer_required)
    //     VALUES ('24', 'personnelHandoverProcess_05M3', '分支机构（确认借款及补助发放截止月）', 1, 1);
    //
    // -- 05M4 财务部审批
    //     INSERT INTO crm_flow.handover_review_config (id, node_id, content, quit_required, transfer_required)
    //     VALUES ('25', 'personnelHandoverProcess_05M4', '出纳', 1, 1);
    //
    // -- 05M5 财务部审批
    //     INSERT INTO crm_flow.handover_review_config (id, node_id, content, quit_required, transfer_required)
    //     VALUES ('26', 'personnelHandoverProcess_05M5', '出纳', 1, 1);
    //
    // -- 05N1 销售管理部审批
    //     INSERT INTO crm_flow.handover_review_config (id, node_id, content, quit_required, transfer_required)
    //     VALUES ('27', 'personnelHandoverProcess_05N1', '销售人员离职及调动管理', 1, 1);
    //
    // -- 05P1 云服务VPN业务管理审批
    //     INSERT INTO crm_flow.handover_review_config (id, node_id, content, quit_required, transfer_required)
    //     VALUES ('28', 'personnelHandoverProcess_05P1', 'VPN相关账号关闭', 1, 0);
    //
    // -- 05Q1 法务合规部审批
    //     INSERT INTO crm_flow.handover_review_config (id, node_id, content, quit_required, transfer_required)
    //     VALUES ('29', 'personnelHandoverProcess_05Q1', '电子印鉴借用及电子营业执照授权审核', 1, 0);
    //
    // -- 05Q2 法务合规部审批
    //     INSERT INTO crm_flow.handover_review_config (id, node_id, content, quit_required, transfer_required)
    //     VALUES ('30', 'personnelHandoverProcess_05Q2', '档案、图书借用情况及相关档案是否移交', 1, 0);


        A1(),
        A2(),

        B1(),

        C1(),
        C2(),

        D1(),

        E1(),
        E2(),
        E3(),

        F1(),

        // G1,

        H1(),

        I1(),

        J1(),
        J2(),
        J3(),
        J4(),

        K1(),

        L1(),
        L2(),
        L3(),
        L4(),

        M1(),
        M2(),
        M3(),
        M4(),
        M5(),

        N1(),

        P1(),

        Q1(),
        Q2(),

        ;



        private final String activityId;
        private final Class<? extends Predicate<HandoverFilterParams>> predicateClass;

        @SuppressWarnings("unchecked")
        HandoverReviewEnum() {
            Class<? extends Predicate<HandoverFilterParams>> predicateTemp;
            String name = this.name();
            this.activityId = "personnelHandoverProcess_05" + name;
            try {
                predicateTemp = (Class<? extends Predicate<HandoverFilterParams>>) Class
                        .forName("com.topsec.crm.flow.api.handoverReview.HandoverReviewEnum.Predicate"+name);
            } catch (ClassNotFoundException e) {
                predicateTemp =null;
            }
            this.predicateClass = predicateTemp;
        }


        /**
         * 是	否	社保缴纳公司为【北京天融信网络安全技术有效公司】
         */
        public static class PredicateA2 implements Predicate<HandoverFilterParams> {
            @Override
            public boolean test(HandoverFilterParams handoverFilterParams) {
                return true;
            }
        }


        /**
         * 是	否	所有员工
         */
        public static class PredicateB1 implements Predicate<HandoverFilterParams> {
            @Override
            public boolean test(HandoverFilterParams handoverFilterParams) {
                return true;
            }
        }


        /**
         * 否	是	所有员工
         */
        public static class PredicateC2 implements Predicate<HandoverFilterParams> {
            @Override
            public boolean test(HandoverFilterParams handoverFilterParams) {
                return true;
            }
        }

        /**
         * 是	是	销售人员
         */
        public static class PredicateD1 implements Predicate<HandoverFilterParams> {
            @Override
            public boolean test(HandoverFilterParams handoverFilterParams) {
                return true;
            }
        }

        /**
         是	是	"【产品体系】内部借试用申请员工
                 1、部门人员判断依据：一级部门（产品体系）；
                 2、内部借试用员工判断依据：旧CRM中供应链管理有记录，申请了内部借试用的人员
                 3、非05E2和05E3责任范围内员工，两个审批节点都审批；"
         */

        public static class PredicateE2 implements Predicate<HandoverFilterParams> {
            @Override
            public boolean test(HandoverFilterParams handoverFilterParams) {
                return true;
            }
        }

        /**
         是	是	"【售后&信管&市场】内部借试用申请员工
                 1、部门人员判断依据：一级部门（售后服务中心、市场中心、信息管理与运维中心）；
                 2、内部借试用员工判断依据：旧CRM中供应链管理有记录，申请了内部借试用的人员
                 3、非05E2和05E3责任范围内员工，两个审批节点都审批；"
         */

        public static class PredicateE3 implements Predicate<HandoverFilterParams> {
            @Override
            public boolean test(HandoverFilterParams handoverFilterParams) {
                return true;
            }
        }

        /**
         * 是	否	所有员工
         */

        public static class PredicateH1 implements Predicate<HandoverFilterParams> {
            @Override
            public boolean test(HandoverFilterParams handoverFilterParams) {
                return true;
            }
        }
        /**
         * 是	否	产品体系员工
         */

        public static class PredicateI1 implements Predicate<HandoverFilterParams> {
            @Override
            public boolean test(HandoverFilterParams handoverFilterParams) {
                return true;
            }
        }

        /**
         * 是	否	销售人员
         */

        public static class PredicateJ1 implements Predicate<HandoverFilterParams> {
            @Override
            public boolean test(HandoverFilterParams handoverFilterParams) {
                return true;
            }
        }

        /**
         * 是	否	售后服务中心员工
         */

        public static class PredicateJ2 implements Predicate<HandoverFilterParams> {
            @Override
            public boolean test(HandoverFilterParams handoverFilterParams) {
                return true;
            }
        }

        /**
         * 是	否	所有员工
         */

        public static class PredicateJ3 implements Predicate<HandoverFilterParams> {
            @Override
            public boolean test(HandoverFilterParams handoverFilterParams) {
                return true;
            }
        }

        /**
         * 是	是	销售人员
         */

        public static class PredicateJ4 implements Predicate<HandoverFilterParams> {
            @Override
            public boolean test(HandoverFilterParams handoverFilterParams) {
                return true;
            }
        }

        /**
         *  是	是	产品体系员工
         */

        public static class PredicateK1 implements Predicate<HandoverFilterParams> {
            @Override
            public boolean test(HandoverFilterParams handoverFilterParams) {
                return true;
            }
        }

        /**
         *是	否	社保缴纳地为【北京】的员工
         */

        public static class PredicateL1 implements Predicate<HandoverFilterParams> {
            @Override
            public boolean test(HandoverFilterParams handoverFilterParams) {
                return true;
            }
        }
        /**
         *是	否	"1、社保缴纳地为【武汉】的员工；
         *           2、所在研发中心为【成都研发中心】和【西安研发中心】的员工"
         */

        public static class PredicateL2 implements Predicate<HandoverFilterParams> {
            @Override
            public boolean test(HandoverFilterParams handoverFilterParams) {
                return true;
            }
        }    /**
         *是	否	除05L2外其它员工
         */

        public static class PredicateL3 implements Predicate<HandoverFilterParams> {
            @Override
            public boolean test(HandoverFilterParams handoverFilterParams) {
                return true;
            }
        }
        /**
         *是	否	社保缴纳地为【北京】的员工
         */

        public static class PredicateL4 implements Predicate<HandoverFilterParams> {
            @Override
            public boolean test(HandoverFilterParams handoverFilterParams) {
                return true;
            }
        }
        /**
         *是	是	社保缴纳地为非【北京】的员工
         */

        public static class PredicateM3 implements Predicate<HandoverFilterParams> {
            @Override
            public boolean test(HandoverFilterParams handoverFilterParams) {
                return true;
            }
        }
        /**
         *是	是	销售人员
         */

        public static class PredicateN1 implements Predicate<HandoverFilterParams> {
            @Override
            public boolean test(HandoverFilterParams handoverFilterParams) {
                return true;
            }
        }
        /**
         *是	否	天勤上存在已办结【安全云服务VPN】流程的员工
         */

        public static class PredicateP1 implements Predicate<HandoverFilterParams> {
            @Override
            public boolean test(HandoverFilterParams handoverFilterParams) {
                return true;
            }
        }

        /**
         *是	否	所有员工
         */

        public static class PredicateQ1 implements Predicate<HandoverFilterParams> {
            @Override
            public boolean test(HandoverFilterParams handoverFilterParams) {
                return true;
            }
        }
        /**
         *是	否	所有员工
         */

        public static class PredicateQ2 implements Predicate<HandoverFilterParams> {
            @Override
            public boolean test(HandoverFilterParams handoverFilterParams) {
                return true;
            }
        }



        public static void main(String[] args) {
            HandoverFilterParams handoverFilterParams=new HandoverFilterParams();
            handoverFilterParams.setHandoverType(0);
            System.out.println(true);
        }

    }
}
