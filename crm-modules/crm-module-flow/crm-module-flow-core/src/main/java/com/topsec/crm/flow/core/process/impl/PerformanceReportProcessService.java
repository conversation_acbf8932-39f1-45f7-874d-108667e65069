package com.topsec.crm.flow.core.process.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.topsec.crm.agent.api.RemoteAgentRebateService;
import com.topsec.crm.agent.api.RemoteAgentSamplePackageService;
import com.topsec.crm.agent.api.RemoteAgentService;
import com.topsec.crm.agent.api.entity.CrmAgentContactsVo;
import com.topsec.crm.agent.api.entity.CrmAgentVo;
import com.topsec.crm.agent.api.entity.PackageProductInfoVO;
import com.topsec.crm.agent.api.entity.crmagentrebate.input.CrmAgentRebatePerformanceInput;
import com.topsec.crm.agent.api.enums.AgentRebateEnum;
import com.topsec.crm.contract.api.RemoteCrmAgentPaymentVerificationService;
import com.topsec.crm.contract.api.RemoteCrmPerformanceReportService;
import com.topsec.crm.flow.api.RemoteMaterialApplyMainService;
import com.topsec.crm.flow.api.dto.materialApply.ExistDisableProductVo;
import com.topsec.crm.flow.api.dto.materialApply.MaterialApplyMainVo;
import com.topsec.crm.flow.api.dto.materialApply.MaterialApplyProductDTO;
import com.topsec.crm.flow.api.dto.performancereport.*;
import com.topsec.crm.flow.api.dto.targetedinventorypreparation.vo.TargetedInventoryPreparationOwnVO;
import com.topsec.crm.flow.api.vo.CrmMaterialDisableVo;
import com.topsec.crm.flow.core.entity.*;
import com.topsec.crm.flow.core.mapper.PerformanceReportContractDeliveryDetailMapper;
import com.topsec.crm.flow.core.mapper.PerformanceReportMapper;
import com.topsec.crm.flow.core.mapstruct.PerformanceReportConvertor;
import com.topsec.crm.flow.core.process.AbstractProjectProcessService;
import com.topsec.crm.flow.core.process.ProcessBeforeFillingValidator;
import com.topsec.crm.flow.core.process.ProcessTypeEnum;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.flow.core.util.ContractUtils;
import com.topsec.crm.flow.core.validator.CheckCondition;
import com.topsec.crm.flow.core.validator.HyperValidator;
import com.topsec.crm.framework.common.bean.CrmProjectProductSnVO;
import com.topsec.crm.framework.common.enums.AgentEnum;
import com.topsec.crm.framework.common.enums.ProjectStatusBase;
import com.topsec.crm.framework.common.enums.ProjectStatusEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.product.api.RemoteProductAgentInventoryService;
import com.topsec.crm.product.api.RemoteProductSeparationRelService;
import com.topsec.crm.product.api.RemoteProductService;
import com.topsec.crm.product.api.entity.CrmProductVo;
import com.topsec.crm.project.api.RemoteProjectAgentService;
import com.topsec.crm.project.api.client.*;
import com.topsec.crm.project.api.entity.*;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.tbsapi.client.TbsCrmClient;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tbscommon.vo.PersonVO;
import com.topsec.tos.api.client.TosEmployeeClient;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.vo.FlowStateInfoVo;
import com.topsec.vo.task.TfsTaskVo;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 业绩上报流程
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class PerformanceReportProcessService extends AbstractProjectProcessService<PerformanceReportFlowLaunchDTO> implements ProcessBeforeFillingValidator<String> {

    private final RemoteAgentService remoteAgentService;
    private final TbsCrmClient tbsCrmClient;
    private final RemoteProjectProductOwnClient remoteProjectProductOwnClient;
    private final RemoteProjectDirectlyClient remoteProjectDirectlyClient;
    private final RemoteProjectOutsourceServiceClient remoteProjectOutsourceServiceClient;
    private final PerformanceReportService performanceReportService;
    private final PerformanceReportMapper performanceReportMapper;
    private final PerformanceReportContractDeliveryService performanceReportContractDeliveryService;
    private final PerformanceReportPaymentTermsService performanceReportPaymentTermsService;
    private final PerformanceReportProductOwnService performanceReportProductOwnService;
    private final PerformanceReportDocService performanceReportDocService;
    private final RemoteCrmPerformanceReportService remoteCrmPerformanceReportService;
    private final RemoteAccessConfigClient remoteAccessConfigClient;
    private final RemoteProductService remoteProductService;
    private final RemoteAgentSamplePackageService remoteAgentSamplePackageService;
    private final RemoteMaterialApplyMainService remoteMaterialApplyMainService;
    private final MilitaryProductInspectionService militaryProductInspectionService;
    private final PriceReviewMainService priceReviewMainService;
    private final PerformanceReportContractDeliveryDetailMapper performanceReportContractDeliveryDetailMapper;
    private final RemoteProjectProductSnClient remoteProjectProductSnClient;
    private final RemoteProductSeparationRelService remoteProductSeparationRelService;
    private final RemoteProductAgentInventoryService remoteProductAgentInventoryService;
    private final RemoteAgentRebateService remoteAgentRebateService;
    private final RemoteProjectAgentService remoteProjectAgentService;
    private final TosEmployeeClient tosEmployeeClient;
    private final ITargetedInventoryPreparationMainService targetedInventoryPreparationMainService;
    private final RemoteCrmAgentPaymentVerificationService remoteCrmAgentPaymentVerificationService;

    @Override
    protected String afterEngineLaunch(ProcessExtensionInfo processInfo, PerformanceReportFlowLaunchDTO launchable) {
        PerformanceReportVO baseInfo = launchable.getBaseInfo();

        String processInstanceId = processInfo.getProcessInstanceId();
        PerformanceReport performanceReport= PerformanceReportConvertor.INSTANCE.toReport(baseInfo);
        performanceReport.setProjectId(launchable.getProjectId());
        performanceReport.setReportStatus(0);
        performanceReportService.save(performanceReport);


        List<PerformanceReportContractDelivery> contractDeliveryList = generateContractDelivertList(launchable.getContractDeliveryList(), processInstanceId);
        performanceReportContractDeliveryService.saveBatch(contractDeliveryList);

        List<PerformanceReportPaymentTermsDTO> paymentTermsDTOs = launchable.getPaymentTermsList();
        List<PerformanceReportPaymentTerms> paymentTermsList = PerformanceReportConvertor.INSTANCE.toPaymentTermsList(paymentTermsDTOs);
        for (PerformanceReportPaymentTerms paymentTerms : paymentTermsList) {
            paymentTerms.setProcessInstanceId(processInstanceId);
        }
        performanceReportPaymentTermsService.saveBatch(paymentTermsList);

        // 产品保存
        List<PerformanceReportFlowLaunchDTO.PerformanceReportProductOwnLaunchDTO> productVOList = ListUtils.emptyIfNull(launchable.getProductList());
        List<String> productIdSet = productVOList.stream().map(PerformanceReportFlowLaunchDTO.PerformanceReportProductOwnLaunchDTO::getProductId).distinct()
                .toList();
        Map<String, String> specificationIdMap = Optional.ofNullable(remoteProductService.batchGetInfo(productIdSet))
                .map(JsonObject::getObjEntity)
                .orElse(Collections.emptyList())
                .stream().collect(Collectors.toMap(CrmProductVo::getId,v->{
                    return Optional.ofNullable(v.getSpecificationId()).orElse(StringUtils.EMPTY);
                }));

        List<PerformanceReportProductOwn> productOwnList = productVOList.stream().map(productOwnVO -> {
            PerformanceReportProductOwn productOwn = PerformanceReportConvertor.INSTANCE.toProductOwn(productOwnVO);
            List<CrmProjectProductSnVO> snVOS = ListUtils.emptyIfNull(productOwnVO.getCrmProjectProductSn());
            boolean hasZXBH = snVOS.stream().map(CrmProjectProductSnVO::getType).anyMatch("专项备货"::equals);
            productOwn.setIsSpecialItem(hasZXBH?1:0);

            boolean hasJSY = snVOS.stream().map(CrmProjectProductSnVO::getType).anyMatch(type -> "借试用".equals(type)||"借转销".equals(type));
            productOwn.setIsBorrowForForward(hasJSY?1:0);

            //型号
            String specificationId = specificationIdMap.get(productOwn.getProductId());
            productOwn.setSpecificationId(specificationId);
            productOwn.setProcessInstanceId(processInstanceId);
            productOwn.setReportStatus(1);
            //计算返点总价
            BigDecimal productNum = BigDecimal.valueOf(Optional.ofNullable(productOwn.getProductNum()).orElse(0));
            BigDecimal rebatePrice = Optional.ofNullable(productOwn.getRebatePrice()).orElse(BigDecimal.ZERO);
            productOwn.setRebateTotalPrice(productNum.multiply(rebatePrice));

            return productOwn;
        }).toList();


        performanceReportProductOwnService.saveBatch(productOwnList);

        //附件保存
        List<PerformanceReportDoc> docList = PerformanceReportConvertor.INSTANCE.toDocList(launchable.getAttachmentList());
        for (PerformanceReportDoc doc : docList) {
            doc.setUploadName(UserInfoHolder.getCurrentPersonName());
            doc.setDocTime(LocalDateTime.now());
            doc.setProcessInstanceId(processInstanceId);
        }
        performanceReportDocService.saveBatch(docList);

        //更新渠道基础信息
        updateAgentPartInfo(baseInfo);

        //产品拆行
        remoteProjectProductOwnClient
                .releaseComponent(launchable.getProjectId(), productOwnList.stream().map(PerformanceReportProductOwn::getProjectRecordId).collect(Collectors.toList()));


        //更新产品上报状态
        List<String> recordIdList = productOwnList.stream().map(PerformanceReportProductOwn::getProjectRecordId).collect(Collectors.toList());
        String supplierName = baseInfo.getSupplierName();
        updateProductOwnReportStatus(supplierName, recordIdList,1);

        //业绩上报返点
        BigDecimal rebateTotal = productOwnList.stream().map(PerformanceReportProductOwn::getRebateTotalPrice)
                .filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        //有返点才走
        if (rebateTotal.compareTo(BigDecimal.ZERO) > 0) {
            CrmAgentRebatePerformanceInput rebatePerformanceInput=new CrmAgentRebatePerformanceInput();
            rebatePerformanceInput.setBusinessType(AgentRebateEnum.BusinessTypeEnum.PERFORMANCE_REPORT.getCode());
            rebatePerformanceInput.setOperationType(AgentRebateEnum.OperationTypeEnum.APPLY.getCode());
            // inAgentld(签订公司agentld),inAgentLevel(签订公司身份),projectNo(项目单
            //         号),processNumber(业绩上报单号),processRebate(业绩上报返点)
            String supplierId = baseInfo.getSupplierId();
            rebatePerformanceInput.setInAgentId(supplierId);
            Integer level = Optional.ofNullable(remoteAgentService.getAgentInfo(supplierId))
                    .map(JsonObject::getObjEntity).map(CrmAgentVo::getLevel).orElse(null);
            rebatePerformanceInput.setInAgentLevel(level);
            String projectNumber = Optional.ofNullable(remoteProjectAgentService.queryInfoByProjectId(baseInfo.getProjectId()))
                    .map(JsonObject::getObjEntity).map(BriefInfo::getNumber).orElse(null);
            rebatePerformanceInput.setProjectNo(projectNumber);
            rebatePerformanceInput.setProcessNumber(processInfo.getProcessNumber());
            rebatePerformanceInput.setProcessRebate(rebateTotal);
            remoteAgentRebateService.rebateCountPerformance(rebatePerformanceInput);

        }
        //负责人默认就是流程发起人
        performanceReport.setOwnId(UserInfoHolder.getCurrentPersonId());
        performanceReport.setOwnName(UserInfoHolder.getCurrentPersonName());

        //预付款信息
        performanceReport.setPrePaymentVerificationList(launchable.getPrePaymentVerificationList());
        return performanceReport.getId();
    }

    //更新部分渠道数据
    private void updateAgentPartInfo(PerformanceReportVO baseInfo){
        CrmAgentVo crmAgentVo=new CrmAgentVo();
        crmAgentVo.setMobile(baseInfo.getChannelPhone());
        crmAgentVo.setBankAccount(baseInfo.getChannelBankAccount());
        crmAgentVo.setDepositBank(baseInfo.getChannelBankName());
        crmAgentVo.setDetailAddress(baseInfo.getChannelContactAddress());
        CrmAgentContactsVo crmAgentContactsVo=new CrmAgentContactsVo();
        crmAgentContactsVo.setId(baseInfo.getChannelContactId());
        crmAgentContactsVo.setEmail(baseInfo.getChannelContactEmail());
        crmAgentContactsVo.setPhone(baseInfo.getChannelContactWay());
        crmAgentVo.setContacts(Collections.singletonList(crmAgentContactsVo));
        remoteAgentService.updateAgentPartInfo(crmAgentVo);
    }

    @Override
    public ProcessTypeEnum processType() {
        return ProcessTypeEnum.PERFORMANCE_REPORT;
    }

    private void validate(PerformanceReportFlowLaunchDTO launchable) {
        validateSale(launchable);
        validatePaymentTerms(launchable);
        validateSn(launchable);
        validateOnceAgain(launchable);
        validateAgent(launchable);
        validateSample(launchable);
        validateDisableMaterialCode(launchable);
        validateTipProduct(launchable);

    }

    // 合同评审、业绩上报00发送至01时，如果产品行存在”发起但未生效专项备货流程号“，则此产品行不允许发起合同评审/业绩上报，
    // 由销售自行决定是”变更专项备货“还是”合同评审/业绩上报中此行不选；（流程流转至06步及以后即为流程生效）
    public void validateTipProduct(PerformanceReportFlowLaunchDTO launchable) {

        List<PerformanceReportFlowLaunchDTO.PerformanceReportProductOwnLaunchDTO> productList = ListUtils.emptyIfNull(launchable.getProductList());
        List<String> recordIds = productList.stream()
                .map(PerformanceReportFlowLaunchDTO.PerformanceReportProductOwnLaunchDTO::getProjectRecordId).toList();
        Map<String, TargetedInventoryPreparationOwnVO> tipByRecordId =targetedInventoryPreparationMainService.getTargetedInventoryPreparationOwnVOMapByRowIds(recordIds);
        for (PerformanceReportFlowLaunchDTO.PerformanceReportProductOwnLaunchDTO product : productList) {
            String projectRecordId = product.getProjectRecordId();
            boolean test = ContractUtils.tipPredicate(tipByRecordId, projectRecordId);
            if (test){
                throw new CrmException("您选择的产品行存在于已发起但未生效专项备货流程中！");
            }
        }
    }



    private void validateSale(PerformanceReportFlowLaunchDTO launchable){
        PerformanceReportVO baseInfo = launchable.getBaseInfo();
        Integer projectType = baseInfo.getProjectType();

        //渠道项目校验saleName
        if(Objects.equals(projectType, 2)){
            String saleName = baseInfo.getSaleName();
            if (StringUtils.isNotBlank(saleName)){
                String saleId =  Optional.ofNullable(tosEmployeeClient.existNameJobNo(saleName))
                        .map(JsonObject::getObjEntity).orElse(null);
                if (StringUtils.isBlank(saleId)){
                    throw new CrmException("销售人员[%s]不存在".formatted(saleName));
                }else {
                    baseInfo.setSaleId(saleId);
                }
            }
        }
    }

    // 业绩上报中付款条款填写内容有调整，有明确条款时，填写应付时间和应付金额，无明确条款时，填写付款条款内容，
    private void validatePaymentTerms(PerformanceReportFlowLaunchDTO launchable) {
        List<PerformanceReportPaymentTermsDTO> paymentTermsList = launchable.getPaymentTermsList();
        if (CollectionUtils.isNotEmpty(paymentTermsList)) {
            for (PerformanceReportPaymentTermsDTO paymentTerms : paymentTermsList) {
                if (paymentTerms.getTermType() == 1) {
                    Assert.notNull(paymentTerms.getDueDate(), "付款条款中，有明确条款时，填写应付时间不能为空");
                    Assert.notNull(paymentTerms.getAmountDue(), "付款条款中，有明确条款时，填写应付金额不能为空");
                } else {
                    Assert.hasText(paymentTerms.getTermDescription(), "付款条款中，无明确条款时，填写付款条款内容不能为空");
                }
            }
        }
    }

    //校验多次发起
    private void validateOnceAgain(PerformanceReportFlowLaunchDTO launchDTO){
        Set<String> inputRecordIds = launchDTO.getProductList().stream().map(PerformanceReportFlowLaunchDTO.PerformanceReportProductOwnLaunchDTO::getProjectRecordId)
                .collect(Collectors.toSet());
        List<String> processInstanceIds = performanceReportMapper.selectList(new QueryWrapper<PerformanceReport>().eq("project_id", launchDTO.getProjectId()))
                .stream().map(PerformanceReport::getProcessInstanceId).collect(Collectors.toList());
        String currentProcessInstanceId = launchDTO.getProcessInstanceId();
        //00步编辑
        if (StringUtils.isNotBlank(currentProcessInstanceId)){
            processInstanceIds.remove(currentProcessInstanceId);
        }
        if (CollectionUtils.isNotEmpty(processInstanceIds)){
            Set<String> launchedRecordIds = performanceReportProductOwnService.list(new QueryWrapper<PerformanceReportProductOwn>()
                            .in("process_instance_id", processInstanceIds))
                    .stream()
                    .map(PerformanceReportProductOwn::getProjectRecordId)
                    .collect(Collectors.toSet());
            Collection<String> intersection = CollectionUtils.intersection(launchedRecordIds, inputRecordIds);
            if(CollectionUtils.isNotEmpty(intersection)){
                throw new CrmException("您选择的产品行存在已发起或被拒单的业绩上报产品，不可重复提交！");
            }
        }
    }

    //校验渠道
    private void validateAgent(PerformanceReportFlowLaunchDTO launchable){
        PerformanceReportVO baseInfo = launchable.getBaseInfo();
        String supplierId = baseInfo.getSupplierId();
        String channelCompanyId = baseInfo.getChannelCompanyId();
        String customerId = baseInfo.getCustomerId();

        Set<String> agentIds = Stream.of(supplierId, channelCompanyId, customerId).filter(Objects::nonNull).collect(Collectors.toSet());

        List<CrmAgentVo> blacklistAgent = Optional.ofNullable(remoteAgentService.filterBlacklistAgent(agentIds))
                .map(JsonObject::getObjEntity)
                .orElse(Collections.emptyList());
        //  您当前关联的渠道(xxx公司)处于黑
        // 名单状态，请及时联系渠道管理中心张玉卓(10092)申请解除，否决将无法进行后续下单流程
        if (CollectionUtils.isNotEmpty(blacklistAgent)){
            throw new CrmException(String.format("您当前关联的渠道(%s)处于黑名单状态，请及时联系渠道管理中心张玉卓(10092)申请解除",
                    blacklistAgent.stream().map(CrmAgentVo::getAgentName).collect(Collectors.joining(","))));
        }
    }

    //校验sn
    private void validateSn(PerformanceReportFlowLaunchDTO launchable){
        List<PerformanceReportFlowLaunchDTO.PerformanceReportProductOwnLaunchDTO> productList = ListUtils.emptyIfNull(launchable.getProductList());
        String keyIndustryStuffCode = productList.stream().filter(product -> {
                    List<CrmProjectProductSnVO> crmProjectProductSn = ListUtils.emptyIfNull(product.getCrmProjectProductSn());
                    return crmProjectProductSn.stream().map(CrmProjectProductSnVO::getType).anyMatch("重点行业"::equals);
                }).map(PerformanceReportFlowLaunchDTO.PerformanceReportProductOwnLaunchDTO::getStuffCode)
                .collect(Collectors.joining(","));
        if(StringUtils.isNotBlank(keyIndustryStuffCode)){
            throw new CrmException(String.format("您选择的产品中包含绑定了重点行业序列号的物料代码[%s]，不可发起业绩上报流程。", keyIndustryStuffCode));
        }

        //1、如果选择业绩上报的产品中存在关联了【借试用、借转销、专项备货】的序列号，提交时校验选择的供货方为非国代时，
        // 给予报错提醒：“已选产品中存在关联了借试用、借转销或专项备货的序列号的产品行，只允许从国代出货，请调整供货方后重试“

        List<PerformanceReportFlowLaunchDTO.PerformanceReportProductOwnLaunchDTO> filterThreeProductList = productList
                .stream()
                .filter(product -> {
                    List<CrmProjectProductSnVO> crmProjectProductSn = ListUtils.emptyIfNull(product.getCrmProjectProductSn());
                    return crmProjectProductSn.stream()
                            .map(CrmProjectProductSnVO::getType)
                            .anyMatch(type -> {
                                return "借试用".equals(type) || "借转销".equals(type) || "专项备货".equals(type);
                            });
                })
                .filter(product -> StringUtils.isNotBlank(product.getStuffCode()))
                .toList();


        List<String> threeStuffCode = filterThreeProductList.stream()
                .map(PerformanceReportFlowLaunchDTO.PerformanceReportProductOwnLaunchDTO::getStuffCode)
                .toList();
        String threeStuffCodeText = String.join(",", threeStuffCode);
        if(StringUtils.isNotBlank(threeStuffCodeText)){
            String supplierId = launchable.getBaseInfo().getSupplierId();
            if(StringUtils.isNotBlank(supplierId)){
                CrmAgentVo crmAgentVo = Optional.ofNullable(remoteAgentService.getAgentInfo(supplierId))
                        .map(JsonObject::getObjEntity)
                        .orElseThrow(()->new CrmException("未查询到[%s]对应供货商".formatted(supplierId)));
                Integer level = crmAgentVo.getLevel();
                AgentEnum.AgentLevelEnum agentLevelEnum = AgentEnum.AgentLevelEnum.fromLevel(level);
                if (!agentLevelEnum.equals(AgentEnum.AgentLevelEnum.NATIONAL_DISTRIBUTOR)){
                    throw new CrmException("您选择的产品中存在关联了借试用、借转销或专项备货的序列号的产品行，只允许从国代出货，请调整供货方后重试");
                }
            }
        }

        validSpecialSn(filterThreeProductList);
    }

    //发起业绩上报时，校验关联的【借试用、借转销、专项备货】的序列号对应的物料代码必须是软硬分离里的硬件代码，
    //如果不是，给报错提示“已选择产品关联的序列号为一体机物料代码，不可通过业绩上报下单！”；
    public void validSpecialSn(List<PerformanceReportFlowLaunchDTO.PerformanceReportProductOwnLaunchDTO> filterThreeProductList){

        List<String> threeStuffCode = filterThreeProductList.stream()
                .map(PerformanceReportFlowLaunchDTO.PerformanceReportProductOwnLaunchDTO::getStuffCode)
                .toList();

        Map<String, Set<String>> codeSNCodeMap = filterThreeProductList
                .stream()
                .collect(Collectors.toMap(
                        PerformanceReportFlowLaunchDTO.PerformanceReportProductOwnLaunchDTO::getStuffCode,
                        v -> {
                            return ListUtils.emptyIfNull(v.getCrmProjectProductSn()).stream()
                                    .map(CrmProjectProductSnVO::getDeviceStuffCode)
                                    .filter(StringUtils::isNotBlank)
                                    .collect(Collectors.toSet());
                        },
                        (v1, v2) -> {
                            v1.addAll(v2);
                            return v1;
                        }
                ));
        //主物料代码对应的软硬分离物料代码
        Map<String, List<String>> separationRelMap = Optional.ofNullable(remoteProductSeparationRelService.getCrmProductSeparationRelListByStuffCode(threeStuffCode))
                .map(JsonObject::getObjEntity)
                .orElse(Collections.emptyMap());
        log.info("codeSNCodeMap:{},separationRelMap:{}",codeSNCodeMap,separationRelMap);
        for (Map.Entry<String, List<String>> separationRelMapEntry : separationRelMap.entrySet()) {
            String stuffCode = separationRelMapEntry.getKey();
            Set<String> sns = codeSNCodeMap.get(stuffCode);
            //查出来的没有对应的
            if (CollectionUtils.isEmpty(sns)){
                throw new CrmException("已选择产品关联的序列号为一体机物料代码，不可通过业绩上报下单！");
            }
            List<String> separationRelSn = separationRelMapEntry.getValue();

            if (!new HashSet<>(separationRelSn).containsAll(sns)){
                throw new CrmException("已选择产品关联的序列号为一体机物料代码，不可通过业绩上报下单！");
            }
        }
    }


    //校验是否禁用物料代码
    private void validateDisableMaterialCode(PerformanceReportFlowLaunchDTO launchable){
        List<PerformanceReportFlowLaunchDTO.PerformanceReportProductOwnLaunchDTO> productList = ListUtils.emptyIfNull(launchable.getProductList());
        List<String> productIdList = productList.stream().map(PerformanceReportFlowLaunchDTO.PerformanceReportProductOwnLaunchDTO::getProductId)
                .distinct().toList();

        if(CollectionUtils.isNotEmpty(productIdList)){
            MaterialApplyMainVo materialApplyMainVo = new MaterialApplyMainVo();
            materialApplyMainVo.setProductIds(productIdList);
            materialApplyMainVo.setSourceType(1);
            materialApplyMainVo.setPersonId(UserInfoHolder.getCurrentPersonId());
            materialApplyMainVo.setProOrAgreementId(launchable.getProjectId());
            ExistDisableProductVo existDisableProduct = Optional.ofNullable(remoteMaterialApplyMainService.existDisableProduct(materialApplyMainVo))
                    .map(JsonObject::getObjEntity)
                    .orElse(null);
            if(existDisableProduct!=null){
                List<CrmMaterialDisableVo> disable = existDisableProduct.getDisable();
                List<CrmMaterialDisableVo> control = existDisableProduct.getControl();
                List<MaterialApplyProductDTO> lastProductFlow = existDisableProduct.getLastProductFlow();
                if (CollectionUtils.isNotEmpty(disable)||CollectionUtils.isNotEmpty(control)) {
                    List<String> message=new ArrayList<>();
                    if(CollectionUtils.isNotEmpty(control)){
                        String disableM = disable.stream().map(CrmMaterialDisableVo::getMaterialCode)
                                .distinct()
                                .collect(Collectors.joining(","));
                        message.add("存在限制状态的物料代码[%s]".formatted(disableM));
                    }
                    if(CollectionUtils.isNotEmpty(disable)){
                        String disableM = disable.stream().map(CrmMaterialDisableVo::getMaterialCode)
                                .distinct()
                                .collect(Collectors.joining(","));
                        message.add("存在禁用状态的物料代码[%s]".formatted(disableM));
                    }
                    String formatted = "%s，不可发起业绩上报流程".formatted(String.join(",", message));
                    throw new CrmException(formatted);
                }else if (CollectionUtils.isNotEmpty(lastProductFlow)){

                    Map<String, Long> productCountMapProject = productList.stream()
                            .collect(Collectors.groupingBy(PerformanceReportFlowLaunchDTO.PerformanceReportProductOwnLaunchDTO::getStuffCode,
                                    Collectors.summingLong(PerformanceReportFlowLaunchDTO.PerformanceReportProductOwnLaunchDTO::getProductNum)));
                    Map<String, Long> productCountMap = lastProductFlow.stream()
                            .collect(Collectors.groupingBy(MaterialApplyProductDTO::getMaterialCode, Collectors.summingLong(MaterialApplyProductDTO::getNumber)));
                    String collect = productCountMap.entrySet().stream()
                            .filter(entry -> {
                                String materialCode = entry.getKey();
                                Long count = entry.getValue();
                                Long countProject = productCountMapProject.getOrDefault(materialCode, null);
                                if (countProject != null) {
                                    return count < countProject;
                                }
                                return false;
                            })
                            .map(Map.Entry::getKey)
                            .collect(Collectors.joining(","));
                    if (StringUtils.isNotBlank(collect)){
                        throw new CrmException("存在物料代码[%s]，权限申请的产品数小于项目添加的产品数，不可发起业绩上报流程".formatted(collect));
                    }

                }
            }

        }


    }
    //校验样机
    private void validateSample(PerformanceReportFlowLaunchDTO launchable){
        PerformanceReportVO baseInfo = launchable.getBaseInfo();
        Integer modelMachine = baseInfo.getModelMachine();
        String currentAgentId = UserInfoHolder.getCurrentAgentId();
        if (CollectionUtils.isEmpty(launchable.getProductList())) return;
        //是样机
        if (Objects.equals(modelMachine,1)){
            Map<String, Integer> inputCountMap = launchable.getProductList().stream()
                    .collect(Collectors.groupingBy(PerformanceReportFlowLaunchDTO.PerformanceReportProductOwnLaunchDTO::getProductId,
                            Collectors.summingInt(item -> {
                                return item.getProductNum().intValue();
                            })));
            validateSample(currentAgentId,inputCountMap);

        }
    }

    public void validateSample(String currentAgentId, Map<String, Integer> inputCountMap){
        List<String> productIds = inputCountMap.keySet().stream().distinct().toList();
        Map<String, Integer> sampleCountMap = Optional.ofNullable(remoteAgentSamplePackageService.listSamplePackageProductByAgentId(currentAgentId, productIds))
                .map(JsonObject::getObjEntity)
                .orElse(Collections.emptyList())
                .stream().collect(Collectors.toMap(PackageProductInfoVO::getProductId, PackageProductInfoVO::getProductNum));
        Map<String, Integer> usedCountMap = performanceReportMapper.listYearSamplePackageProductByAgentId(currentAgentId, productIds).stream()
                .collect(Collectors.toMap(PackageProductInfoVO::getProductId, PackageProductInfoVO::getProductNum));

        for (String productId : productIds) {
            sampleCountMap.putIfAbsent(productId,0);
            usedCountMap.putIfAbsent(productId,0);
            inputCountMap.putIfAbsent(productId,0);
        }
        //合并usedCountMap、inputCountMap 与 总count 对比
        for (Map.Entry<String, Integer> entry : usedCountMap.entrySet()) {
            String productId = entry.getKey();
            Integer usedCount = entry.getValue();
            Integer inputCount = inputCountMap.get(productId);
            Integer sampleCount = sampleCountMap.get(productId);
            if((usedCount + inputCount) > sampleCount){
                // 剩余
                Integer leftCount = sampleCount - usedCount;
                String materialCode = Optional.ofNullable(remoteProductService.getProduct(productId))
                        .map(JsonObject::getObjEntity)
                        .map(CrmProductVo::getMaterialCode).orElse(null);
                throw new CrmException("【物料代码%s】已达到备货数量上限【%s】，当前剩余可备货【%s】".formatted(materialCode,sampleCount,leftCount));
            }
        }
    }

    @Override
    protected void preProcess(PerformanceReportFlowLaunchDTO launchable) {

        validate(launchable);

        launchable.setProcessBusinessType(1);
        PerformanceReportVO baseInfo = launchable.getBaseInfo();
        launchable.setProjectId(StringUtils.firstNonBlank(baseInfo.getProjectId(),launchable.getProjectId()));
        Integer supplierType = baseInfo.getSupplierType();
        if (supplierType==null){
            throw new CrmException("供货商类型不能为空");
        }
        String currentAgentId = UserInfoHolder.getCurrentAgentId();
        if (StringUtils.isNotBlank(currentAgentId)){
            launchable.setCompanyId(currentAgentId);
        }
        Map<String,Object> variables = new HashMap<>();
        String agentId = baseInfo.getSupplierId();
        Set<String> assigneeList = Optional.ofNullable(tbsCrmClient.listPersonAccountByAgentId(agentId))
                .map(JsonObject::getObjEntity)
                .orElse(Collections.emptyList())
                .stream()
                .map(PersonVO::getAccountId)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(assigneeList)){
            throw new CrmException("该供应商暂无账号，无法发起业绩上报流程");
        }
        variables.put("assigneeList",assigneeList);
        //是否省代
        variables.put("supplyMethod",supplierType==2);
        launchable.setVariables(variables);

    }

    //上报或合同状态 0-待上报或待发起 1-上报中或合同审批中 2-已上报或合同已完成
    private void updateProductOwnReportStatus(String processInstanceId,Integer status){
        String supplierName = performanceReportService.getOne(new QueryWrapper<PerformanceReport>().eq("process_instance_id", processInstanceId)).getSupplierName();
        List<PerformanceReportProductOwn> productOwns = performanceReportProductOwnService.list(new QueryWrapper<PerformanceReportProductOwn>().eq("process_instance_id", processInstanceId));
        List<String> recordIdList = productOwns.stream().map(PerformanceReportProductOwn::getProjectRecordId).collect(Collectors.toList());
        updateProductOwnReportStatus(supplierName,recordIdList,status);
    }

    //更新产品上报状态
    private void updateProductOwnReportStatus(String supplierName,List<String> recordIdList,Integer status){
        List<CrmProjectProductOwnUpdateVO> updateVOS = recordIdList.stream()
                .map(recordId -> {
                    CrmProjectProductOwnUpdateVO crmProjectProductOwnUpdateVO = new CrmProjectProductOwnUpdateVO();
                    crmProjectProductOwnUpdateVO.setId(recordId);
                    crmProjectProductOwnUpdateVO.setReportStatus(status);
                    crmProjectProductOwnUpdateVO.setSupplier(supplierName);
                    return crmProjectProductOwnUpdateVO;
                }).collect(Collectors.toList());
        //产品业绩上报已完成
        remoteProjectProductOwnClient.writeBack(updateVOS);
    }



    @Override
    public void handleProcessing(FlowStateInfoVo flowInfo) {
        if ("sid-4D6C16B8-18F0-4956-9F22-016B1F940CD4".equals(flowInfo.getCurrentActivityId())){
            String processInstanceId = flowInfo.getProcessInstanceId();
            PerformanceReport performanceReport = performanceReportService.getOne(new QueryWrapper<PerformanceReport>().eq("process_instance_id", processInstanceId));
            // 业绩上报状态
            performanceReport.setReportStatus(1);
            performanceReport.setEffectiveTime(LocalDateTime.now());
            performanceReportService.updateById(performanceReport);

            PerformanceReportDetailInfo performanceReportDetailInfo = performanceReportService.getPerformanceReportDetailsByProcessInstanceId(processInstanceId);
            // 业绩上报产品状态
            List<PerformanceReportProductOwnDTO> productList = performanceReportDetailInfo.getProductList();
            for (PerformanceReportProductOwnDTO performanceReportProductOwnDTO : productList){
                performanceReportProductOwnDTO.setReportStatus(2);
                PerformanceReportProductOwn performanceReportProductOwn = HyperBeanUtils.copyProperties(performanceReportProductOwnDTO, PerformanceReportProductOwn::new);
                performanceReportProductOwnService.updateById(performanceReportProductOwn);
            }
            // 写入主表
            remoteCrmPerformanceReportService.saveValidData(performanceReportDetailInfo.getBaseInfo());
            // 写入产品
            remoteCrmPerformanceReportService.saveProductData(productList, processInstanceId);
            // 写入附件
            remoteCrmPerformanceReportService.savePerformanceReportDoc(performanceReportDetailInfo.getAttachmentList());
            // 写入合同发货
            List<String> stringList = performanceReportDetailInfo.getContractDeliveryList().stream().map(PerformanceReportContractDeliveryDTO::getId).collect(Collectors.toList());
            List<PerformanceReportContractDeliveryDetail> performanceReportContractDeliveryDetailList = performanceReportContractDeliveryDetailMapper.selectList(new QueryWrapper<PerformanceReportContractDeliveryDetail>().in("contract_delivery_id", stringList));
            List<PerformanceReportContractDeliveryDetailDTO> performanceReportContractDeliveryDetailDTOS = HyperBeanUtils.copyListPropertiesByJackson(performanceReportContractDeliveryDetailList, PerformanceReportContractDeliveryDetailDTO.class);
            ContractDeliveryDetailVO contractDeliveryDetailVO = new ContractDeliveryDetailVO();
            contractDeliveryDetailVO.setPerformanceReportContractDeliveryDTOS(performanceReportDetailInfo.getContractDeliveryList());
            contractDeliveryDetailVO.setPerformanceReportContractDeliveryDetailDTOS(performanceReportContractDeliveryDetailDTOS);
            remoteCrmPerformanceReportService.savePerformanceReportContractDelivery(contractDeliveryDetailVO, processInstanceId);
            // 写入付款信息
            remoteCrmPerformanceReportService.savePerformanceReportPaymentInfo(performanceReportDetailInfo.getPaymentInfoList());
            // 写入付款条款
            remoteCrmPerformanceReportService.savePerformanceReportPaymentTerms(performanceReportDetailInfo.getPaymentTermsList());

            //业绩上报返点
            BigDecimal rebateTotal = productList.stream().map(PerformanceReportProductOwnDTO::getRebateTotalPrice)
                    .filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            //有返点才走
            if (rebateTotal.compareTo(BigDecimal.ZERO) > 0) {
                // 生效使用返点
                CrmAgentRebatePerformanceInput crmAgentRebatePerformanceInput = new CrmAgentRebatePerformanceInput();
                crmAgentRebatePerformanceInput.setBusinessType(AgentRebateEnum.BusinessTypeEnum.PERFORMANCE_REPORT.getCode());
                crmAgentRebatePerformanceInput.setOperationType(AgentRebateEnum.OperationTypeEnum.EFFECT.getCode());
                crmAgentRebatePerformanceInput.setProcessNumber(performanceReport.getProcessNumber());
                crmAgentRebatePerformanceInput.setProcessEffectTime(performanceReport.getEffectiveTime());
                crmAgentRebatePerformanceInput.setContractNumber(performanceReport.getSigningContractNumber());
                remoteAgentRebateService.rebateCountPerformance(crmAgentRebatePerformanceInput);
            }
        }else if ("__initiator__".equals(flowInfo.getCurrentActivityId())){

        }

    }



    @Override
    public void handlePass(FlowStateInfoVo flowInfo) {
        String processInstanceId = flowInfo.getProcessInstanceId();

        PerformanceReport performanceReport = performanceReportService.getOne(new QueryWrapper<PerformanceReport>().eq("process_instance_id", processInstanceId));
        // 写入生效数据
        if (performanceReport.getAcceptOrder() == 1){
            updateProductOwnReportStatus(processInstanceId,2);
            // 业绩上报办结时间
            performanceReport.setProcessPassTime(LocalDateTime.now());
            performanceReportService.updateById(performanceReport);
            PerformanceReportDetailInfo performanceReportDetailInfo = performanceReportService.getPerformanceReportDetailsByProcessInstanceId(processInstanceId);
            // 写入主表
            remoteCrmPerformanceReportService.saveValidData(performanceReportDetailInfo.getBaseInfo());
            // 写入产品
            List<PerformanceReportProductOwnDTO> productList = performanceReportDetailInfo.getProductList();
            remoteCrmPerformanceReportService.saveProductData(productList, processInstanceId);
            // 写入合同发货
            List<String> stringList = performanceReportDetailInfo.getContractDeliveryList().stream().map(PerformanceReportContractDeliveryDTO::getId).collect(Collectors.toList());
            List<PerformanceReportContractDeliveryDetail> performanceReportContractDeliveryDetailList = performanceReportContractDeliveryDetailMapper.selectList(new QueryWrapper<PerformanceReportContractDeliveryDetail>().in("contract_delivery_id", stringList));
            List<PerformanceReportContractDeliveryDetailDTO> performanceReportContractDeliveryDetailDTOS = HyperBeanUtils.copyListPropertiesByJackson(performanceReportContractDeliveryDetailList, PerformanceReportContractDeliveryDetailDTO.class);
            ContractDeliveryDetailVO contractDeliveryDetailVO = new ContractDeliveryDetailVO();
            contractDeliveryDetailVO.setPerformanceReportContractDeliveryDTOS(performanceReportDetailInfo.getContractDeliveryList());
            contractDeliveryDetailVO.setPerformanceReportContractDeliveryDetailDTOS(performanceReportContractDeliveryDetailDTOS);
            remoteCrmPerformanceReportService.savePerformanceReportContractDelivery(contractDeliveryDetailVO, processInstanceId);
            // 写入付款信息
            remoteCrmPerformanceReportService.savePerformanceReportPaymentInfo(performanceReportDetailInfo.getPaymentInfoList());

            List<CrmProjectProductSnVO> crmProjectProductSnVOS = new ArrayList<>();
            if (performanceReport.getSupplierType() == 1){
                for (PerformanceReportProductOwnDTO performanceReportProductOwnDTO : productList) {
                    List<PerformanceReportProductSeparationDTO> performanceReportProductSeparationDTOS = performanceReportProductOwnDTO.getPerformanceReportProductSeparationDTOS();
                    for (PerformanceReportProductSeparationDTO performanceReportProductSeparationDTO : performanceReportProductSeparationDTOS) {
                        List<PerformanceReportProductSeparationSnDTO> performanceReportProductSeparationSnDTOS = performanceReportProductSeparationDTO.getPerformanceReportProductSeparationSnDTOS();
                        for (PerformanceReportProductSeparationSnDTO performanceReportProductSeparationSnDTO : performanceReportProductSeparationSnDTOS) {
                            CrmProjectProductSnVO crmProjectProductSnVO = new CrmProjectProductSnVO();
                            crmProjectProductSnVO.setRecordId(performanceReportProductOwnDTO.getProjectRecordId());
                            crmProjectProductSnVO.setStuffCode(performanceReportProductOwnDTO.getStuffCode());
                            crmProjectProductSnVO.setPsn(performanceReportProductSeparationSnDTO.getPsn());
                            crmProjectProductSnVO.setType("业绩上报");
                            crmProjectProductSnVOS.add(crmProjectProductSnVO);
                        }
                    }
                }
                remoteProjectProductSnClient.addProductSnFromPerformanceReport(crmProjectProductSnVOS);
            }else if (performanceReport.getSupplierType() == 2){
                for (PerformanceReportProductOwnDTO performanceReportProductOwnDTO : productList) {
                    List<PerformanceReportProductOwnSnDTO> performanceReportProductOwnSnDTOS = performanceReportProductOwnDTO.getPerformanceReportProductOwnSnDTOS();
                    for (PerformanceReportProductOwnSnDTO performanceReportProductOwnSnDTO : performanceReportProductOwnSnDTOS) {
                        CrmProjectProductSnVO crmProjectProductSnVO = new CrmProjectProductSnVO();
                        crmProjectProductSnVO.setRecordId(performanceReportProductOwnDTO.getProjectRecordId());
                        crmProjectProductSnVO.setStuffCode(performanceReportProductOwnDTO.getStuffCode());
                        crmProjectProductSnVO.setPsn(performanceReportProductOwnSnDTO.getPsn());
                        crmProjectProductSnVO.setType("业绩上报");
                        crmProjectProductSnVOS.add(crmProjectProductSnVO);
                    }
                }
                remoteProjectProductSnClient.addProductSnFromPerformanceReport(crmProjectProductSnVOS);
            }
            // 解除库存锁定
            //remoteProductAgentInventoryService.unLockPerformanceReportInventory(performanceReport.getProcessNumber());
            //业绩上报返点
            BigDecimal rebateTotal = productList.stream().map(PerformanceReportProductOwnDTO::getRebateTotalPrice)
                    .filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            //有返点才走
            if (rebateTotal.compareTo(BigDecimal.ZERO) > 0) {
                // 办结返点
                CrmAgentRebatePerformanceInput crmAgentRebatePerformanceInput1 = new CrmAgentRebatePerformanceInput();
                crmAgentRebatePerformanceInput1.setBusinessType(AgentRebateEnum.BusinessTypeEnum.PERFORMANCE_REPORT.getCode());
                crmAgentRebatePerformanceInput1.setOperationType(AgentRebateEnum.OperationTypeEnum.COMPLETED.getCode());
                crmAgentRebatePerformanceInput1.setProcessNumber(performanceReport.getProcessNumber());
                crmAgentRebatePerformanceInput1.setProcessEndTime(performanceReport.getProcessPassTime());
                crmAgentRebatePerformanceInput1.setContractDigest(rebateTotal);
                //价差
                BigDecimal priceDifferenceTotal = performanceReportMapper.selectPriceDifferenceTotal(performanceReport.getProcessInstanceId());
                crmAgentRebatePerformanceInput1.setContractPriceDifference(priceDifferenceTotal);
                // 软件0消耗
                BigDecimal osConsumePriceTotal = performanceReportMapper.selectOSConsumePriceTotal(performanceReport.getProcessInstanceId());
                crmAgentRebatePerformanceInput1.setContractReplaceSoftware(osConsumePriceTotal);
                remoteAgentRebateService.rebateCountPerformance(crmAgentRebatePerformanceInput1);
            }
            // 生效业绩上报（写入最终表后调用）-付款信息-回款核销
            remoteCrmAgentPaymentVerificationService.savePaymentVerificationByPerformance(performanceReportDetailInfo.getPaymentInfoList());
            // 预付款-汇款核销

        }else if (performanceReport.getSupplierType() == 1 && performanceReport.getAcceptOrder() == 0){
            // 解除产品锁定状态
            updateProductOwnReportStatus(processInstanceId,0);
            // 业绩上报状态
            performanceReport.setReportStatus(2);
            performanceReportService.updateById(performanceReport);
            // 取消通路限制（允许走合同评审）
            CrmProjectDirectlyVo crmProjectDirectlyVo = remoteProjectDirectlyClient.getProjectInfo(performanceReport.getProjectId()).getObjEntity();
            CrmProjectProductAccessSwitchingVo crmProjectProductAccessSwitchingVo = new CrmProjectProductAccessSwitchingVo();
            crmProjectProductAccessSwitchingVo.setProcessInstanceId(processInstanceId);
            crmProjectProductAccessSwitchingVo.setProjectId(performanceReport.getProjectId());
            crmProjectProductAccessSwitchingVo.setProjectNo(crmProjectDirectlyVo.getProjectNo());
            crmProjectProductAccessSwitchingVo.setTargerSwitching(1);
            crmProjectProductAccessSwitchingVo.setRangeSwitching(1);
            List<CrmProjectProductAccessSwitchingDetailVO> crmProjectProductAccessSwitchingDetailVOS = new ArrayList<>();
            // 自有产品
            List<PerformanceReportProductOwn> reportProductOwnList = performanceReportProductOwnService.
                    list(new QueryWrapper<PerformanceReportProductOwn>()
                            .eq("process_instance_id", processInstanceId)
                            .eq("del_flag", 0));
            List<CrmProjectProductOwnVO> crmProjectProductOwnVOList = remoteProjectProductOwnClient.queryProjectProductOwnStore(performanceReport.getProjectId()).getObjEntity();
            reportProductOwnList.stream().forEach(performanceReportProductOwn -> {
                CrmProjectProductOwnVO crmProjectProductOwnVO1 = crmProjectProductOwnVOList.stream().filter(crmProjectProductOwnVO -> Objects.equals(crmProjectProductOwnVO.getId(), performanceReportProductOwn.getProjectRecordId())).findFirst().orElse(new CrmProjectProductOwnVO());
                CrmProjectProductAccessSwitchingDetailVO crmProjectProductAccessSwitchingDetailVO = new CrmProjectProductAccessSwitchingDetailVO();
                crmProjectProductAccessSwitchingDetailVO.setProductOwnThirdId(performanceReportProductOwn.getProjectRecordId());
                crmProjectProductAccessSwitchingDetailVO.setProductType(0);
                crmProjectProductAccessSwitchingDetailVO.setStuffCode(performanceReportProductOwn.getStuffCode());
                crmProjectProductAccessSwitchingDetailVO.setProductName(performanceReportProductOwn.getProductName());
                crmProjectProductAccessSwitchingDetailVO.setPnCode(performanceReportProductOwn.getPnCode());
                crmProjectProductAccessSwitchingDetailVO.setProductNum(performanceReportProductOwn.getProductNum());
                crmProjectProductAccessSwitchingDetailVO.setDealPrice(performanceReportProductOwn.getDealPrice());
                crmProjectProductAccessSwitchingDetailVO.setFinalPrice(performanceReportProductOwn.getFinalPrice());
                crmProjectProductAccessSwitchingDetailVO.setDiscount(crmProjectProductOwnVO1.getDiscount());
                crmProjectProductAccessSwitchingDetailVO.setFinalTotalPrice(performanceReportProductOwn.getFinalTotalPrice());
                crmProjectProductAccessSwitchingDetailVOS.add(crmProjectProductAccessSwitchingDetailVO);
            });
            crmProjectProductAccessSwitchingVo.setProductOwn(crmProjectProductAccessSwitchingDetailVOS);
            remoteAccessConfigClient.saveAccessSwitching(crmProjectProductAccessSwitchingVo);
        }
    }



    @Override
    public void handlePassBack(FlowStateInfoVo flowInfo) {
        updateProductOwnReportStatus(flowInfo.getProcessInstanceId(),1);
    }

    @Override
    public void handleProcessingBack(FlowStateInfoVo flowInfo) {
        if ("sid-B961E1EB-8D21-4C72-A853-6D7E1BA27F8E".equals(flowInfo.getCurrentActivityId()) && "sid-0024E5FC-BD42-4351-9D30-2F1A79F29FE3".equals(flowInfo.getCurrentActivityId())){
            String processInstanceId = flowInfo.getProcessInstanceId();
            // 业绩上报状态
            performanceReportService.update(new UpdateWrapper<PerformanceReport>().eq("process_instance_id", processInstanceId).eq("del_flag", 0).set("report_status", 0).set("effective_time", null));
            // 业绩上报产品状态
            performanceReportProductOwnService.update(new UpdateWrapper<PerformanceReportProductOwn>()
                    .eq("del_flag", 0)
                    .eq("process_instance_id", processInstanceId)
                    .set("report_status", 1));

            // 删除生效的数据
            remoteCrmPerformanceReportService.deleteData(processInstanceId);
        }
    }




    @Override
    public ProjectStatusBase projectStatus(Integer projectType) {
        if (projectType==1){
            //公司项目
            return ProjectStatusEnum.DirectlyStatus.CONTRACT_EVALUATION;
        }else {
            //渠道项目
            return ProjectStatusEnum.AgentStatus.CONTRACT;
        }
    }

    @Override
    public Pair<Boolean, String> checkBeforeFilling(String projectId) {

        CheckCondition<String> hasOutCondition = new CheckCondition<>() {
            @Override
            public boolean check(String projectId) {
                return !Optional.ofNullable(remoteProjectOutsourceServiceClient.hasRecord(projectId))
                        .map(JsonObject::getObjEntity).orElse(false);
            }
            @Override
            public String defaultFailureReason() {
                return "项目存在外包服务费，不可发起业绩上报，如有任何疑问可咨询各部门商务助理！";
            }
        };
        CheckCondition<String> existsMilitaryProductCondition = new CheckCondition<>() {
            @Override
            public boolean check(String projectId) {
                return  !militaryProductInspectionService.existsNoMilitaryProductInspectionProcess(projectId);
            }
            @Override
            public String defaultFailureReason() {
                return "项目存在军品检验流程，无法发起业绩上报";
            }
        };

        CheckCondition<String> priceReviewMainCondition = new CheckCondition<>() {
            @Override
            public boolean check(String projectId) {
                return !priceReviewMainService.isTheLatestPassedPriceReviewValid(projectId);
            }
            @Override
            public String defaultFailureReason() {
                return "价审未办结或办结后价格失效，请重新发起价格审批!";
            }
        };


        CheckCondition<String> agentFlowDisableCondition = new CheckCondition<>() {
            @Override
            public boolean check(String projectId) {
                List<String> agentList = Optional.ofNullable(remoteProjectAgentService.querySignCustomIdsByProjectId(projectId))
                        .map(JsonObject::getObjEntity)
                        .orElse(Collections.emptyList());
                if(!agentList.isEmpty()){
                    Boolean disabled = Optional.ofNullable(remoteAgentService.isFlowDisabled(agentList, projectId))
                            .map(JsonObject::getObjEntity)
                            .orElse(false);
                    return !disabled;
                }
                return true;
            }
            @Override
            public String defaultFailureReason() {
                return "当前关联渠道授信额度不足且存在超期，禁止下单";
            }
        };


        HyperValidator<String> validator = HyperValidator.check(projectId, List.of(hasOutCondition, existsMilitaryProductCondition,
                        priceReviewMainCondition,agentFlowDisableCondition),
                HyperValidator.CHECK_MODE.PARALLEL_FAIL_FAST);
        boolean result = validator.getResult();
        if (!result){
            return Pair.of(false, validator.getFailMessage());
        }else {
            return Pair.of(true, null);
        }
    }

    @Override
    protected void edit00Process(PerformanceReportFlowLaunchDTO launchable) {
        preProcess(launchable);

        String processInstanceId = launchable.getProcessInstanceId();
        ProcessExtensionInfo processExtensionInfo = processExtensionInfoMapper.selectOne(new LambdaQueryWrapper<ProcessExtensionInfo>().eq(ProcessExtensionInfo::getProcessInstanceId, processInstanceId));
        String processNumber = processExtensionInfo.getProcessNumber();

        PerformanceReportVO baseInfo = launchable.getBaseInfo();

        PerformanceReport performanceReport= PerformanceReportConvertor.INSTANCE.toReport(baseInfo);
        performanceReport.setProjectId(launchable.getProjectId());
        performanceReport.setReportStatus(0);
        performanceReportService.update(performanceReport,new LambdaQueryWrapper<PerformanceReport>()
                .eq(PerformanceReport::getProcessInstanceId, processInstanceId));


        List<PerformanceReportContractDelivery> contractDeliveryList = generateContractDelivertList(launchable.getContractDeliveryList(), processInstanceId);
        performanceReportContractDeliveryService
                .remove(new LambdaQueryWrapper<PerformanceReportContractDelivery>()
                        .eq(PerformanceReportContractDelivery::getProcessInstanceId, processInstanceId));
        performanceReportContractDeliveryService.saveBatch(contractDeliveryList);

        List<PerformanceReportPaymentTermsDTO> paymentTermsDTOs = launchable.getPaymentTermsList();
        List<PerformanceReportPaymentTerms> paymentTermsList = PerformanceReportConvertor.INSTANCE.toPaymentTermsList(paymentTermsDTOs);
        for (PerformanceReportPaymentTerms paymentTerms : paymentTermsList) {
            paymentTerms.setProcessInstanceId(processInstanceId);
        }
        performanceReportPaymentTermsService.remove(new LambdaQueryWrapper<PerformanceReportPaymentTerms>()
                .eq(PerformanceReportPaymentTerms::getProcessInstanceId, processInstanceId));
        performanceReportPaymentTermsService.saveBatch(paymentTermsList);

        // 产品保存
        List<PerformanceReportFlowLaunchDTO.PerformanceReportProductOwnLaunchDTO> productVOList = ListUtils.emptyIfNull(launchable.getProductList());
        List<String> productIdSet = productVOList.stream().map(PerformanceReportFlowLaunchDTO.PerformanceReportProductOwnLaunchDTO::getProductId).distinct()
                .toList();
        Map<String, String> specificationIdMap = Optional.ofNullable(remoteProductService.batchGetInfo(productIdSet))
                .map(JsonObject::getObjEntity)
                .orElse(Collections.emptyList())
                .stream().collect(Collectors.toMap(CrmProductVo::getId,v->{
                    return Optional.ofNullable(v.getSpecificationId()).orElse(StringUtils.EMPTY);
                }));

        List<PerformanceReportProductOwn> productOwnList = productVOList.stream().map(productOwnVO -> {
            PerformanceReportProductOwn productOwn = PerformanceReportConvertor.INSTANCE.toProductOwn(productOwnVO);
            List<CrmProjectProductSnVO> snVOS = ListUtils.emptyIfNull(productOwnVO.getCrmProjectProductSn());
            boolean hasZXBH = snVOS.stream().map(CrmProjectProductSnVO::getType).anyMatch("专项备货"::equals);
            productOwn.setIsSpecialItem(hasZXBH?1:0);

            boolean hasJSY = snVOS.stream().map(CrmProjectProductSnVO::getType).anyMatch(type -> "借试用".equals(type)||"借转销".equals(type));
            productOwn.setIsBorrowForForward(hasJSY?1:0);

            //型号
            String specificationId = specificationIdMap.get(productOwn.getProductId());
            productOwn.setSpecificationId(specificationId);
            productOwn.setProcessInstanceId(processInstanceId);
            productOwn.setReportStatus(1);
            //计算返点总价
            BigDecimal productNum = BigDecimal.valueOf(Optional.ofNullable(productOwn.getProductNum()).orElse(0));
            BigDecimal rebatePrice = Optional.ofNullable(productOwn.getRebatePrice()).orElse(BigDecimal.ZERO);
            productOwn.setRebateTotalPrice(productNum.multiply(rebatePrice));

            return productOwn;
        }).toList();

        performanceReportProductOwnService.remove(new LambdaQueryWrapper<PerformanceReportProductOwn>()
                .eq(PerformanceReportProductOwn::getProcessInstanceId, processInstanceId));
        performanceReportProductOwnService.saveBatch(productOwnList);

        //附件保存
        List<PerformanceReportDoc> docList = PerformanceReportConvertor.INSTANCE.toDocList(launchable.getAttachmentList());
        for (PerformanceReportDoc doc : docList) {
            doc.setUploadName(UserInfoHolder.getCurrentPersonName());
            doc.setDocTime(LocalDateTime.now());
            doc.setProcessInstanceId(processInstanceId);
        }
        performanceReportDocService.remove(new LambdaQueryWrapper<PerformanceReportDoc>()
                .eq(PerformanceReportDoc::getProcessInstanceId, processInstanceId));
        performanceReportDocService.saveBatch(docList);

        //更新渠道基础信息
        updateAgentPartInfo(baseInfo);

        //产品拆行
        remoteProjectProductOwnClient
                .releaseComponent(launchable.getProjectId(), productOwnList.stream().map(PerformanceReportProductOwn::getProjectRecordId).collect(Collectors.toList()));


        //更新产品上报状态
        List<String> recordIdList = productOwnList.stream().map(PerformanceReportProductOwn::getProjectRecordId).collect(Collectors.toList());
        String supplierName = baseInfo.getSupplierName();
        updateProductOwnReportStatus(supplierName, recordIdList,1);

        //业绩上报返点
        BigDecimal rebateTotal = productOwnList.stream().map(PerformanceReportProductOwn::getRebateTotalPrice)
                .filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        //有返点才走
        if (rebateTotal.compareTo(BigDecimal.ZERO) > 0) {
            CrmAgentRebatePerformanceInput rebatePerformanceInput=new CrmAgentRebatePerformanceInput();
            rebatePerformanceInput.setBusinessType(AgentRebateEnum.BusinessTypeEnum.PERFORMANCE_REPORT.getCode());
            rebatePerformanceInput.setOperationType(AgentRebateEnum.OperationTypeEnum.APPLY.getCode());
            // inAgentld(签订公司agentld),inAgentLevel(签订公司身份),projectNo(项目单
            //         号),processNumber(业绩上报单号),processRebate(业绩上报返点)
            String supplierId = baseInfo.getSupplierId();
            rebatePerformanceInput.setInAgentId(supplierId);
            Integer level = Optional.ofNullable(remoteAgentService.getAgentInfo(supplierId))
                    .map(JsonObject::getObjEntity).map(CrmAgentVo::getLevel).orElse(null);
            rebatePerformanceInput.setInAgentLevel(level);
            String projectNumber = Optional.ofNullable(remoteProjectAgentService.queryInfoByProjectId(baseInfo.getProjectId()))
                    .map(JsonObject::getObjEntity).map(BriefInfo::getNumber).orElse(null);
            rebatePerformanceInput.setProjectNo(projectNumber);
            rebatePerformanceInput.setProcessNumber(processNumber);
            rebatePerformanceInput.setProcessRebate(rebateTotal);
            remoteAgentRebateService.rebateCountPerformance(rebatePerformanceInput);

        }

    }

    private List<PerformanceReportContractDelivery> generateContractDelivertList(List<PerformanceReportContractDeliveryDTO> contractDeliveryDTOList, String processInstanceId) {

        List<PerformanceReportContractDelivery> contractDeliveryList = PerformanceReportConvertor.INSTANCE.toContractDeliveryList(contractDeliveryDTOList);
        for (PerformanceReportContractDelivery delivery : contractDeliveryList) {
            delivery.setId(null);
            delivery.setProcessInstanceId(processInstanceId);
            List<ContractDeliveryProductVO> productVOList = Optional.of(delivery)
                    .map(PerformanceReportContractDelivery::getDeliveryProducts)
                    .orElse(Collections.emptyList())
                    .stream()
                    .filter(item -> {
                        //过滤掉为0的数据
                        if (item==null) return false;
                        Integer num = item.getNum();
                        return num != null && num != 0;
                    }).collect(Collectors.toList());
            delivery.setDeliveryProducts(productVOList);
        }
        return contractDeliveryList;
    }

    @Override
        public Pair<Boolean, String> checkFilling(TfsTaskVo taskVo) {
        return Pair.of(true, null);
    }
}
