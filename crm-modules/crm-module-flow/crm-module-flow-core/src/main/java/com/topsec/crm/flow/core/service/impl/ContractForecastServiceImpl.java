package com.topsec.crm.flow.core.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.topsec.crm.flow.api.dto.contractForecast.ContractForecastFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.contractForecast.ContractForecastUserProjectProductVO;
import com.topsec.crm.flow.api.dto.contractForecast.ContractForecastUserProjectVO;
import com.topsec.crm.flow.api.dto.contractForecast.ContractForecastUserVO;
import com.topsec.crm.flow.core.entity.*;
import com.topsec.crm.flow.core.mapper.ContractForecastMapper;
import com.topsec.crm.flow.core.process.impl.ContractForecastProcessService;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.framework.common.bean.FlowPerson;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.AccountAccquireUtils;
import com.topsec.crm.project.api.client.RemoteProjectDirectlyClient;
import com.topsec.crm.project.api.entity.CrmProjectDirectlyVo;
import com.topsec.crm.project.api.entity.CrmProjectProductOwnVO;
import com.topsec.crm.stats.api.RemoteContractStatsService;
import com.topsec.crm.stats.api.entity.business.ContractStatsResultVO;
import com.topsec.crm.stats.api.entity.business.DeptContractStatsQuery;
import com.topsec.crm.stats.api.entity.business.PersonContractStatsQuery;
import com.topsec.crm.stats.api.entity.home.HomeRankContractForecastVO;
import com.topsec.enums.FormTypeEnum;
import com.topsec.enums.ProcessDefinitionKeyEnum;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsTaskClient;
import com.topsec.tfs.api.client.TfsTodoClient;
import com.topsec.tos.api.client.TosDepartmentClient;
import com.topsec.tos.api.client.TosEmployeeClient;
import com.topsec.tos.api.client.TosTreeClient;
import com.topsec.tos.common.vo.*;
import com.topsec.tos.common.vo.tree.TreeVO;
import com.topsec.vo.task.TfsTaskVo;
import com.topsec.vo.task.TfsToTaskVo;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.topsec.crm.framework.common.name.NameUtils;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-26
 */
@Service
public class ContractForecastServiceImpl extends ServiceImpl<ContractForecastMapper, ContractForecast> implements IContractForecastService {

    @Resource
    private ContractForecastProcessService contractForecastProcessService;

    @Resource
    private IContractForecastService contractForecastService;

    @Resource
    private IContractForecastUserService contractForecastUserService;

    @Resource
    private IContractForecastUserProjectService contractForecastUserProjectService;

    @Resource
    private IContractForecastUserProjectProductService contractForecastUserProjectProductService;

    @Resource
    private TosEmployeeClient tosEmployeeClient;

    @Resource
    private RemoteProjectDirectlyClient remoteProjectDirectlyClient;

    @Resource
    private RemoteContractStatsService remoteContractStatsService;

    @Resource
    private FlowService flowService;

    @Resource
    private TosTreeClient tosTreeClient;

    @Resource
    private TosDepartmentClient tosDepartmentClient;

    @Resource
    private TfsTodoClient tfsTodoClient;

    @Resource
    private TfsTaskClient tfsTaskClient;

    @Resource
    private CommonService commonService;

    /**
     * 发送合同预测流程
     *
     * @param month
     * @return
     */
    @Override
    public Boolean sendContractForecastFlow(String month) {
        // 查询所有营销体系中存在员工的末级部门
        CrmDeptLeaderMemberQuery query = new CrmDeptLeaderMemberQuery();
        query.setDeptName(Arrays.asList("营销体系-区域","营销体系-行业"));
        JsonObject<Collection<CrmDeptLeaderMember>> collectionJsonObject = tosEmployeeClient.queryDeptLeaderMemberForCrm(query);
        if(collectionJsonObject.isSuccess() && CollectionUtils.isNotEmpty(collectionJsonObject.getObjEntity())){
            collectionJsonObject.getObjEntity().parallelStream().forEach( dept -> {
                // 构建流程发起参数
                ContractForecastFlowLaunchDTO launchDTO = buildContractForecastFlowLaunchDTO(month, dept);
                if(null != launchDTO){
                    launchDTO.setMatter(launchDTO.getMonth() +"合同预测");
                    try {
                        contractForecastProcessService.launch(launchDTO);
                    } catch (Exception e) {
                        log.error("合同预测流程发起异常", e);
                    }
                }
            });
        }
        return true;
    }

    /**
     * 查询合同预测流程详情
     *
     * @param processInstanceId
     * @return
     */
    @Override
    public ContractForecast contractForecastDetail(String processInstanceId) {
        ContractForecast one = contractForecastService.getOne(new LambdaQueryWrapper<ContractForecast>().eq(ContractForecast::getProcessInstanceId, processInstanceId));
        if(null != one){
            List<ContractForecastUser> allUsers = contractForecastUserService.list(new LambdaQueryWrapper<ContractForecastUser>().eq(ContractForecastUser::getForecastId, one.getId()));
            List<ContractForecastUserProject> allUsersProjects = new ArrayList<>();
            List<ContractForecastUserProjectProduct> allUsersProjectProducts =  new ArrayList<>();
            if(CollectionUtils.isNotEmpty(allUsers)){
                Set<String> forecastUserIds = allUsers.stream().map(ContractForecastUser::getId).collect(Collectors.toSet());
                allUsersProjects =  contractForecastUserProjectService.list(new LambdaQueryWrapper<ContractForecastUserProject>().in(ContractForecastUserProject::getForecastUserId, forecastUserIds));
                if(CollectionUtils.isNotEmpty(allUsersProjects)){
                    Set<String> forecastUserProjectIds = allUsersProjects.stream().map(ContractForecastUserProject::getId).collect(Collectors.toSet());
                    allUsersProjectProducts = contractForecastUserProjectProductService.list(new LambdaQueryWrapper<ContractForecastUserProjectProduct>().in(ContractForecastUserProjectProduct::getForecastUserProjectId, forecastUserProjectIds));
                }
                List<ContractForecastUserProject> finalAllUsersProjects = allUsersProjects;
                List<ContractForecastUserProjectProduct> finalAllUsersProjectProducts = allUsersProjectProducts;
                PersonContractStatsQuery query = new PersonContractStatsQuery();
                query.setQueryDate(one.getMonth());
                query.setPersonIds(allUsers.stream().map(ContractForecastUser::getProjectLeaderId).toList());
                JsonObject<List<ContractStatsResultVO>> personContractStats = remoteContractStatsService.getPersonContractStats(query);
                allUsers.forEach(user -> {
                    List<ContractForecastUserProject> userProjects = finalAllUsersProjects.stream().filter(userProject -> userProject.getForecastUserId().equals(user.getId())).toList();
                    userProjects.forEach(userProject -> {
                        List<ContractForecastUserProjectProduct> userProjectProducts = finalAllUsersProjectProducts.stream().filter(userProjectProduct -> userProjectProduct.getForecastUserProjectId().equals(userProject.getId())).toList();
                        userProject.setUserProjectProducts(userProjectProducts);
                        // 填充项目预测统计数据
                        userProject.setForecastNewContractAmount(ContractForecastUserProjectProduct.newContractAmount(userProjectProducts, one.getMonth()));
                    });
                    // 填充个人任务数据
                    if(personContractStats.isSuccess() && CollectionUtils.isNotEmpty(personContractStats.getObjEntity())){
                        personContractStats.getObjEntity().stream().filter(personContractStat -> personContractStat.getId().equals(user.getProjectLeaderId())).findFirst().ifPresentOrElse(personContractStat -> {
                            user.setTaskAmount(personContractStat.getTaskAmount());
                            user.setCurrentNewContractAmount(personContractStat.getNewContractAmountCurrentMonth());
                            user.setNewContractAmount(personContractStat.getNewContractAmountLastMonth());
                            user.setNewContractGrossMargin(personContractStat.getNewGrossProfitLastMonth());
                        }, () -> {
                            user.setTaskAmount(BigDecimal.ZERO);
                            user.setCurrentNewContractAmount(BigDecimal.ZERO);
                            user.setNewContractAmount(BigDecimal.ZERO);
                            user.setNewContractGrossMargin(BigDecimal.ZERO);
                        });
                    }
                    user.setUserProjects(userProjects);
                });
                one.setUsers(allUsers);
                // 填充部门任务数据
                DeptContractStatsQuery deptQuery = new DeptContractStatsQuery();
                deptQuery.setQueryDate(one.getMonth());
                deptQuery.setDeptIds(allUsers.stream().map(ContractForecastUser::getProjectLeaderId).toList());
                JsonObject<List<ContractStatsResultVO>> deptContractStats = remoteContractStatsService.getDeptContractStats(deptQuery);
                if(deptContractStats.isSuccess() && CollectionUtils.isNotEmpty(deptContractStats.getObjEntity())){
                    deptContractStats.getObjEntity().stream().filter(deptContractStat -> deptContractStat.getId().equals(one.getDeptId())).findFirst().ifPresentOrElse(deptContractStat -> {
                        one.setTaskAmount(deptContractStat.getTaskAmount());
                        one.setCurrentNewContractAmount(deptContractStat.getNewContractAmountCurrentMonth());
                        one.setNewContractAmount(deptContractStat.getNewContractAmountLastMonth());
                        one.setNewContractGrossMargin(deptContractStat.getNewGrossProfitLastMonth());
                    }, () -> {
                        one.setTaskAmount(BigDecimal.ZERO);
                        one.setCurrentNewContractAmount(BigDecimal.ZERO);
                        one.setNewContractAmount(BigDecimal.ZERO);
                        one.setNewContractGrossMargin(BigDecimal.ZERO);
                    });
                }
                // 填充部门预测统计数据
                fillDeptForecastStatistics(one);
            }
        }
        return one;
    }

    private void fillUserForecastStatistics(ContractForecastUser user, String month) {
        List<ContractForecastUserProject> userProjects = user.getUserProjects();
        if(CollectionUtils.isNotEmpty(userProjects)){
            user.setJanuary(userProjects.stream().map(ContractForecastUserProject::getJanuary).reduce(BigDecimal.ZERO,BigDecimal::add));
            user.setFebruary(userProjects.stream().map(ContractForecastUserProject::getFebruary).reduce(BigDecimal.ZERO,BigDecimal::add));
            user.setMarch(userProjects.stream().map(ContractForecastUserProject::getMarch).reduce(BigDecimal.ZERO,BigDecimal::add));
            user.setApril(userProjects.stream().map(ContractForecastUserProject::getApril).reduce(BigDecimal.ZERO,BigDecimal::add));
            user.setMay(userProjects.stream().map(ContractForecastUserProject::getMay).reduce(BigDecimal.ZERO,BigDecimal::add));
            user.setJune(userProjects.stream().map(ContractForecastUserProject::getJune).reduce(BigDecimal.ZERO,BigDecimal::add));
            user.setJuly(userProjects.stream().map(ContractForecastUserProject::getJuly).reduce(BigDecimal.ZERO,BigDecimal::add));
            user.setAugust(userProjects.stream().map(ContractForecastUserProject::getAugust).reduce(BigDecimal.ZERO,BigDecimal::add));
            user.setSeptember(userProjects.stream().map(ContractForecastUserProject::getSeptember).reduce(BigDecimal.ZERO,BigDecimal::add));
            user.setOctober(userProjects.stream().map(ContractForecastUserProject::getOctober).reduce(BigDecimal.ZERO,BigDecimal::add));
            user.setNovember(userProjects.stream().map(ContractForecastUserProject::getNovember).reduce(BigDecimal.ZERO,BigDecimal::add));
            user.setDecember(userProjects.stream().map(ContractForecastUserProject::getDecember).reduce(BigDecimal.ZERO,BigDecimal::add));
            user.setSignFifty(userProjects.stream().map(ContractForecastUserProject::getSignFifty).reduce(BigDecimal.ZERO,BigDecimal::add));
            user.setSignSixty(userProjects.stream().map(ContractForecastUserProject::getSignSixty).reduce(BigDecimal.ZERO,BigDecimal::add));
            user.setSignSeventy(userProjects.stream().map(ContractForecastUserProject::getSignSeventy).reduce(BigDecimal.ZERO,BigDecimal::add));
            user.setSignEighty(userProjects.stream().map(ContractForecastUserProject::getSignEighty).reduce(BigDecimal.ZERO,BigDecimal::add));
            user.setSignNinety(userProjects.stream().map(ContractForecastUserProject::getSignNinety).reduce(BigDecimal.ZERO,BigDecimal::add));
            user.setSignHundred(userProjects.stream().map(ContractForecastUserProject::getSignHundred).reduce(BigDecimal.ZERO,BigDecimal::add));
            List<ContractForecastUserProjectProduct> userProjectProducts = new ArrayList<>();
            userProjects.forEach(userProject -> {
                userProjectProducts.addAll(userProject.getUserProjectProducts());
            });
            if(CollectionUtils.isNotEmpty(userProjectProducts)){
                user.setCurrentMonthContractAmount(ContractForecastUserProjectProduct.currentMonthContractAmount(userProjectProducts, month));
                user.setCurrentMonthBidAmount(ContractForecastUserProjectProduct.currentMonthBidAmount(userProjectProducts, month));
                user.setSprintTarget(ContractForecastUserProjectProduct.sprintTarget(userProjectProducts, month));
                user.setLeastTarget(ContractForecastUserProjectProduct.leastTarget(userProjectProducts, month));
                user.setBidAmount(ContractForecastUserProjectProduct.bidAmount(userProjectProducts, month));
                user.setDeliveryAmount(ContractForecastUserProjectProduct.deliveryAmount(userProjectProducts, month));
                user.setComfirmAmount(ContractForecastUserProjectProduct.comfirmAmount(userProjectProducts, month));
                user.setSprintTargetYear(user.getNewContractAmount().add(user.getSprintTarget()));
                user.setLeastTargetYear(user.getNewContractAmount().add(user.getLeastTarget()));
                user.setSprintTargetYearRate(user.getSprintTargetYear().multiply(BigDecimal.valueOf(0.75)).subtract(user.getTaskAmount()));
                user.setLeastTargetYearRate(user.getLeastTargetYear().multiply(BigDecimal.valueOf(0.75)).subtract(user.getTaskAmount()));
            }else{
                user.setCurrentMonthContractAmount(BigDecimal.ZERO);
                user.setCurrentMonthBidAmount(BigDecimal.ZERO);
                user.setSprintTarget(BigDecimal.ZERO);
                user.setLeastTarget(BigDecimal.ZERO);
                user.setBidAmount(BigDecimal.ZERO);
                user.setDeliveryAmount(BigDecimal.ZERO);
                user.setComfirmAmount(BigDecimal.ZERO);
                user.setSprintTargetYear(BigDecimal.ZERO);
                user.setLeastTargetYear(BigDecimal.ZERO);
                user.setSprintTargetYearRate(BigDecimal.ZERO);
                user.setLeastTargetYearRate(BigDecimal.ZERO);
            }
        }else{
            user.setJanuary(BigDecimal.ZERO);
            user.setFebruary(BigDecimal.ZERO);
            user.setMarch(BigDecimal.ZERO);
            user.setApril(BigDecimal.ZERO);
            user.setMay(BigDecimal.ZERO);
            user.setJune(BigDecimal.ZERO);
            user.setJuly(BigDecimal.ZERO);
            user.setAugust(BigDecimal.ZERO);
            user.setSeptember(BigDecimal.ZERO);
            user.setOctober(BigDecimal.ZERO);
            user.setNovember(BigDecimal.ZERO);
            user.setDecember(BigDecimal.ZERO);
            user.setSignFifty(BigDecimal.ZERO);
            user.setSignSixty(BigDecimal.ZERO);
            user.setSignSeventy(BigDecimal.ZERO);
            user.setSignEighty(BigDecimal.ZERO);
            user.setSignNinety(BigDecimal.ZERO);
            user.setSignHundred(BigDecimal.ZERO);
            user.setCurrentMonthContractAmount(BigDecimal.ZERO);
            user.setCurrentMonthBidAmount(BigDecimal.ZERO);
            user.setSprintTarget(BigDecimal.ZERO);
            user.setLeastTarget(BigDecimal.ZERO);
            user.setBidAmount(BigDecimal.ZERO);
            user.setDeliveryAmount(BigDecimal.ZERO);
            user.setComfirmAmount(BigDecimal.ZERO);
            user.setSprintTargetYear(BigDecimal.ZERO);
            user.setLeastTargetYear(BigDecimal.ZERO);
            user.setSprintTargetYearRate(BigDecimal.ZERO);
            user.setLeastTargetYearRate(BigDecimal.ZERO);
        }
    }

    private void fillDeptForecastStatistics(ContractForecast one) {
        List<ContractForecastUser> users = one.getUsers();
        if(CollectionUtils.isNotEmpty(users)){
            one.setCurrentMonthContractAmount(users.stream().map(ContractForecastUser::getCurrentMonthContractAmount).reduce(BigDecimal.ZERO,BigDecimal::add));
            one.setCurrentMonthBidAmount(users.stream().map(ContractForecastUser::getCurrentMonthBidAmount).reduce(BigDecimal.ZERO,BigDecimal::add));
            one.setSprintTarget(users.stream().map(ContractForecastUser::getSprintTarget).reduce(BigDecimal.ZERO,BigDecimal::add));
            one.setLeastTarget(users.stream().map(ContractForecastUser::getLeastTarget).reduce(BigDecimal.ZERO,BigDecimal::add));
            one.setBidAmount(users.stream().map(ContractForecastUser::getBidAmount).reduce(BigDecimal.ZERO,BigDecimal::add));
            one.setDeliveryAmount(users.stream().map(ContractForecastUser::getDeliveryAmount).reduce(BigDecimal.ZERO,BigDecimal::add));
            one.setComfirmAmount(users.stream().map(ContractForecastUser::getComfirmAmount).reduce(BigDecimal.ZERO,BigDecimal::add));
            one.setSprintTargetYear(one.getNewContractAmount().add(one.getSprintTarget()));
            one.setLeastTargetYear(one.getNewContractAmount().add(one.getLeastTarget()));
            one.setSprintTargetYearRate(one.getSprintTargetYear().multiply(BigDecimal.valueOf(0.75)).subtract(one.getTaskAmount()));
            one.setLeastTargetYearRate(one.getLeastTargetYear().multiply(BigDecimal.valueOf(0.75)).subtract(one.getTaskAmount()));
        }else{
            one.setCurrentMonthContractAmount(BigDecimal.ZERO);
            one.setCurrentMonthBidAmount(BigDecimal.ZERO);
            one.setSprintTarget(BigDecimal.ZERO);
            one.setLeastTarget(BigDecimal.ZERO);
            one.setBidAmount(BigDecimal.ZERO);
            one.setDeliveryAmount(BigDecimal.ZERO);
            one.setComfirmAmount(BigDecimal.ZERO);
            one.setSprintTargetYear(BigDecimal.ZERO);
            one.setLeastTargetYear(BigDecimal.ZERO);
            one.setSprintTargetYearRate(BigDecimal.ZERO);
            one.setLeastTargetYearRate(BigDecimal.ZERO);
        }
    }

    /**
     * 获取合同预测图表数据
     *
     * @param processInstanceId
     * @return
     */
    @Override
    public ContractForecastStatistics contractForecastChartData(String processInstanceId) {
        ContractForecastStatistics chartData = ContractForecastStatistics.builder()
                .january(BigDecimal.ZERO)
                .february(BigDecimal.ZERO)
                .march(BigDecimal.ZERO)
                .april(BigDecimal.ZERO)
                .may(BigDecimal.ZERO)
                .june(BigDecimal.ZERO)
                .july(BigDecimal.ZERO)
                .august(BigDecimal.ZERO)
                .september(BigDecimal.ZERO)
                .october(BigDecimal.ZERO)
                .november(BigDecimal.ZERO)
                .december(BigDecimal.ZERO)
                .signFifty(BigDecimal.ZERO)
                .signSixty(BigDecimal.ZERO)
                .signSeventy(BigDecimal.ZERO)
                .signEighty(BigDecimal.ZERO)
                .signNinety(BigDecimal.ZERO)
                .signHundred(BigDecimal.ZERO)
                .build();
        ContractForecast one = contractForecastService.getOne(new LambdaQueryWrapper<ContractForecast>().eq(ContractForecast::getProcessInstanceId, processInstanceId));
        if(null != one){
            List<ContractForecastUser> allUsers = contractForecastUserService.list(new LambdaQueryWrapper<ContractForecastUser>().eq(ContractForecastUser::getForecastId, one.getId()));
            if(CollectionUtils.isNotEmpty(allUsers)){
                chartData = calculateChartData(allUsers);
            }
        }
        return chartData;
    }

    private ContractForecastStatistics calculateChartData(List<ContractForecastUser> allUsers) {
        return ContractForecastStatistics.builder()
                .january(allUsers.stream().map(ContractForecastUser::getJanuary).reduce(BigDecimal.ZERO,BigDecimal::add))
                .february(allUsers.stream().map(ContractForecastUser::getFebruary).reduce(BigDecimal.ZERO,BigDecimal::add))
                .march(allUsers.stream().map(ContractForecastUser::getMarch).reduce(BigDecimal.ZERO,BigDecimal::add))
                .april(allUsers.stream().map(ContractForecastUser::getApril).reduce(BigDecimal.ZERO,BigDecimal::add))
                .may(allUsers.stream().map(ContractForecastUser::getMay).reduce(BigDecimal.ZERO,BigDecimal::add))
                .june(allUsers.stream().map(ContractForecastUser::getJune).reduce(BigDecimal.ZERO,BigDecimal::add))
                .july(allUsers.stream().map(ContractForecastUser::getJuly).reduce(BigDecimal.ZERO,BigDecimal::add))
                .august(allUsers.stream().map(ContractForecastUser::getAugust).reduce(BigDecimal.ZERO,BigDecimal::add))
                .september(allUsers.stream().map(ContractForecastUser::getSeptember).reduce(BigDecimal.ZERO,BigDecimal::add))
                .october(allUsers.stream().map(ContractForecastUser::getOctober).reduce(BigDecimal.ZERO,BigDecimal::add))
                .november(allUsers.stream().map(ContractForecastUser::getNovember).reduce(BigDecimal.ZERO,BigDecimal::add))
                .december(allUsers.stream().map(ContractForecastUser::getDecember).reduce(BigDecimal.ZERO,BigDecimal::add))
                .signFifty(allUsers.stream().map(ContractForecastUser::getSignFifty).reduce(BigDecimal.ZERO,BigDecimal::add))
                .signSixty(allUsers.stream().map(ContractForecastUser::getSignSixty).reduce(BigDecimal.ZERO,BigDecimal::add))
                .signSeventy(allUsers.stream().map(ContractForecastUser::getSignSeventy).reduce(BigDecimal.ZERO,BigDecimal::add))
                .signEighty(allUsers.stream().map(ContractForecastUser::getSignEighty).reduce(BigDecimal.ZERO,BigDecimal::add))
                .signNinety(allUsers.stream().map(ContractForecastUser::getSignNinety).reduce(BigDecimal.ZERO,BigDecimal::add))
                .signHundred(allUsers.stream().map(ContractForecastUser::getSignHundred).reduce(BigDecimal.ZERO,BigDecimal::add))
                .build();
    }

    /**
     * 修改项目产品的预测数据
     *
     * @param productForecastData
     * @param processInstanceId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateProductForecastData(List<ContractForecastUserProjectProduct> productForecastData, String processInstanceId) {
        boolean b = contractForecastUserProjectProductService.updateBatchById(productForecastData);
        ContractForecast one = contractForecastService.getOne(new LambdaQueryWrapper<ContractForecast>().eq(ContractForecast::getProcessInstanceId, processInstanceId));
        if(b && null != one){
            // 重新计算统计数据并保存
            Map<String, List<ContractForecastUserProjectProduct>> updateProjectsMap = productForecastData.stream().collect(Collectors.groupingBy(ContractForecastUserProjectProduct::getForecastUserProjectId));
            Set<String> forecastUserProjectIds = updateProjectsMap.keySet();
            List<ContractForecastUserProject> updateProjects = contractForecastUserProjectService.list(new LambdaQueryWrapper<ContractForecastUserProject>().in(ContractForecastUserProject::getId, forecastUserProjectIds));
            Map<String, List<ContractForecastUserProject>> updateUsersMap = new HashMap<>();
            List<ContractForecastUser> updateUsers = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(updateProjects)){
                updateUsersMap = updateProjects.stream().collect(Collectors.groupingBy(ContractForecastUserProject::getForecastUserId));
                Set<String> forecastUserIds = updateUsersMap.keySet();
                updateUsers = contractForecastUserService.list(new LambdaQueryWrapper<ContractForecastUser>().in(ContractForecastUser::getId, forecastUserIds));
            }
            if(CollectionUtils.isNotEmpty(updateUsers)){
                List<ContractForecastUser> finalUpdateUsers = updateUsers;
                updateUsersMap.forEach((k, v)->{
                    PersonContractStatsQuery query = new PersonContractStatsQuery();
                    query.setQueryDate(one.getMonth());
                    query.setPersonIds(finalUpdateUsers.stream().map(ContractForecastUser::getProjectLeaderId).toList());
                    JsonObject<List<ContractStatsResultVO>> personContractStats = remoteContractStatsService.getPersonContractStats(query);
                    finalUpdateUsers.stream().filter(u->u.getId().equals(k)).findFirst().ifPresent(u->{
                        List<ContractForecastUserProjectProduct> temp = new ArrayList<>();
                        v.forEach(p->{
                            List<ContractForecastUserProjectProduct> contractForecastUserProjectProducts = updateProjectsMap.get(p.getId());
                            // 填充项目统计数据
                            p.setUserProjectProducts(contractForecastUserProjectProducts);
                            // 填充项目预测统计数据
                            p.setForecastNewContractAmount(ContractForecastUserProjectProduct.newContractAmount(contractForecastUserProjectProducts, one.getMonth()));
                        });
                        contractForecastUserProjectService.updateBatchById(v);
                        // 填充个人任务数据
                        if(personContractStats.isSuccess() && CollectionUtils.isNotEmpty(personContractStats.getObjEntity())){
                            personContractStats.getObjEntity().stream().filter(personContractStat -> personContractStat.getId().equals(u.getProjectLeaderId())).findFirst().ifPresentOrElse(personContractStat -> {
                                u.setTaskAmount(personContractStat.getTaskAmount());
                                u.setCurrentNewContractAmount(personContractStat.getNewContractAmountCurrentMonth());
                                u.setNewContractAmount(personContractStat.getNewContractAmountLastMonth());
                                u.setNewContractGrossMargin(personContractStat.getNewGrossProfitLastMonth());
                            }, () -> {
                                u.setTaskAmount(BigDecimal.ZERO);
                                u.setCurrentNewContractAmount(BigDecimal.ZERO);
                                u.setNewContractAmount(BigDecimal.ZERO);
                                u.setNewContractGrossMargin(BigDecimal.ZERO);
                            });
                        }
                        // 填充用户统计数据
                        u.setUserProjects(v);
                        // 填充用户预测统计数据
                        fillUserForecastStatistics(u, one.getMonth());
                    });
                });
                contractForecastUserService.updateBatchById(finalUpdateUsers);
            }
        }else{
            throw new CrmException("保存项目产品的预测数据失败");
        }
        return b;
    }

    private ContractForecastFlowLaunchDTO buildContractForecastFlowLaunchDTO(String month, CrmDeptLeaderMember deptData) {
        ContractForecast one = contractForecastService.getOne(new LambdaQueryWrapper<ContractForecast>().eq(ContractForecast::getMonth, month).eq(ContractForecast::getDeptId, deptData.getDept().getUuid()));
        if(null != one){
            // 该部门已经发起过该月份的合同预测流程
            return null;
        }
        boolean isFull = false;
        // 构建合同预测流程数据
        ContractForecastFlowLaunchDTO launchDTO = new ContractForecastFlowLaunchDTO();
        launchDTO.setMonth(month);
        launchDTO.setDeptId(deptData.getDept().getUuid());
        launchDTO.setDeptName(deptData.getDept().getName());
        launchDTO.setDeptLeaderId(deptData.getLeaderList().stream().map(BriefInfoVO::getUuid).toList());// 部门负责人可能会有多个
        launchDTO.setDeptLeaderName(deptData.getLeaderList().stream().map(BriefInfoVO::getName).toList());
        List<ContractForecastUserVO> users = new ArrayList<>();
        Set<String> memberIds = deptData.getMemberList().stream().map(BriefInfoVO::getUuid).collect(Collectors.toSet());
        for (String memberId : memberIds) {
            ContractForecastUserVO userVO = ContractForecastUserVO.builder()
                    .projectLeaderId(memberId)
                    .projectLeaderName(NameUtils.getName(memberId))
                    .currentMonthContractAmount(BigDecimal.ZERO)
                    .currentMonthBidAmount(BigDecimal.ZERO)
                    .sprintTarget(BigDecimal.ZERO)
                    .leastTarget(BigDecimal.ZERO)
                    .bidAmount(BigDecimal.ZERO)
                    .deliveryAmount(BigDecimal.ZERO)
                    .comfirmAmount(BigDecimal.ZERO)
                    .sprintTargetYear(BigDecimal.ZERO)
                    .leastTargetYear(BigDecimal.ZERO)
                    .sprintTargetYearRate(BigDecimal.ZERO)
                    .leastTargetYearRate(BigDecimal.ZERO)
                    .january(BigDecimal.ZERO)
                    .february(BigDecimal.ZERO)
                    .march(BigDecimal.ZERO)
                    .april(BigDecimal.ZERO)
                    .may(BigDecimal.ZERO)
                    .june(BigDecimal.ZERO)
                    .july(BigDecimal.ZERO)
                    .august(BigDecimal.ZERO)
                    .september(BigDecimal.ZERO)
                    .october(BigDecimal.ZERO)
                    .november(BigDecimal.ZERO)
                    .december(BigDecimal.ZERO)
                    .signFifty(BigDecimal.ZERO)
                    .signSixty(BigDecimal.ZERO)
                    .signSeventy(BigDecimal.ZERO)
                    .signEighty(BigDecimal.ZERO)
                    .signNinety(BigDecimal.ZERO)
                    .signHundred(BigDecimal.ZERO)
                    .build();
            List<ContractForecastUserProjectVO> userProjects = new ArrayList<>();
            JsonObject<List<CrmProjectDirectlyVo>> directlyProjectAndUnfinishedProduct = remoteProjectDirectlyClient.getDirectlyProjectAndUnfinishedProduct(memberId);
            if(directlyProjectAndUnfinishedProduct.isSuccess() && CollectionUtils.isNotEmpty(directlyProjectAndUnfinishedProduct.getObjEntity())){
                for(CrmProjectDirectlyVo projectDirectly : directlyProjectAndUnfinishedProduct.getObjEntity()){
                    ContractForecastUserProjectVO userProjectVO = ContractForecastUserProjectVO.builder()
                            .projectId(projectDirectly.getId())
                            .projectNo(projectDirectly.getProjectNo())
                            .projectName(projectDirectly.getProjectName())
                            .finalCustomerId(projectDirectly.getFinalCustomerId())
                            .finalCustomerName(projectDirectly.getFinalCustomer())
                            //.specify(false)
                            .forecastNewContractAmount(BigDecimal.ZERO)
                            .january(BigDecimal.ZERO)
                            .february(BigDecimal.ZERO)
                            .march(BigDecimal.ZERO)
                            .april(BigDecimal.ZERO)
                            .may(BigDecimal.ZERO)
                            .june(BigDecimal.ZERO)
                            .july(BigDecimal.ZERO)
                            .august(BigDecimal.ZERO)
                            .september(BigDecimal.ZERO)
                            .october(BigDecimal.ZERO)
                            .november(BigDecimal.ZERO)
                            .december(BigDecimal.ZERO)
                            .signFifty(BigDecimal.ZERO)
                            .signSixty(BigDecimal.ZERO)
                            .signSeventy(BigDecimal.ZERO)
                            .signEighty(BigDecimal.ZERO)
                            .signNinety(BigDecimal.ZERO)
                            .signHundred(BigDecimal.ZERO)
                            .build();
                    List<ContractForecastUserProjectProductVO> userProjectProducts = new ArrayList<>();
                    List<CrmProjectProductOwnVO> crmProjectProductOwn = projectDirectly.getCrmProjectProductOwn();
                    Set<String> hasContractIds = flowService.queryRecordHasContract(projectDirectly.getId(), projectDirectly.getCrmProjectProductOwn().stream().map(CrmProjectProductOwnVO::getId).collect(Collectors.toSet()));
                    // 过滤已经发起了合同的产品行
                    for(CrmProjectProductOwnVO productOwn : crmProjectProductOwn.stream().filter(productOwn -> !hasContractIds.contains(productOwn.getId())).collect(Collectors.toSet())){
                        ContractForecastUserProjectProductVO userProjectProductVO = ContractForecastUserProjectProductVO.builder()
                                .productRecordId(productOwn.getId())
                                .stuffCode(productOwn.getStuffCode())
                                .productCategory(productOwn.getProductCategory())
                                .pnCode(productOwn.getPnCode())
                                .productNum(productOwn.getProductNum())
                                .dealTotalPrice(productOwn.getDealTotalPrice())
                                .build();
                        userProjectProducts.add(userProjectProductVO);
                    }
                    if(CollectionUtils.isNotEmpty(userProjectProducts)){
                        userProjectVO.setUserProjectProducts(userProjectProducts);
                        userProjects.add(userProjectVO);
                    }
                }
            }
            if(CollectionUtils.isNotEmpty(userProjects)){
                userVO.setUserProjects(userProjects);
                users.add(userVO);
            }
        }
        if(CollectionUtils.isNotEmpty(users)){
            launchDTO.setUsers(users);
            // 填充审核人
            Collection<String> accountIds = Optional.ofNullable(AccountAccquireUtils.getAccountIdByPersonId(new ArrayList<>(deptData.getLeaderList().stream().map(BriefInfoVO::getUuid).collect(Collectors.toSet()))))
                    .map(Map::values).orElse(Collections.emptyList());
            launchDTO.setAssigneeList(new HashSet<>(accountIds));
            launchDTO.setCreatorPersonId("b051b2e8a22aaa814cd0ad384bffa318");
            return launchDTO;
        }else{
            return null;
        }
    }

    /**
     * 获取合同预测未办结排名
     *
     * @param type
     * @return
     */
    @Override
    public List<HomeRankContractForecastVO> unfinishedContractForecastRank(Integer type) {
        List<HomeRankContractForecastVO> list = new ArrayList<>();
        String deptId = "";
        if(type == 1){
            deptId = "202976"; //营销体系-行业
        }else if(type == 2){
            deptId = "206841"; //营销体系-区域
        }
        if(StringUtils.isNotBlank(deptId)){
            JsonObject<TreeVO> treeVOJsonObject = tosTreeClient.queryPartDepartmentTree(deptId);
            if(treeVOJsonObject.isSuccess() && treeVOJsonObject.getObjEntity() != null){
                List<TreeVO> children = treeVOJsonObject.getObjEntity().getChildren();
                if(CollectionUtils.isNotEmpty(children)){
                    List<String> deptIds = getDeptIds(children);
                    List<ContractForecast> unfinishedFlows = contractForecastService.list(new LambdaQueryWrapper<ContractForecast>().in(ContractForecast::getDeptId, deptIds).eq(ContractForecast::getProcessState, 1));
                    children.forEach(m -> {
                        JsonObject<TosDepartmentVO> byId = tosDepartmentClient.findById(m.getKey());
                        HomeRankContractForecastVO rankContractForecastVO = new HomeRankContractForecastVO();
                        rankContractForecastVO.setDeptId(m.getKey());
                        rankContractForecastVO.setDeptName(m.getLabel());
                        if(byId.isSuccess() && byId.getObjEntity() != null) {
                            rankContractForecastVO.setLeader(byId.getObjEntity().getLeaders().stream().map(n -> NameUtils.getName(n.getUuid())).toList());
                        }
                        List<String> currentChildrenDeptIds = getDeptIds(m.getChildren());
                        List<HomeRankContractForecastVO.UnfinishedDetailVO> unfinished = unfinishedFlows.stream().filter(flow -> currentChildrenDeptIds.contains(flow.getDeptId())).map(flow -> {
                            HomeRankContractForecastVO.UnfinishedDetailVO unfinishedDetailVO = new HomeRankContractForecastVO.UnfinishedDetailVO();
                            unfinishedDetailVO.setDeptName(flow.getDeptName());
                            unfinishedDetailVO.setLeader(flow.getDeptLeaderName());
                            return unfinishedDetailVO;
                        }).toList();
                        rankContractForecastVO.setUnfinished(unfinished);
                        if(CollectionUtils.isNotEmpty(unfinished)){
                            list.add(rankContractForecastVO);
                        }
                    });
                }
            }
        }
        if(CollectionUtils.isNotEmpty(list)){
            list.sort((o1, o2) -> o2.getUnfinished().size() - o1.getUnfinished().size());
        }
        return list;
    }

    private List<String> getDeptIds(List<TreeVO> trees){
        List<String> deptIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(trees)) {
            for (TreeVO tree : trees) {
                // 添加当前节点的ID
                if (tree != null && StringUtils.isNotBlank(tree.getKey())) {
                    deptIds.add(tree.getKey());
                }
                // 递归处理子节点
                if (tree != null && CollectionUtils.isNotEmpty(tree.getChildren())) {
                    deptIds.addAll(getDeptIds(tree.getChildren()));
                }
            }
        }
        return deptIds;
    }

    /**
     * 自动完成合同预测流程01
     *
     * @param month
     * @return
     */
    @Override
    public Boolean autoFinishContractForecastFlow01(List<String> month) {
        List<ContractForecast> unfinishedFlows = contractForecastService.list(new LambdaQueryWrapper<ContractForecast>().in(ContractForecast::getMonth, month).eq(ContractForecast::getProcessState, 1));
        // 查询合同预测01节点的待办任务
        String currentNodeId = "sid-CFDDAA98-DC3C-42F4-BAC0-891DF595BEB5";
        JsonObject<List<TfsToTaskVo>> todoTaskList = tfsTodoClient.queryTaskByProcessDefinitionKeyAndNodeIds(ProcessDefinitionKeyEnum.NEW_DEAL_PREDICTION_KEY.getValue(), Collections.singletonList(currentNodeId));
        if (todoTaskList.isSuccess() && CollectionUtils.isNotEmpty(todoTaskList.getObjEntity())){
            List<TfsToTaskVo> objEntity = todoTaskList.getObjEntity();
            objEntity.forEach(tfsUserTaskVo -> {
                unfinishedFlows.stream().filter(flow -> tfsUserTaskVo.getProcessInstanceId().equals(flow.getProcessInstanceId())).findFirst().ifPresent(flow -> {
                    TfsTaskVo tfsTaskVo = new TfsTaskVo();
                    tfsTaskVo.setType(FormTypeEnum.NEW_DEAL_PREDICTION.getType());
                    tfsTaskVo.setComment("流程办理超时，系统自动办结");
                    tfsTaskVo.setTaskId(tfsUserTaskVo.getTaskId());
                    tfsTaskVo.setProcessInstanceId(tfsUserTaskVo.getProcessInstanceId());
                    tfsTaskVo.setCurrentNodeId(currentNodeId);
                    tfsTaskVo.setUserId("b051b2e8a22aaa814cd0ad384bffa318");
                    Map<String,Object> map = new HashMap<>();
                    List<String> deptLeaderId = flow.getDeptLeaderId();
                    Set<String> assigneeList = new HashSet<>();
                    deptLeaderId.forEach(m -> {
                        List<FlowPerson> flowPeople = commonService.queryFlowSuperiors(m);
                        if(CollectionUtils.isNotEmpty(flowPeople)){
                            assigneeList.addAll(flowPeople.stream().map(FlowPerson::getAccountId).toList());
                        }
                    });
                    if(CollectionUtils.isNotEmpty(assigneeList)){
                        map.put("assigneeList", CollectionUtil.toTreeSet(assigneeList,Comparator.naturalOrder()));
                        tfsTaskVo.setVariables(map);
                        tfsTaskClient.completeTask(tfsTaskVo);
                    }
                });
            });
        }
        return true;
    }

    /**
     * 自动完成合同预测流程02
     *
     * @param month
     * @return
     */
    @Override
    public Boolean autoFinishContractForecastFlow02(List<String> month) {
        // 查询合同预测02节点的待办任务
        String currentNodeId = "sid-F2D6F4BA-5B2A-4653-8E79-DB38F2B97A85";
        JsonObject<List<TfsToTaskVo>> todoTaskList = tfsTodoClient.queryTaskByProcessDefinitionKeyAndNodeIds(ProcessDefinitionKeyEnum.NEW_DEAL_PREDICTION_KEY.getValue(), Collections.singletonList(currentNodeId));
        if (todoTaskList.isSuccess() && CollectionUtils.isNotEmpty(todoTaskList.getObjEntity())){
            List<TfsToTaskVo> objEntity = todoTaskList.getObjEntity();
            objEntity.forEach(tfsUserTaskVo -> {
                TfsTaskVo tfsTaskVo = new TfsTaskVo();
                tfsTaskVo.setType(FormTypeEnum.NEW_DEAL_PREDICTION.getType());
                tfsTaskVo.setComment("流程办理超时，系统自动办结");
                tfsTaskVo.setTaskId(tfsUserTaskVo.getTaskId());
                tfsTaskVo.setProcessInstanceId(tfsUserTaskVo.getProcessInstanceId());
                tfsTaskVo.setCurrentNodeId(currentNodeId);
                tfsTaskVo.setUserId("b051b2e8a22aaa814cd0ad384bffa318");
                tfsTaskClient.stopTask(tfsTaskVo);
            });
        }
        return true;
    }
}
