package com.topsec.crm.flow.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.topsec.crm.agent.api.RemoteAgentRebateService;
import com.topsec.crm.agent.api.RemoteCrmAgentAuthenticationService;
import com.topsec.crm.agent.api.entity.CrmAgentAuthenticationVO;
import com.topsec.crm.agent.api.entity.crmagentrebate.CrmAgentRebatePoolVO;
import com.topsec.crm.contract.api.RemoteCrmAgentPaymentVerificationService;
import com.topsec.crm.framework.common.bean.AgentPrePaymentVerificationVO;
import com.topsec.crm.contract.api.entity.crmagentpayment.agentpaymentverification.PrepaymentSummaryVO;
import com.topsec.crm.flow.api.dto.performancereport.*;
import com.topsec.crm.flow.api.dto.performancereport.constant.PerformanceReportConstant;
import com.topsec.crm.flow.core.entity.*;
import com.topsec.crm.flow.core.mapper.*;
import com.topsec.crm.flow.core.mapstruct.PerformanceReportConvertor;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.framework.common.bean.CrmProjectProductSnVO;
import com.topsec.crm.framework.common.bean.FlowPerson;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.product.api.RemoteProductSeparationRelService;
import com.topsec.crm.product.api.RemoteProductService;
import com.topsec.crm.product.api.RemoteProductWholesalePriceService;
import com.topsec.crm.product.api.entity.CrmProductSeparationRelVo;
import com.topsec.crm.product.api.entity.CrmProductVo;
import com.topsec.crm.product.api.entity.CrmProductWholesalePriceVO;
import com.topsec.crm.project.api.RemoteProjectDynastyService;
import com.topsec.crm.project.api.client.RemoteProjectAgentClient;
import com.topsec.crm.project.api.client.RemoteProjectDirectlyClient;
import com.topsec.crm.project.api.client.RemoteProjectProductSnClient;
import com.topsec.crm.project.api.entity.CrmProjectAgentVo;
import com.topsec.crm.project.api.entity.CrmProjectDirectlyVo;
import com.topsec.crm.project.api.entity.CrmProjectDynastyVo;
import com.topsec.tbsapi.client.TbsCrmClient;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tbscommon.vo.PersonVO;
import com.topsec.tfs.api.client.TfsNodeClient;
import com.topsec.tfs.api.client.TfsTaskClient;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.vo.node.ApproveNode;
import com.topsec.vo.task.TfsTaskVo;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.Period;
import java.util.*;
import java.util.stream.Collectors;

import static com.topsec.jwt.UserInfoHolder.getCurrentAccountId;

@Service
@RequiredArgsConstructor
public class PerformanceReportServiceImpl extends ServiceImpl<PerformanceReportMapper,PerformanceReport> implements PerformanceReportService {

    private final PerformanceReportProductOwnService performanceReportProductOwnService;
    private final PerformanceReportMapper performanceReportMapper;
    private final PerformanceReportProductOwnMapper performanceReportProductOwnMapper;
    private final PerformanceReportProductOwnSnMapper performanceReportProductOwnSnMapper;
    private final PerformanceReportContractDeliveryDetailMapper performanceReportContractDeliveryDetailMapper;
    private final PerformanceReportProductSeparationSnMapper performanceReportProductSeparationSnMapper;
    private final PerformanceReportProductSeparationMapper performanceReportProductSeparationMapper;
    private final PerformanceReportProductSeparationService performanceReportProductSeparationService;

    private final PerformanceReportProductSeparationSnService performanceReportProductSeparationSnService;
    private final PerformanceReportContractDeliveryService performanceReportContractDeliveryService;
    private final PerformanceReportPaymentTermsService performanceReportPaymentTermsService;
    private final PerformanceReportPaymentInfoService performanceReportPaymentInfoService;
    private final PerformanceReportDocService performanceReportDocService;
    private final TfsTaskClient tfsTaskclient;
    private final TbsCrmClient tbsCrmClient;
    private final RemoteProductWholesalePriceService remoteProductWholesalePriceService;
    private final RemoteProductSeparationRelService remoteProductSeparationRelService;
    private final RemoteProductService remoteProductService;
    private final AgentAuthPaymentDisburseMapper agentAuthPaymentDisburseMapper;
    private final AgentAuthenticationMapper agentAuthenticationMapper;
    private final RemoteCrmAgentAuthenticationService remoteCrmAgentAuthenticationService;
    private final ContractReviewMainMapper contractReviewMainMapper;
    private final RemoteProjectAgentClient remoteProjectAgentClient;
    private final RemoteProjectDirectlyClient remoteProjectDirectlyClient;
    private final RemoteProjectDynastyService remoteProjectDynastyService;
    private final RemoteAgentRebateService remoteAgentRebateService;
    private final RemoteProjectProductSnClient remoteProjectProductSnClient;
    private final TfsNodeClient tfsNodeClient;
    private final PerformanceReportReturnExchangeMapper performanceReportReturnExchangeMapper;
    private final PerformanceReportCrossAgentSubmitService performanceReportCrossAgentSubmitService;
    private final RemoteCrmAgentPaymentVerificationService remoteCrmAgentPaymentVerificationService;


    @Override
    public List<PerformanceReportProductOwnDTO> getPerformanceReportProductByProcessInstanceId(String processInstanceId) {

        PerformanceReport performanceReport = queryByProcessInstanceId(processInstanceId);
        // 自有产品
        List<PerformanceReportProductOwn> reportProductOwnList = performanceReportProductOwnService.
                list(new QueryWrapper<PerformanceReportProductOwn>()
                        .eq("process_instance_id", processInstanceId)
                        .eq("del_flag", 0));
        List<PerformanceReportProductOwnDTO> performanceReportProductOwnDTOS = HyperBeanUtils.copyListPropertiesByJackson(reportProductOwnList, PerformanceReportProductOwnDTO.class);
        // 累计OS差异金额
        BigDecimal totalOsDifference = performanceReportMapper.selectTotalOsDifference(performanceReport.getSupplierId());
        for (PerformanceReportProductOwnDTO performanceReportProductOwnDTO : performanceReportProductOwnDTOS){
            if (performanceReport.getSupplierType() == 1){
                // 软硬分离产品
                List<PerformanceReportProductSeparation> reportProductSeparations = performanceReportProductSeparationService.list(new QueryWrapper<PerformanceReportProductSeparation>()
                        .eq("performance_report_product_id", performanceReportProductOwnDTO.getId())
                        .eq("del_flag", 0));
                List<PerformanceReportProductSeparationDTO> performanceReportProductSeparationDTOS = HyperBeanUtils.copyListPropertiesByJackson(reportProductSeparations, PerformanceReportProductSeparationDTO.class);

                int matchSnNum = 0;
                for (PerformanceReportProductSeparationDTO performanceReportProductSeparationDTO : performanceReportProductSeparationDTOS){
                    // 软硬分离产品序列号
                    List<PerformanceReportProductSeparationSn> list = performanceReportProductSeparationSnService.list(new QueryWrapper<PerformanceReportProductSeparationSn>()
                            .eq("performance_report_product_separation_id", performanceReportProductSeparationDTO.getId())
                            .eq("del_flag", 0));
                    List<PerformanceReportProductSeparationSnDTO> performanceReportProductSeparationSnDTOS = HyperBeanUtils.copyListPropertiesByJackson(list, PerformanceReportProductSeparationSnDTO.class);
                    matchSnNum = matchSnNum + performanceReportProductSeparationSnDTOS.size();
                    performanceReportProductSeparationDTO.setPerformanceReportProductSeparationSnDTOS(performanceReportProductSeparationSnDTOS);
                    // 累计OS差异个数
                    if (performanceReportProductSeparationDTO.getProductClassification() == 2){
                        BigDecimal bigDecimal = performanceReportProductSeparationMapper.selectTotalOsDifferenceNum(totalOsDifference, performanceReportProductSeparationDTO.getId());
                        BigDecimal divide = bigDecimal.setScale(2, RoundingMode.HALF_UP);
                        performanceReportProductSeparationDTO.setTotalOsDifferenceNum(divide.floatValue());
                        performanceReportProductSeparationDTO.setTotalOsDifferencePrice(totalOsDifference);
                    }
                }
                performanceReportProductOwnDTO.setMatchSnNum(matchSnNum);
                performanceReportProductOwnDTO.setPerformanceReportProductSeparationDTOS(performanceReportProductSeparationDTOS);
            }else if (performanceReport.getSupplierType() == 2){
                // 自有产品序列号
                List<PerformanceReportProductOwnSn> performanceReportProductOwnSns = performanceReportProductOwnSnMapper.selectList(new QueryWrapper<PerformanceReportProductOwnSn>()
                        .eq("performance_report_product_own_id", performanceReportProductOwnDTO.getId())
                        .eq("del_flag", 0));
                List<PerformanceReportProductOwnSnDTO> performanceReportProductOwnSnDTOS = HyperBeanUtils.copyListPropertiesByJackson(performanceReportProductOwnSns, PerformanceReportProductOwnSnDTO.class);
                performanceReportProductOwnDTO.setMatchSnNum(performanceReportProductOwnSnDTOS.size());
                performanceReportProductOwnDTO.setPerformanceReportProductOwnSnDTOS(performanceReportProductOwnSnDTOS);
            }
        }

        return performanceReportProductOwnDTOS;
    }



    @Override
    public PerformanceReportDetailInfo getPerformanceReportDetailsByProcessInstanceId(String processInstanceId) {

        PerformanceReportDetailInfo performanceReportDetailInfo= new PerformanceReportDetailInfo();

        performanceReportDetailInfo.setBaseInfo(PerformanceReportConvertor.INSTANCE.toReportVO(queryByProcessInstanceId(processInstanceId)));


        List<PerformanceReportProductOwnDTO> productList = queryProductInfo(processInstanceId);

        performanceReportDetailInfo.setProductList(productList);

        performanceReportDetailInfo.setContractDeliveryList(getContractDeliveryVOList(processInstanceId));
        performanceReportDetailInfo.setPaymentTermsList(PerformanceReportConvertor.INSTANCE.toPaymentTermsVOList(performanceReportPaymentTermsService
                .list(new QueryWrapper<PerformanceReportPaymentTerms>().eq("process_instance_id", processInstanceId))));

        List<PerformanceReportPaymentInfo> performanceReportPaymentInfos = performanceReportPaymentInfoService.selectPerformanceReportPaymentInfoByProcessInstanceId(processInstanceId);
        performanceReportDetailInfo.setPaymentInfoList(PerformanceReportConvertor.INSTANCE.toPaymentInfoVOList(performanceReportPaymentInfos));


        List<PerformanceReportDoc> performanceReportDocList = performanceReportDocService.selectPerformanceReportDocDTO(processInstanceId);
        performanceReportDetailInfo.setAttachmentList(PerformanceReportConvertor.INSTANCE.toDocVOList(performanceReportDocList));

        return performanceReportDetailInfo;
    }


    private List<PerformanceReportProductOwnDTO> queryProductInfo(String processInstanceId) {
        PerformanceReport performanceReport = queryByProcessInstanceId(processInstanceId);
        // 累计OS差异金额
        BigDecimal totalOsDifference = performanceReportMapper.selectTotalOsDifference(performanceReport.getSupplierId());
        // 自有产品
        List<PerformanceReportProductOwn> reportProductOwnList = performanceReportProductOwnService.
                list(new QueryWrapper<PerformanceReportProductOwn>()
                        .eq("process_instance_id", processInstanceId)
                        .eq("del_flag", 0));
        List<PerformanceReportProductOwnDTO> performanceReportProductOwnDTOS = HyperBeanUtils
                .copyListPropertiesByJackson(reportProductOwnList, PerformanceReportProductOwnDTO.class);

        for (PerformanceReportProductOwnDTO performanceReportProductOwnDTO : performanceReportProductOwnDTOS) {
            // 软硬分离产品
            List<PerformanceReportProductSeparation> reportProductSeparations = performanceReportProductSeparationService.list(new QueryWrapper<PerformanceReportProductSeparation>()
                    .eq("performance_report_product_id", performanceReportProductOwnDTO.getId())
                    .eq("del_flag", 0));
            List<PerformanceReportProductSeparationDTO> performanceReportProductSeparationDTOS = HyperBeanUtils.copyListPropertiesByJackson(reportProductSeparations, PerformanceReportProductSeparationDTO.class);

            for (PerformanceReportProductSeparationDTO performanceReportProductSeparationDTO : performanceReportProductSeparationDTOS) {
                // 软硬分离产品序列号
                List<PerformanceReportProductSeparationSn> list = performanceReportProductSeparationSnService.list(new QueryWrapper<PerformanceReportProductSeparationSn>()
                        .eq("performance_report_product_separation_id", performanceReportProductSeparationDTO.getId())
                        .eq("del_flag", 0));
                List<PerformanceReportProductSeparationSnDTO> performanceReportProductSeparationSnDTOS = HyperBeanUtils.copyListPropertiesByJackson(list, PerformanceReportProductSeparationSnDTO.class);
                performanceReportProductSeparationDTO.setPerformanceReportProductSeparationSnDTOS(performanceReportProductSeparationSnDTOS);
                // 累计OS差异个数
                if (performanceReportProductSeparationDTO.getProductClassification() == 2){
                    BigDecimal bigDecimal = performanceReportProductSeparationMapper.selectTotalOsDifferenceNum(totalOsDifference, performanceReportProductSeparationDTO.getId());
                    BigDecimal divide = bigDecimal.setScale(2, BigDecimal.ROUND_HALF_UP);
                    performanceReportProductSeparationDTO.setTotalOsDifferenceNum(divide.floatValue());
                    performanceReportProductSeparationDTO.setTotalOsDifferencePrice(totalOsDifference);
                }
            }
            performanceReportProductOwnDTO.setPerformanceReportProductSeparationDTOS(performanceReportProductSeparationDTOS);

            // 自有产品序列号
            List<PerformanceReportProductOwnSn> performanceReportProductOwnSns = performanceReportProductOwnSnMapper.selectList(new QueryWrapper<PerformanceReportProductOwnSn>()
                    .eq("performance_report_product_own_id", performanceReportProductOwnDTO.getId())
                    .eq("del_flag", 0));
            List<PerformanceReportProductOwnSnDTO> performanceReportProductOwnSnDTOS = HyperBeanUtils.copyListPropertiesByJackson(performanceReportProductOwnSns, PerformanceReportProductOwnSnDTO.class);
            performanceReportProductOwnDTO.setPerformanceReportProductOwnSnDTOS(performanceReportProductOwnSnDTOS);
        }

        return performanceReportProductOwnDTOS;
    }



    private PerformanceReport queryByProcessInstanceId(String processInstanceId) {
        return getOne(new QueryWrapper<PerformanceReport>().eq("process_instance_id", processInstanceId));
    }


    @Override
    public List<FlowPerson> selectDynastyApprover(String processInstanceId) {
        PerformanceReport performanceReport = queryByProcessInstanceId(processInstanceId);


        return selectAgentApprover("11");
    }

    @Override
    public List<FlowPerson> selectAgentApprover(String supplierId) {
        JsonObject<List<PersonVO>> listJsonObject = tbsCrmClient.listPersonAccountByAgentId(supplierId);
        List<FlowPerson> flowPersonList = new ArrayList<>();
        listJsonObject.getObjEntity().stream().forEach(personVO -> {
            FlowPerson flowPerson = HyperBeanUtils.copyProperties(personVO, FlowPerson::new);
            flowPerson.setPersonId(personVO.getUuid());
            flowPerson.setPersonName(personVO.getName());
            flowPersonList.add(flowPerson);
        });
        return flowPersonList;
    }

    @Override
    public boolean updatePerformanceReportProductAcceptOrder(PerformanceReportProcessVO performanceReportVO) {
        PerformanceReport performanceReport1 = queryByProcessInstanceId(performanceReportVO.getProcessInstanceId());
        PerformanceReport performanceReport = new PerformanceReport();
        performanceReport.setId(performanceReport1.getId());
        if (performanceReportVO.getAcceptOrder() == 1){
            performanceReport.setAcceptOrder(performanceReportVO.getAcceptOrder());
            return updateById(performanceReport);
        }else if (performanceReportVO.getAcceptOrder() == 0){
            // 拒单原因
            List<PerformanceReportRefuseOrderReasonsDTO> refuseOrderReasons = performanceReport1.getRefuseOrderReasons();
            if (refuseOrderReasons == null){
                refuseOrderReasons = new ArrayList<PerformanceReportRefuseOrderReasonsDTO>();
            }
            PerformanceReportRefuseOrderReasonsDTO performanceReportRefuseOrderReasonsDTO = new PerformanceReportRefuseOrderReasonsDTO();
            performanceReportRefuseOrderReasonsDTO.setSupplierName(performanceReport1.getSupplierName());
            performanceReportRefuseOrderReasonsDTO.setRefuseOrderType(performanceReportVO.getRefuseOrderType());
            performanceReportRefuseOrderReasonsDTO.setRefuseOrderReason(performanceReportVO.getRefuseOrderReason());
            refuseOrderReasons.add(performanceReportRefuseOrderReasonsDTO);

            TfsTaskVo tfsTaskVo = HyperBeanUtils.copyProperties(performanceReportVO, TfsTaskVo::new);
            tfsTaskVo.setUserId(getCurrentAccountId());
            // 省代拒单
            if (performanceReportVO.getCurrentNodeId() != null && Objects.equals(performanceReportVO.getCurrentNodeId(), PerformanceReportConstant.CURRENT_NODE_01)){
                // 供货商修改为国代
                performanceReport.setRefuseOrderReasons(refuseOrderReasons);
                performanceReport.setSupplierId("11");
                performanceReport.setSupplierName("伟仕佳杰（重庆）科技有限公司");
                performanceReport.setSupplierType(1);
                updateById(performanceReport);
                tfsTaskclient.taskJump(tfsTaskVo);
                return true;
            }else if (performanceReportVO.getCurrentNodeId() != null && Objects.equals(performanceReportVO.getCurrentNodeId(), PerformanceReportConstant.CURRENT_NODE_02)){
                performanceReport.setRefuseOrderReasons(refuseOrderReasons);
                performanceReport.setAcceptOrder(0);
                updateById(performanceReport);
                tfsTaskclient.rejectTask(tfsTaskVo);
                return true;
            }
        }
        return false;

    }

    // 根据自有产品id查询 业绩上报供货商名称
    @Override
    public PerformanceReport selectPerformanceReportSupplierName(String performanceReportProductId){
        PerformanceReportProductOwn performanceReportProductOwn = performanceReportProductOwnService.getById(performanceReportProductId);
        return getOne(new QueryWrapper<PerformanceReport>().eq("process_instance_id", performanceReportProductOwn.getProcessInstanceId()).eq("del_flag", 0));
    }

    @Override
    public boolean savePerformanceReportProductInfo(PerformanceReportVO performanceReportVO) {
        PerformanceReport performanceReport = getById(performanceReportVO.getId());
        if (performanceReportVO.getRebateReplaceOs() == null){
            throw new CrmException("请选择是否使用返点代替OS消耗");
        }
        Integer supplierType = performanceReport.getSupplierType();
        if (supplierType == 1 && (performanceReport.getRebateReplaceOs() == null || performanceReport.getRebateReplaceOs() != performanceReportVO.getRebateReplaceOs())){
            // 判断国代剩余可用返点金额是否大于当前订单中OS出货对应金额
            if (performanceReportVO.getRebateReplaceOs() == 1){
                BigDecimal bigDecimal = performanceReportProductSeparationMapper.selectOsPrice(performanceReport.getProcessInstanceId());
                CrmAgentRebatePoolVO objEntity = remoteAgentRebateService.selectOneRebate(performanceReport.getSupplierId(), null).getObjEntity();
                if (objEntity == null || objEntity.getAvailableRebate().compareTo(bigDecimal) < 0){
                    throw new CrmException("可用返点数量不足，当前订单无法使用返点代替OS消耗");
                }
            }
            performanceReport.setRebateReplaceOs(performanceReportVO.getRebateReplaceOs());
            updateById(performanceReport);
            List<PerformanceReportProductOwn> performanceReportProductOwnList = performanceReportProductOwnService.list(new QueryWrapper<PerformanceReportProductOwn>().eq("process_instance_id", performanceReport.getProcessInstanceId()).eq("del_flag", 0));
            for (PerformanceReportProductOwn performanceReportProductOwn : performanceReportProductOwnList){
                // 使用返点代替OS消耗
                if (performanceReportVO.getRebateReplaceOs() == 1){
                    performanceReportProductSeparationService.updateOsConsume(performanceReportProductOwn.getId());
                }else if (performanceReportVO.getRebateReplaceOs() == 0){
                    List<PerformanceReportProductSeparation> performanceReportProductSeparationList = performanceReportProductSeparationMapper.selectList(new QueryWrapper<PerformanceReportProductSeparation>()
                            .eq("performance_report_product_id", performanceReportProductOwn.getId())
                            .eq("product_classification", 2)
                            .eq("del_flag", 0));
                    for (PerformanceReportProductSeparation performanceReportProductSeparation : performanceReportProductSeparationList){
                        BigDecimal consumePrice = performanceReportProductOwnMapper.selectPerformanceReportProductSeparationConsumePrice(performanceReportProductOwn.getId(), performanceReportProductSeparation.getHardwareSeparationId());
                        if (consumePrice == null){
                            throw new CrmException("无价格信息");
                        }
                        float osSystemNum = consumePrice.multiply(BigDecimal.valueOf(performanceReportProductSeparation.getProductNum())).divide(performanceReportProductSeparation.getSellInPrice(), 1, RoundingMode.HALF_UP).floatValue();
                        float osRealityNum = consumePrice.multiply(BigDecimal.valueOf(performanceReportProductSeparation.getProductNum())).divide(performanceReportProductSeparation.getSellInPrice(), 0,RoundingMode.HALF_UP).floatValue();

                        performanceReportProductSeparation.setConsumePrice(consumePrice);
                        performanceReportProductSeparation.setOsSystemNum(osSystemNum);
                        performanceReportProductSeparation.setOsRealityNum(osRealityNum);
                        BigDecimal num = BigDecimal.valueOf(osRealityNum - osSystemNum);
                        performanceReportProductSeparation.setOsDifferencePrice(performanceReportProductSeparation.getSellInPrice().multiply(num));
                        performanceReportProductSeparationService.updateById(performanceReportProductSeparation);
                    }

                }
            }
        }
        return true;
    }

    @Override
    public Map<String, Object> selectCompletionConditions(PerformanceReportProductQuery performanceReportProductQuery) {
        Map<String, Object> map = new HashMap<>();
        String approvalNode = performanceReportProductQuery.getApprovalNode();
        String processInstanceId = performanceReportProductQuery.getProcessInstanceId();
        PerformanceReport performanceReport = queryByProcessInstanceId(processInstanceId);

        List<PerformanceReportProductOwn> performanceReportProductOwnList = performanceReportProductOwnService.list(new QueryWrapper<PerformanceReportProductOwn>().eq("process_instance_id", processInstanceId).eq("del_flag", 0));
        if (Objects.equals(approvalNode, "01") || Objects.equals(approvalNode, "05")){
            if (Objects.equals(approvalNode, "05")) {
                for (PerformanceReportProductOwn performanceReportProductOwn : performanceReportProductOwnList) {
                    List<PerformanceReportProductOwnSn> performanceReportProductOwnSnList = performanceReportProductOwnSnMapper.selectList(new QueryWrapper<PerformanceReportProductOwnSn>().eq("performance_report_product_own_id", performanceReportProductOwn.getId()).eq("del_flag", 0));
                    if (performanceReportProductOwn.getProductNum() != performanceReportProductOwnSnList.size()) {
                        map.put("id", 2);
                        map.put("message", performanceReportProductOwn.getStuffCode() + "的【产品数量】未关联相等的序列号");
                        return map;
                    }
                }
            }
            if (Objects.equals(approvalNode, "01")){
                List<PerformanceReportDoc> performanceReportDocList = performanceReportDocService.selectPerformanceReportDocDTO(processInstanceId).stream().filter(performanceReportDoc -> Objects.equals(performanceReportDoc.getDocSource(), "01") && Objects.equals(performanceReportDoc.getType(), "总代合同")).toList();
                if (performanceReportDocList.isEmpty()){
                    map.put("id", 2);
                    map.put("message", "请上传总代合同 ");
                    return map;
                }
            }
        }else if (Objects.equals(approvalNode, "03")) {
            List<PerformanceReportDoc> performanceReportDocs = performanceReportDocService.selectPerformanceReportDocDTO(processInstanceId);
            if (performanceReportDocs.isEmpty()) {
                map.put("id", 2);
                map.put("message", "请上传附件");
                return map;
            }
            List<PerformanceReportDoc> collect = performanceReportDocs.stream().filter(performanceReportDoc -> Objects.equals(performanceReportDoc.getDocSource(), "03") && Objects.equals(performanceReportDoc.getType(), "总经销商与二级代理合同")).toList();
            if (collect.isEmpty()) {
                JsonObject<CrmProjectDirectlyVo> projectInfo = remoteProjectDirectlyClient.getProjectInfo(performanceReport.getProjectId());
                if (projectInfo.isSuccess() && projectInfo.getObjEntity() != null) {
                    Integer zdType = projectInfo.getObjEntity().getZdType();
                    if (zdType == 1){
                        map.put("id", 2);
                        map.put("message", "请上传合同");
                        return map;
                    }
                }
                map.put("id", 2);
                map.put("message", "请上传总经销商与二级代理合同");
                return map;
            }
            List<PerformanceReportDoc> collect1 = performanceReportDocs.stream().filter(performanceReportDoc -> Objects.equals(performanceReportDoc.getDocSource(), "03") && Objects.equals(performanceReportDoc.getType(), "付款凭证")).toList();
            if (collect1.isEmpty()) {
                map.put("id", 2);
                map.put("message", "请上传付款凭证");
                return map;
            }
            List<PerformanceReportPaymentInfo> performanceReportPaymentInfos = performanceReportPaymentInfoService.selectPerformanceReportPaymentInfoByProcessInstanceId(processInstanceId);
            if (performanceReportPaymentInfos.isEmpty()) {
                map.put("id", 2);
                map.put("message", "请填写付款信息");
                return map;
            }
            List<PerformanceReportPaymentTerms> performanceReportPaymentTerms = performanceReportPaymentTermsService.selectPerformanceReportPaymentTerms(processInstanceId);
            if (performanceReportPaymentTerms.isEmpty()) {
                map.put("id", 2);
                map.put("message", "请填写付款条款");
                return map;
            }
        }else if (Objects.equals(approvalNode, "04")){
            List<PerformanceReportDoc> performanceReportDocs = performanceReportDocService.selectPerformanceReportDocDTO(processInstanceId);
            List<PerformanceReportDoc> list = performanceReportDocs.stream().filter(performanceReportDoc -> performanceReportDoc.getOriginalContract() != null && performanceReportDoc.getOriginalContract() == 1 && Objects.equals(performanceReportDoc.getType(), "总经销商与二级代理合同")).toList();
            if (list.isEmpty()) {
                map.put("id", 2);
                map.put("message", "未标记合同原件");
                return map;
            }
        }else if (Objects.equals(approvalNode, "02") || Objects.equals(approvalNode, "06")){
            for (PerformanceReportProductOwn performanceReportProductOwn : performanceReportProductOwnList){
                int hardwareTotalNum = performanceReportProductSeparationService.selectSeparationHardwareTotalNum(performanceReportProductOwn.getId());
                if (performanceReportProductOwn.getProductNum() != hardwareTotalNum){
                    map.put("id", 2);
                    map.put("message",performanceReportProductOwn.getStuffCode() + "的【产品数量】未匹配相等的硬件");
                    return map;
                }

                if (Objects.equals(approvalNode, "06")){
                    List<PerformanceReportProductSeparation> performanceReportProductSeparationList = performanceReportProductSeparationService.list(new QueryWrapper<PerformanceReportProductSeparation>()
                            .eq("performance_report_product_id", performanceReportProductOwn.getId())
                            .eq("product_classification", 1)
                            .eq("del_flag", 0));
                    for (PerformanceReportProductSeparation performanceReportProductSeparation : performanceReportProductSeparationList){
                        // 代下单的数量必须大于等于 借试用的数量
                        if ((performanceReportProductSeparation.getSpecialItem() !=null && performanceReportProductSeparation.getSpecialItem() == 1) || (performanceReportProductSeparation.getBorrowForForward() != null && performanceReportProductSeparation.getBorrowForForward() == 1)){
                            if (performanceReportProductOwn.getScarceGoodsOrderNum() < performanceReportProductSeparation.getProductNum()){
                                map.put("id", 2);
                                map.put("message",performanceReportProductSeparation.getStuffCode() + "因存在借试用/借转销/专项备货产品，请发起【一键代下单】从天融信采购");
                                return map;
                            }

                            List<ContractReviewMain> contractReviewMains = contractReviewMainMapper.selectList(new LambdaQueryWrapper<ContractReviewMain>()
                                    .eq(ContractReviewMain::getPerformanceReportId, performanceReport.getId())
                                    .eq(ContractReviewMain::getDelFlag, 0));
                            if (contractReviewMains == null || contractReviewMains.isEmpty()){
                                map.put("id", 2);
                                map.put("message", "因存在借试用/借转销/专项备货产品，请发起【一键代下单】从天融信采购");
                                return map;
                            }else{
                                List<ContractReviewMain> collect = contractReviewMains.stream().filter(contractReviewMain -> contractReviewMain.getProcessState() != 2).collect(Collectors.toList());
                                if (!collect.isEmpty()){
                                    map.put("id", 2);
                                    map.put("message", "存在代下单未办结！");
                                    return map;
                                }
                            }
                        }
                        List<PerformanceReportProductSeparationSn> performanceReportProductSeparationSnList = performanceReportProductSeparationSnService.list(new QueryWrapper<PerformanceReportProductSeparationSn>().eq("performance_report_product_separation_id", performanceReportProductSeparation.getId()).eq("del_flag", 0));
                        if (performanceReportProductSeparation.getProductNum() != performanceReportProductSeparationSnList.size()){
                            map.put("id", 2);
                            map.put("message",performanceReportProductSeparation.getStuffCode() + "的【产品数量】未关联相等的序列号");
                            return map;
                        }
                    }
                }
            }
            if (Objects.equals(approvalNode, "02")){
                List<PerformanceReportDoc> performanceReportDocList = performanceReportDocService.selectPerformanceReportDocDTO(processInstanceId).stream().filter(performanceReportDoc -> Objects.equals(performanceReportDoc.getDocSource(), "02") && Objects.equals(performanceReportDoc.getType(), "总代合同")).collect(Collectors.toList());
                if (performanceReportDocList.isEmpty()){
                    map.put("id", 2);
                    map.put("message", "请上传总代合同 ");
                    return map;
                }
            }
            if (Objects.equals(approvalNode, "06")){
                // 判断国代剩余可用返点金额是否大于当前订单中OS出货对应金额
                if (performanceReport.getRebateReplaceOs() == 1){
                    BigDecimal bigDecimal = performanceReportMapper.selectOSConsumePriceTotal(performanceReport.getProcessInstanceId());
                    CrmAgentRebatePoolVO objEntity = remoteAgentRebateService.selectOneRebate(performanceReport.getSupplierId(), null).getObjEntity();
                    if (objEntity == null || objEntity.getAvailableRebate().compareTo(bigDecimal) < 0){
                        throw new CrmException("可用返点数量不足，当前订单无法使用返点代替OS消耗");
                    }
                }

                List<CrmProductVo> crmProductVoList = remoteProductService.batchGetInfo(performanceReportProductOwnList.stream().map(PerformanceReportProductOwn::getProductId).collect(Collectors.toList())).getObjEntity();
                List<PerformanceReportContractDelivery> performanceReportContractDeliveryList = performanceReportContractDeliveryService.list(new QueryWrapper<PerformanceReportContractDelivery>().eq("process_instance_id", processInstanceId));
                for (PerformanceReportContractDelivery performanceReportContractDelivery : performanceReportContractDeliveryList){
                    // 发货产品
                    List<ContractDeliveryProductVO> deliveryProducts = performanceReportContractDelivery.getDeliveryProducts();
                    for (ContractDeliveryProductVO contractDeliveryProductVO : deliveryProducts){
                        PerformanceReportProductOwn performanceReportProductOwn = performanceReportProductOwnService.getOne(new QueryWrapper<PerformanceReportProductOwn>().eq("project_record_id", contractDeliveryProductVO.getProjectProductRecordId()).eq("del_flag", 0));
                        CrmProductVo crmProductVo1 = crmProductVoList.stream().filter(crmProductVo -> Objects.equals(crmProductVo.getId(), performanceReportProductOwn.getProductId()) && (crmProductVo.getForm() == 1 || crmProductVo.getForm() == 3)).findFirst().orElse(null);
                        if (crmProductVo1 != null){
                            List<ContractDeliveryProductSnVO> deliveryProductSns = new ArrayList<>();
                            // 发货序列号
                            List<PerformanceReportContractDeliveryDetail> performanceReportContractDeliveryDetail = performanceReportContractDeliveryDetailMapper.selectList(new QueryWrapper<PerformanceReportContractDeliveryDetail>().eq("contract_delivery_id", performanceReportContractDelivery.getId()));
                            if (performanceReportContractDeliveryDetail == null || performanceReportContractDeliveryDetail.isEmpty()){
                                map.put("id", 2);
                                map.put("message", "请填写货运信息！");
                                return map;
                            }else {
                                performanceReportContractDeliveryDetail.forEach(performanceReportContractDeliveryDetail1 -> {
                                    deliveryProductSns.addAll(performanceReportContractDeliveryDetail1.getDeliveryProductSns());
                                });
                                List<ContractDeliveryProductSnVO> contractDeliveryProductSnVOList = deliveryProductSns.stream().filter(contractDeliveryProductSnVO -> Objects.equals(contractDeliveryProductSnVO.getPerformanceReportProductId(), performanceReportProductOwn.getId())).collect(Collectors.toList());
                                if (contractDeliveryProductVO.getNum() != ListUtils.emptyIfNull(contractDeliveryProductSnVOList).size()){
                                    map.put("id", 2);
                                    map.put("message", "产品" + performanceReportProductOwn.getStuffCode() + "的货运信息发货序列号数量不正确;");
                                    return map;
                                }
                            }
                        }
                    }
                }
            }
        }
        if (Objects.equals(approvalNode, "05") || Objects.equals(approvalNode, "06")){
            List<PerformanceReportPaymentInfo> performanceReportPaymentInfos = performanceReportPaymentInfoService.selectPerformanceReportPaymentInfoByProcessInstanceId(processInstanceId).stream().filter(performanceReportPaymentInfo -> performanceReportPaymentInfo.getAccountDate() == null || performanceReportPaymentInfo.getAccountNumber() == null).collect(Collectors.toList());
            if (!performanceReportPaymentInfos.isEmpty()){
                map.put("id", 2);
                map.put("message", "请填写到账时间和进账单号");
                return map;
            }
        }
        map.put("id", 1);
        map.put("message", "正常办理完毕");
        return map;
    }

    @Override
    public Map<String, Object> backConditions(PerformanceReportProductQuery performanceReportProductQuery) {
        Map<String, Object> map = new HashMap<>();
        String approvalNode = performanceReportProductQuery.getApprovalNode();
        String targetApprovalNode = performanceReportProductQuery.getTargetApprovalNode();
        String processInstanceId = performanceReportProductQuery.getProcessInstanceId();
        PerformanceReport performanceReport = queryByProcessInstanceId(processInstanceId);

        if (performanceReport.getSupplierType() ==1 && Objects.equals(targetApprovalNode, "00")) {
            // 存在代下单不可退回
            List<ContractReviewMain> contractReviewMains = contractReviewMainMapper.selectList(new LambdaQueryWrapper<ContractReviewMain>()
                    .eq(ContractReviewMain::getPerformanceReportId, performanceReport.getId())
                    .eq(ContractReviewMain::getDelFlag, 0));
            if (contractReviewMains != null && !contractReviewMains.isEmpty()) {
                map.put("id", 2);
                map.put("message", "存在代下单不可退回！");
                return map;
            }
            // 存在ZD项目不可退回
            JsonObject<List<CrmProjectDynastyVo>> info = remoteProjectDynastyService.getProjectDynastyByPerformanceReportId(performanceReport.getId());
            if (info.isSuccess() && info.getObjEntity() != null) {
                if (!info.getObjEntity().isEmpty()){
                    map.put("id", 2);
                    map.put("message", "存在ZD项目不可退回！");
                    return map;
                }
            }

        }else if ((Objects.equals(approvalNode, "05") || Objects.equals(approvalNode, "06") && performanceReport.getEffectiveTime() != null)){
            // 判断是否跨月
            Period between = Period.between(performanceReport.getEffectiveTime().toLocalDate(), LocalDate.now());
            int i = between.getYears() * 12 + between.getMonths();
            if (i >= 1){
                map.put("id", 2);
                map.put("message", "跨月不可退回！");
                return map;
            }
        }
        map.put("id", 1);
        map.put("message", "正常退回");
        return map;
    }

    @Override
    public Map<String, Object> deleteConditions(PerformanceReportProductQuery performanceReportProductQuery) {
        Map<String, Object> map = new HashMap<>();
        String processInstanceId = performanceReportProductQuery.getProcessInstanceId();
        PerformanceReport performanceReport = queryByProcessInstanceId(processInstanceId);
        // 存在代下单不可删除
        List<ContractReviewMain> contractReviewMains = contractReviewMainMapper.selectList(new LambdaQueryWrapper<ContractReviewMain>()
                .eq(ContractReviewMain::getPerformanceReportId, performanceReport.getId())
                .eq(ContractReviewMain::getDelFlag, 0));
        if (contractReviewMains != null && !contractReviewMains.isEmpty()) {
            map.put("id", 2);
            map.put("message", "存在代下单不可删除！");
            return map;
        }
        // 存在ZD项目不可删除
        JsonObject<List<CrmProjectDynastyVo>> info = remoteProjectDynastyService.getProjectDynastyByPerformanceReportId(performanceReport.getId());
        if (info.isSuccess() && info.getObjEntity() != null) {
            if (!info.getObjEntity().isEmpty()){
                map.put("id", 2);
                map.put("message", "存在ZD项目不可删除！");
                return map;
            }
        }
        // 关联序列号不可删除
        int count = 0;
        if (performanceReport.getSupplierType() == 1){
            count = performanceReportProductSeparationSnMapper.selectProductSeparationSnStatistics(processInstanceId);
        }else {
            count = performanceReportProductOwnSnMapper.selectProductSnStatistics(processInstanceId);
        }
        if (count > 0){
            map.put("id", 2);
            map.put("message", "已关联序列号不可删除！");
            return map;
        }

        map.put("id", 1);
        map.put("message", "正常退回");
        return map;
    }

    @Override
    public Boolean snInPerformanceReport(String sn) {
        Map<String, Set<String>> snRecordMap = Optional.ofNullable(remoteProjectProductSnClient.getProductSnBatchBySns(List.of(sn)))
                .map(JsonObject::getObjEntity)
                .orElse(Collections.emptyList())
                .stream()
                .filter(item->{
                    return com.topsec.crm.framework.common.util.StringUtils.isNotEmpty(item.getPsn())&& com.topsec.crm.framework.common.util.StringUtils.isNotEmpty(item.getRecordId());
                })
                .collect(Collectors.groupingBy(CrmProjectProductSnVO::getPsn, Collectors.collectingAndThen(Collectors.toList(), crmProjectProductSnVOS -> {
                    return crmProjectProductSnVOS.stream().map(CrmProjectProductSnVO::getRecordId).collect(Collectors.toSet());
                })));
        if (snRecordMap.containsKey( sn)){
            Set<String> recordIds = snRecordMap.get(sn);
            return performanceReportMapper.existsByRecordId(recordIds);
        }else {
            return false;
        }
    }

    @Override
    public Boolean updateSigningContractNumber(PerformanceReportVO performanceReportVO) {
        update(new UpdateWrapper<PerformanceReport>().eq("process_instance_id", performanceReportVO.getProcessInstanceId()).set("signing_contract_number", performanceReportVO.getSigningContractNumber()));
        return true;
    }

    @Override
    public List<PerformanceReportContractDeliveryDTO> getReturnContractDeliveryVOList(String returnProcessInstanceId){
        List<PerformanceReportContractDeliveryDTO> contractDeliveryVOList = PerformanceReportConvertor.INSTANCE.toContractDeliveryVOList(performanceReportContractDeliveryService
                .list(new LambdaQueryWrapper<PerformanceReportContractDelivery>().eq(PerformanceReportContractDelivery::getReturnExchangeProcessInstanceId, returnProcessInstanceId)));
        return postProcessContractDeliveryInfo(contractDeliveryVOList);
    }

    @Override
    public List<PerformanceReportContractDeliveryDTO> getContractDeliveryVOList(String processInstanceId){
        List<PerformanceReportContractDeliveryDTO> contractDeliveryVOList = PerformanceReportConvertor.INSTANCE.toContractDeliveryVOList(performanceReportContractDeliveryService
                .list(new QueryWrapper<PerformanceReportContractDelivery>().eq("process_instance_id", processInstanceId)));
        return postProcessContractDeliveryInfo(contractDeliveryVOList);
    }

    private List<PerformanceReportContractDeliveryDTO> postProcessContractDeliveryInfo(List<PerformanceReportContractDeliveryDTO> contractDeliveryVOList) {
        List<String> projectProductRecordIds = contractDeliveryVOList.stream().flatMap(
                vo -> {
                    return Optional.ofNullable(vo.getDeliveryProducts())
                            .orElse(Collections.emptyList())
                            .stream()
                            .map(ContractDeliveryProductVO::getProjectProductRecordId);
                }
        ).distinct().toList();
        Map<String, String> recordIdProductIdMap ;
        if(CollectionUtils.isEmpty(projectProductRecordIds)){
            recordIdProductIdMap= Collections.emptyMap();
        }else {
            recordIdProductIdMap = performanceReportProductOwnMapper.selectList(new QueryWrapper<PerformanceReportProductOwn>().in("project_record_id", projectProductRecordIds))
                    .stream()
                    .collect(Collectors.toMap(PerformanceReportProductOwn::getProjectRecordId, PerformanceReportProductOwn::getProductId, (s, s2) -> {
                        return s;
                    }));
        }
        for (PerformanceReportContractDeliveryDTO dto : contractDeliveryVOList) {
            List<ContractDeliveryProductVO> deliveryProducts = ListUtils.emptyIfNull(dto.getDeliveryProducts());
            for (ContractDeliveryProductVO deliveryProduct : deliveryProducts) {
                String projectProductRecordId = deliveryProduct.getProjectProductRecordId();
                deliveryProduct.setProductId(recordIdProductIdMap.get(projectProductRecordId));
            }

            String id = dto.getId();
            List<PerformanceReportContractDeliveryDetail> performanceReportProductOwnSnList = performanceReportContractDeliveryDetailMapper
                    .selectList(new QueryWrapper<PerformanceReportContractDeliveryDetail>().eq("contract_delivery_id", id));

            List<PerformanceReportProductOwnSnDTO> productOwnSnVOList = performanceReportProductOwnSnList.stream().flatMap(item -> {
                List<ContractDeliveryProductSnVO> deliveryProductSns = item.getDeliveryProductSns();
                return Optional.ofNullable(deliveryProductSns).orElse(Collections.emptyList())
                        .stream().map(product -> {
                            PerformanceReportProductOwnSnDTO performanceReportProductOwnSnDTO = new PerformanceReportProductOwnSnDTO();
                            performanceReportProductOwnSnDTO.setPerformanceReportProductOwnId(product.getPerformanceReportProductId());

                            performanceReportProductOwnSnDTO.setPsn(product.getPsn());
                            performanceReportProductOwnSnDTO.setStorageTime(null);
                            performanceReportProductOwnSnDTO.setDeliveryId(item.getContractDeliveryId());
                            return performanceReportProductOwnSnDTO;
                        });
            }).toList();

            Map<String, List<PerformanceReportProductOwnSnDTO>> collect = ListUtils
                    .emptyIfNull(productOwnSnVOList).stream()
                    .filter(item->{
                        return !StringUtils.isEmpty(item.getDeliveryId());
                    })
                    .collect(Collectors.groupingBy(PerformanceReportProductOwnSnDTO::getDeliveryId));
            dto.setDeliveryInfoMap(collect);
        }
        return contractDeliveryVOList;
    }

    @Override
    public List<PerformanceReportContractDeliveryDTO> querySelectedProductDeliveryInfo(String processInstanceId,Set<String> recordIdSet){
        List<PerformanceReportContractDeliveryDTO> contractDeliveryVOList = getContractDeliveryVOList(processInstanceId);
        if (CollectionUtils.isNotEmpty(recordIdSet)){
            contractDeliveryVOList = contractDeliveryVOList
                    .stream()
                    .filter(contractDeliveryVO -> {
                        List<ContractDeliveryProductVO> deliveryProducts = contractDeliveryVO.getDeliveryProducts();
                        if (CollectionUtils.isNotEmpty(deliveryProducts)){
                            return deliveryProducts.stream().anyMatch(deliveryProduct -> recordIdSet.contains(deliveryProduct.getProjectProductRecordId()));
                        }else {
                            return false;
                        }
                    })
                    .collect(Collectors.toList());
        }
        return contractDeliveryVOList;
    }

    @Override
    public Page<PerformanceReport> pagePerformanceReport(PerformanceReportQuery query) {
        Page<PerformanceReport> page = new Page<>(query.getPageNum(), query.getPageSize());


        Page<PerformanceReport> paged = page(page,
                new QueryWrapper<PerformanceReport>()
                        .eq(query.getProcessState()!=null,"process_state", query.getProcessState())
                        .eq(query.getChannelType()!=null,"channel_type", query.getChannelType())
                        .eq(StringUtils.isNotBlank(query.getProjectId()),"project_id", query.getProjectId())

        );

        return paged;
    }

    @Override
    public ScarceGoodsOrderDTO getCustomerInfoByprocessInstanceId(String processInstanceId) {
        ScarceGoodsOrderDTO scarceGoodsOrderDTO = new ScarceGoodsOrderDTO();
        PerformanceReport performanceReport = queryByProcessInstanceId(processInstanceId);
        ScarceGoodsOrderBaseInfoDTO baseInfo = new ScarceGoodsOrderBaseInfoDTO();
        baseInfo.setFinalCustomId(performanceReport.getCustomerId());
        baseInfo.setFinalCustomName(performanceReport.getCustomerName());
        scarceGoodsOrderDTO.setBaseInfo(baseInfo);
        return scarceGoodsOrderDTO;
    }
    //缺货下单产品明细
    @Override
    public TableDataInfo productInfoByProcessInstanceId(String processInstanceId) {
        // 自有产品
        List<PerformanceReportProductOwn> reportProductOwnList = performanceReportProductOwnService
                .list(new QueryWrapper<PerformanceReportProductOwn>()
                        .eq("process_instance_id", processInstanceId)
                );

        List<PerformanceReportProductOwnDTO> performanceReportProductOwnDTOS = reportProductOwnList.stream()
                .map(item -> {
                    PerformanceReportProductOwnDTO dto = HyperBeanUtils.copyPropertiesByJackson(item, PerformanceReportProductOwnDTO.class);
                    dto.setZdProjectProductId(UUID.randomUUID().toString());
                    dto.setScarceGoodsNum(item.getProductNum() - item.getScarceGoodsOrderNum());
                    return dto;
                })
                .collect(Collectors.toList());

        performanceReportProductOwnDTOS = performanceReportProductOwnDTOS.stream()
                .filter(item -> item.getProductNum() > item.getScarceGoodsOrderNum())
                .collect(Collectors.toList());

        for (PerformanceReportProductOwnDTO performanceReportProductOwnDTO : performanceReportProductOwnDTOS) {
            CrmProductWholesalePriceVO objEntity = remoteProductWholesalePriceService.getInfo(performanceReportProductOwnDTO.getStuffCode()).getObjEntity();
            if(objEntity != null) {
                performanceReportProductOwnDTO.setSellInPrice(objEntity.getSellinPrice());
                performanceReportProductOwnDTO.setWholesalePrice(objEntity.getWholesalePrice());
            }
            // 软硬分离产品
            List<PerformanceReportProductSeparation> reportProductSeparations = performanceReportProductSeparationService.list(new QueryWrapper<PerformanceReportProductSeparation>()
                    .eq("performance_report_product_id", performanceReportProductOwnDTO.getId())
                    .eq("product_classification", 1)
                    .eq("del_flag", 0));

            List<PerformanceReportProductSeparationDTO> performanceReportProductSeparationDTOS = HyperBeanUtils.copyListPropertiesByJackson(reportProductSeparations, PerformanceReportProductSeparationDTO.class);
            performanceReportProductOwnDTO.setPerformanceReportProductSeparationDTOS(performanceReportProductSeparationDTOS);
        }

        TableDataInfo tableDataInfo = new TableDataInfo();
        tableDataInfo.setTotalCount(new PageInfo(performanceReportProductOwnDTOS).getTotal());
        tableDataInfo.setList(performanceReportProductOwnDTOS);
        return tableDataInfo;
    }

    @Override
    public List<PsnVO> exportPerformanceReportProductSn(String processInstanceId) {
        PerformanceReport performanceReport = queryByProcessInstanceId(processInstanceId);
        List<PsnVO> psnVOS = new ArrayList<>();
        if (performanceReport.getSupplierType() == 1){
            List<PerformanceReportProductSeparationSn> performanceReportProductSeparationSnList = performanceReportProductSeparationSnService.selectPerformanceReportProductSeparationSn(processInstanceId);
            psnVOS = HyperBeanUtils.copyListPropertiesByJackson(performanceReportProductSeparationSnList, PsnVO.class);
        }else if (performanceReport.getSupplierType() == 2){
            List<PerformanceReportProductOwnSn> performanceReportProductOwnSns = performanceReportProductOwnSnMapper.selectPerformanceReportProductSn(processInstanceId);
            psnVOS = HyperBeanUtils.copyListPropertiesByJackson(performanceReportProductOwnSns, PsnVO.class);
        }
        return psnVOS;
    }

    @Override
    public Integer getOrderStatus(String processInstanceId) {
        PerformanceReport performanceReport = queryByProcessInstanceId(processInstanceId);
        if (performanceReport.getTimeLimit() != null){
            return 1;
        }
        List<PerformanceReportProductOwnDTO> performanceReportProductOwnDTOList = getPerformanceReportProductByProcessInstanceId(processInstanceId);
        if (performanceReport.getSupplierType() == 1){
            for (PerformanceReportProductOwnDTO performanceReportProductOwnDTO : performanceReportProductOwnDTOList){
                CrmProductSeparationRelVo crmProductSeparationRelVoDefault = remoteProductSeparationRelService.getCrmProductSeparationRelDefaultHardware(performanceReportProductOwnDTO.getStuffCode()).getObjEntity();
                if (crmProductSeparationRelVoDefault == null){
                    throw new CrmException(performanceReportProductOwnDTO.getStuffCode() + "无软硬分离信息");
                }
                CrmProductWholesalePriceVO crmProductWholesalePriceVO = remoteProductWholesalePriceService.getInfo(crmProductSeparationRelVoDefault.getSeparationStuffCode()).getObjEntity();
                if (crmProductWholesalePriceVO == null){
                    throw new CrmException(performanceReportProductOwnDTO.getStuffCode() + "缺少批发价！");
                }
                if (crmProductSeparationRelVoDefault.getQuotedPrice().compareTo(crmProductWholesalePriceVO.getSellinPrice()) < 0){
                    return 2;
                }
            }
        }else if (performanceReport.getSupplierType() == 2){
            for (PerformanceReportProductOwnDTO performanceReportProductOwnDTO : performanceReportProductOwnDTOList){
                CrmProductWholesalePriceVO crmProductWholesalePriceVO = remoteProductWholesalePriceService.getInfo(performanceReportProductOwnDTO.getStuffCode()).getObjEntity();
                if (crmProductWholesalePriceVO == null){
                    throw new CrmException(performanceReportProductOwnDTO.getStuffCode() + "缺少批发价！");
                }
                if (performanceReportProductOwnDTO.getQuotedPrice().compareTo(crmProductWholesalePriceVO.getWholesalePrice()) < 0){
                    return 2;
                }
            }
        }

        return 0;
    }
    @Override
    public Boolean bindingDeviceSerialNumber(String serialNumber) {
        List<String> listSerialNumber = Arrays.asList(serialNumber.split(","));
        // 校验设备序列号是否为真实存在的序列号
        JsonObject<List<String>> notExistSns = remoteProductService.getNotExistSns(listSerialNumber);
        if (notExistSns.isSuccess() && CollectionUtils.isEmpty(notExistSns.getObjEntity())) {
            return true;
        } else {
            List<String> notExistList = notExistSns.getObjEntity();
            String notExistStr = String.join("、", notExistList);
            throw new CrmException("以下序列号不存在：" + notExistStr);
        }
    }

    @Override
    public PerformanceReportProductStatisticsDTO getPerformanceReportProductStatistics(String processInstanceId) {
        PerformanceReport performanceReport = queryByProcessInstanceId(processInstanceId);
        PerformanceReportProductStatisticsDTO performanceReportProductStatisticsDTO = performanceReportProductOwnMapper.selectProductStatistics(processInstanceId);

        if (performanceReportProductStatisticsDTO == null){
            throw new CrmException("请添加产品");
        }
        // 查询已关联序列号
        if (performanceReport.getSupplierType() == 1){
            int productSeparationSnStatistics = performanceReportProductSeparationSnMapper.selectProductSeparationSnStatistics(processInstanceId);
            performanceReportProductStatisticsDTO.setMatchTotalSN(productSeparationSnStatistics);
            PerformanceReportProductStatisticsDTO performanceReportProductStatisticsDTO1 = performanceReportProductSeparationMapper.selectProductStatistics(processInstanceId);
            if (performanceReportProductStatisticsDTO1 != null){
                performanceReportProductStatisticsDTO.setPriceDifferenceTotal(performanceReportProductStatisticsDTO1.getPriceDifferenceTotal());
                performanceReportProductStatisticsDTO.setOsDifferenceTotalPrice(performanceReportProductStatisticsDTO1.getOsDifferenceTotalPrice());
            }
            List<PerformanceReportProductOwn> performanceReportProductOwns = performanceReportProductOwnService.list(new LambdaQueryWrapper<PerformanceReportProductOwn>()
                    .eq(PerformanceReportProductOwn::getProcessInstanceId, processInstanceId));
            List<CrmProductWholesalePriceVO> crmProductWholesalePriceVOS = remoteProductWholesalePriceService.batchGetInfo(performanceReportProductOwns.stream().map(PerformanceReportProductOwn::getStuffCode).collect(Collectors.toList())).getObjEntity();
            boolean anyMatch = performanceReportProductOwns.stream().anyMatch(performanceReportProductOwn ->
                    performanceReportProductOwn.getDealPrice().add(Optional.ofNullable(performanceReportProductOwn.getRebatePrice()).orElse(BigDecimal.ZERO))
                            .compareTo(Objects.requireNonNull(crmProductWholesalePriceVOS.stream()
                                    .filter(crmProductWholesalePriceVO -> Objects.equals(crmProductWholesalePriceVO.getStuffCode(), performanceReportProductOwn.getStuffCode()))
                                    .findFirst().orElse(null)).getSellinPrice()) < 0);
            performanceReportProductStatisticsDTO.setSpecialPrice(anyMatch ? 1 : 0);
        }else if (performanceReport.getSupplierType() == 2){
            int productSnStatistics = performanceReportProductOwnSnMapper.selectProductSnStatistics(processInstanceId);
            performanceReportProductStatisticsDTO.setMatchTotalSN(productSnStatistics);
        }
        return performanceReportProductStatisticsDTO;
    }

    @Override
    public List<CrmAgentAuthenticationVO> selectSupplierNameByChannelCompanyId(String channelCompanyId, String projectId) {
        List<CrmAgentAuthenticationVO> crmAgentAuthenticationVOS = remoteCrmAgentAuthenticationService.getAgentAuthentication(channelCompanyId).getObjEntity();
        if (CollectionUtils.isEmpty(crmAgentAuthenticationVOS)){
            return new ArrayList<>();
        }
        // 当年认证
        AgentAuthentication agentAuthentication = agentAuthenticationMapper.selectAgentAuthenticationThisYear(channelCompanyId);
        // 省代业绩上报时，OD项目签约模式为【下单】时，供应方只能是国代;二级代理业绩上报时，上报对像可以是省代也可以是国代;
        // 总代直签时(包括公司项目或省代QD项目签约模式为【直签】)供货方为【省代自己】或【国代】
        JsonObject<CrmProjectDirectlyVo> projectInfo = remoteProjectDirectlyClient.getProjectInfo(projectId);
        if (projectInfo.isSuccess() && projectInfo.getObjEntity() != null){
            CrmProjectDirectlyVo crmProjectDirectlyVo = projectInfo.getObjEntity();
            if (crmProjectDirectlyVo.getZdType() == 1){
                List<CrmAgentAuthenticationVO> authenticationVOList;
                if (Objects.equals(crmProjectDirectlyVo.getZdCategory(), "省代")){
                    authenticationVOList = new ArrayList<>(crmAgentAuthenticationVOS.stream().filter(crmAgentAuthenticationVO -> crmAgentAuthenticationVO.getAgentClassification() == 1).toList());
                    if (agentAuthentication != null && agentAuthentication.getAgentClassification() == 2){
                        authenticationVOList.add(HyperBeanUtils.copyProperties(agentAuthentication, CrmAgentAuthenticationVO::new));
                    }
                }else {
                    authenticationVOList = crmAgentAuthenticationVOS.stream().filter(crmAgentAuthenticationVO -> crmAgentAuthenticationVO.getAgentClassification() == 1 && Objects.equals(crmAgentAuthenticationVO.getAgentId(), channelCompanyId)).toList();
                }
                return authenticationVOList;
            }else {
                return crmAgentAuthenticationVOS;
            }
        }

        JsonObject<CrmProjectAgentVo> info = remoteProjectAgentClient.getInfo(projectId);
        if (info.isSuccess() && info.getObjEntity() != null){
            CrmProjectAgentVo crmProjectAgentVo = info.getObjEntity();
            if (crmProjectAgentVo.getSigningType() == 1){
                List<CrmAgentAuthenticationVO> authenticationVOList = crmAgentAuthenticationVOS.stream().filter(crmAgentAuthenticationVO -> crmAgentAuthenticationVO.getAgentClassification() == 1).toList();
                return authenticationVOList;
            }else {
                if (agentAuthentication != null && agentAuthentication.getAgentClassification() == 2){
                    List<CrmAgentAuthenticationVO> authenticationVOList = crmAgentAuthenticationVOS.stream().filter(crmAgentAuthenticationVO -> crmAgentAuthenticationVO.getAgentClassification() == 1).toList();
                    authenticationVOList.add(HyperBeanUtils.copyProperties(agentAuthentication, CrmAgentAuthenticationVO::new));
                    return authenticationVOList;
                }else {
                    return crmAgentAuthenticationVOS;
                }
            }
        }
        // 跨总代业绩上报
        List<PerformanceReportCrossAgentSubmit> list = performanceReportCrossAgentSubmitService.list(new LambdaQueryWrapper<PerformanceReportCrossAgentSubmit>()
                .eq(PerformanceReportCrossAgentSubmit::getProjectId, projectId)
                .isNotNull(PerformanceReportCrossAgentSubmit::getEffectiveTime));
        if (CollectionUtils.isNotEmpty(list)){
            for (PerformanceReportCrossAgentSubmit performanceReportCrossAgentSubmit : list){
                List<CrmAgentAuthenticationVO> authenticationVOList = crmAgentAuthenticationVOS.stream().filter(crmAgentAuthenticationVO -> Objects.equals(crmAgentAuthenticationVO.getAgentId(), performanceReportCrossAgentSubmit.getTargetAgentId())).toList();
                if (CollectionUtils.isEmpty(authenticationVOList)){
                    AgentAuthentication agentAuthentication1 = agentAuthenticationMapper.selectAgentAuthenticationThisYear(performanceReportCrossAgentSubmit.getTargetAgentId());
                    crmAgentAuthenticationVOS.add(HyperBeanUtils.copyProperties(agentAuthentication1, CrmAgentAuthenticationVO::new));
                }
            }
        }

        return crmAgentAuthenticationVOS;
    }

    @Override
    public BigDecimal selectPerformanceReportAvailableAdvancePayment(String channelCompanyId, String supplierId) {
        // 全部缴纳预付款
        BigDecimal totalAdvancePayment = agentAuthPaymentDisburseMapper.selectPerformanceReportTotalAdvancePayment(channelCompanyId, supplierId);
        // 使用预付款
        BigDecimal usedAdvancePayment = performanceReportMapper.selectPerformanceReportUsedAdvancePayment(channelCompanyId, supplierId);
        return totalAdvancePayment.subtract(usedAdvancePayment);
    }

    @Override
    public List<String> selectCrmProjectProductOwnByProjectId(String projectId) {
        List<PerformanceReport> performanceReportList = list(new QueryWrapper<PerformanceReport>().eq("project_id", projectId).ne("report_status", 2).eq("del_flag", 0));
        List<String> projectRecordIds = new ArrayList<>();
        ListUtils.emptyIfNull(performanceReportList).forEach(performanceReport -> {
            List<PerformanceReportProductOwn> reportProductOwnList = performanceReportProductOwnService.
                    list(new QueryWrapper<PerformanceReportProductOwn>()
                            .eq("process_instance_id", performanceReport.getProcessInstanceId())
                            .eq("del_flag", 0));
            projectRecordIds.addAll(reportProductOwnList.stream().map(PerformanceReportProductOwn::getProjectRecordId).collect(Collectors.toList()));
        });
        return projectRecordIds;
    }

    @Override
    public Map<String, String> selectPerformanceReportProcessNumberByIds(List<String> ids) {
        List<PerformanceReport> performanceReports = listByIds(ids);
        return ListUtils.emptyIfNull(performanceReports).stream()
                .collect(Collectors.toMap(
                        PerformanceReport::getId,
                        PerformanceReport::getProcessNumber
                ));
    }

    @Override
    public PerformanceReportSelloutVO selectPerformanceReportSelloutVO(Integer lockType, String processNumber) {
        PerformanceReport performanceReport = new PerformanceReport();
        PerformanceReportSelloutVO performanceReportSelloutVO = new PerformanceReportSelloutVO();
        if (lockType == null || lockType == 1){
            performanceReport = getOne(new QueryWrapper<PerformanceReport>().eq("process_number", processNumber).eq("del_flag", 0));
        }else if (lockType == 4){
            PerformanceReportReturnExchange reportReturnExchange = performanceReportReturnExchangeMapper.selectOne(new LambdaQueryWrapper<PerformanceReportReturnExchange>().eq(FlowBaseEntity::getProcessNumber, processNumber)
                    .eq(ReturnExchange::getDelFlag, 0).last("limit 1"));
            performanceReport = getOne(new LambdaQueryWrapper<PerformanceReport>().eq(PerformanceReport::getProcessInstanceId, reportReturnExchange.getParentProcessInstanceId()).eq(PerformanceReport::getDelFlag, 0));
            performanceReportSelloutVO.setReturnExchangeProcessNumber(reportReturnExchange.getProcessNumber());
            Set<ApproveNode> approveNodeSet = tfsNodeClient.queryNodeByProcessInstanceId(reportReturnExchange.getProcessInstanceId()).getObjEntity();
            performanceReportSelloutVO.setReturnExchangeApprovalNode(approveNodeSet);
        }
        performanceReportSelloutVO.setProcessNumber(processNumber);
        performanceReportSelloutVO.setChannelCompanyName(performanceReport.getChannelCompanyName());
        performanceReportSelloutVO.setCustomerName(performanceReport.getCustomerName());
        performanceReportSelloutVO.setSaleTime(performanceReport.getProcessPassTime());
        Set<ApproveNode> approveNodes = tfsNodeClient.queryNodeByProcessInstanceId(performanceReport.getProcessInstanceId()).getObjEntity();
        performanceReportSelloutVO.setApprovalNode(approveNodes);

        return performanceReportSelloutVO;
    }

    @Override
    public List<String> queryPerformanceReportBySaleId(String saleId) {
        List<PerformanceReport> performanceReportList = list(new LambdaQueryWrapper<PerformanceReport>().eq(PerformanceReport::getProcessState, 1)
                .eq(PerformanceReport::getProjectType, 1).eq(PerformanceReport::getSaleId, saleId));

        return performanceReportList.stream().map(PerformanceReport::getProcessNumber).collect(Collectors.toList());
    }

    @Override
    public BigDecimal selectPerformanceReportAvailableAdvancePaymentInfo(String channelCompanyName, String supplierName) {
        PrepaymentSummaryVO objEntity = remoteCrmAgentPaymentVerificationService.verificationCount(channelCompanyName, supplierName).getObjEntity();

        return objEntity.getAllPreAmount();
    }

    @Override
    public List<AgentPrePaymentVerificationVO> selectAvailableAdvancePaymentList(String channelCompanyId, String supplierId) {
        List<AgentPrePaymentVerificationVO> objEntity = remoteCrmAgentPaymentVerificationService.queryPrePaymentVerificationList(channelCompanyId, supplierId).getObjEntity();

        return objEntity;
    }
}
