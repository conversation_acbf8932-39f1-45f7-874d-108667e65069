package com.topsec.crm.flow.core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.topsec.crm.flow.api.dto.handoverProcess.HandoverProcessReviewFlowLaunchDTO;
import com.topsec.crm.flow.core.entity.HandoverReviewConfig;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 *
 */
public interface HandoverReviewConfigService extends IService<HandoverReviewConfig> {
    List<HandoverReviewConfig> filterRequiredHandoverReviewConfig(HandoverProcessReviewFlowLaunchDTO launchable);

    /**
     * 根据节点id获取内容
     * @param nodeId 根据节点id获取内容
     * @return 根据节点id获取内容
     */
    String getContentByNodeId(String nodeId);

    Map<String,Boolean> generateHandoverReviewContentGateWay(List<HandoverReviewConfig> filteredHandoverReviewConfigList);
    Set<String> getNodeIds();
}
