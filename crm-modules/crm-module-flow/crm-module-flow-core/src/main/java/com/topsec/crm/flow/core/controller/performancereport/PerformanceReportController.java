package com.topsec.crm.flow.core.controller.performancereport;


import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.util.WebFilenameUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.topsec.crm.agent.api.RemoteAgentService;
import com.topsec.crm.agent.api.entity.CrmAgentAuthenticationVO;
import com.topsec.crm.agent.api.entity.CrmAgentVo;
import com.topsec.crm.contract.api.RemoteContractReviewService;
import com.topsec.crm.framework.common.bean.AgentPrePaymentVerificationVO;
import com.topsec.crm.customer.api.RemoteCustomerService;
import com.topsec.crm.customer.api.entity.CrmCustomerVo;
import com.topsec.crm.flow.api.dto.contractreview.ContractReviewMainBaseInfoDTO;
import com.topsec.crm.flow.api.dto.performancereport.*;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.*;
import com.topsec.crm.flow.core.handler.right.ProjectRightHandler;
import com.topsec.crm.flow.core.handler.right.Step00RemoveEditProcessRightHandler;
import com.topsec.crm.flow.core.mapper.AgentAuthenticationMapper;
import com.topsec.crm.flow.core.mapper.PerformanceReportContractDeliveryDetailMapper;
import com.topsec.crm.flow.core.mapstruct.PerformanceReportConvertor;
import com.topsec.crm.flow.core.process.impl.PerformanceReportProcessService;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.framework.common.bean.AreaVO;
import com.topsec.crm.framework.common.bean.FlowPerson;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.AgentEnum;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.poi.ExcelUtil;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.operation.api.RemoteAreaService;
import com.topsec.crm.operation.api.RemoteCrmLogisticsService;
import com.topsec.crm.operation.api.entity.CrmLogisticsInfoVO;
import com.topsec.crm.product.api.dto.CrmProductSeparationRelQuery;
import com.topsec.crm.product.api.entity.CrmProductAgentInventoryVO;
import com.topsec.crm.product.api.entity.CrmProductSeparationRelVo;
import com.topsec.crm.project.api.RemoteProjectAgentDirectorConfigService;
import com.topsec.crm.project.api.client.RemoteProjectDirectlyClient;
import com.topsec.crm.project.api.entity.CrmProjectAgentDirectorConfigVO;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.logistics.api.domain.BatchSubParam;
import com.topsec.logistics.api.domain.LogisticsInfo;
import com.topsec.logistics.api.domain.LogisticsInfoDetail;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.tos.common.vo.EmployeeVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@RestController
@RequestMapping("/performanceReport")
@Tag(name = "业绩上报", description = "/performanceReport")
@RequiredArgsConstructor
@Validated
public class PerformanceReportController extends BaseController {

    private final PerformanceReportService performanceReportService;
    private final PerformanceReportProcessService performanceReportProcessService;
    private final PerformanceReportProductOwnSnService performanceReportProductOwnSnService;

    private final PerformanceReportProductOwnService performanceReportProductOwnService;


    private final PerformanceReportDocService performanceReportDocService;

    private final PerformanceReportPaymentTermsService performanceReportPaymentTermsService;

    private final PerformanceReportPaymentInfoService performanceReportPaymentInfoService;


    private final RemoteContractReviewService remoteContractReviewService;

    private final PerformanceReportContractDeliveryService performanceReportContractDelivery;

    private final RemoteAreaService remoteAreaService;
    private final RemoteCrmLogisticsService remoteCrmLogisticsService;
    private final RemoteAgentService remoteAgentService;

    private final ScarceGoodsOrderService scarceGoodsOrderService;
    private final ResourceLoader resourceLoader;

    private final ContractReviewFlowService contractReviewFlowService;
    private final PerformanceReportContractDeliveryDetailMapper performanceReportContractDeliveryDetailMapper;
    private final RemoteProjectDirectlyClient remoteProjectDirectlyClient;
    private final AgentAuthenticationMapper agentAuthenticationMapper;
    private final RemoteCustomerService remoteCustomerService;
    private final RemoteProjectAgentDirectorConfigService remoteProjectAgentDirectorConfigService;
    private final PerformanceReportReturnExchangeService performanceReportReturnExchangeService;

    @Data
    public static class LogisticsInfoVO{
        @Schema(description = "物流信息")
        private LogisticsInfo logisticsInfo;

        @Schema(description = "发货序列号详情")
        private DeliveryProductSnDTO deliveryProductSnDTO;
    }

    @GetMapping("/getPerformanceReportProductByProcessInstanceId")
    @Operation(summary = "根据业绩上报流程实例ID查询产品详细信息")
    @PreFlowPermission
    public JsonObject<List<PerformanceReportProductOwnDTO>> getPerformanceReportProductByProcessInstanceId(){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        List<PerformanceReportProductOwnDTO> performanceReportProductOwnDTOList = performanceReportService.getPerformanceReportProductByProcessInstanceId(processInstanceId);
        return new JsonObject<>(performanceReportProductOwnDTOList);
    }

    @GetMapping("/getPerformanceReportProductStatistics")
    @Operation(summary = "根据业绩上报流程实例ID查询产品统计信息")
    @PreFlowPermission
    public JsonObject<PerformanceReportProductStatisticsDTO> getPerformanceReportProductStatistics(){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject(performanceReportService.getPerformanceReportProductStatistics(processInstanceId));
    }

    @PostMapping("/addPerformanceReportProductSeparation")
    @Operation(summary = "匹配软硬分离产品")
    @PreFlowPermission(hasAnyNodes = {"sid-0D23126B-4A0A-45CC-BF90-84A18C8BC86C","sid-0024E5FC-BD42-4351-9D30-2F1A79F29FE3"})
    public JsonObject<Boolean> addPerformanceReportProductSeparation(@RequestBody PerformanceReportProductQuery performanceReportProductQuery){
        PerformanceReportProductOwn performanceReportProductOwn = performanceReportProductOwnService.getById(performanceReportProductQuery.getPerformanceReportProductId());
        PreFlowPermissionAspect.checkProcessInstanceId(performanceReportProductOwn.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(performanceReportProductOwnService.insertPerformanceReportProductSeparation(performanceReportProductQuery));
    }

    @PostMapping("/addBorrowForForwardOrSpecialItemSeparation")
    @Operation(summary = "匹配借转销专项备货软硬分离产品")
    @PreFlowPermission(hasAnyNodes = {"sid-0D23126B-4A0A-45CC-BF90-84A18C8BC86C","sid-0024E5FC-BD42-4351-9D30-2F1A79F29FE3"})
    public JsonObject<Boolean> addBorrowForForwardOrSpecialItemSeparation(@RequestBody PerformanceReportProductQuery performanceReportProductQuery){
        PreFlowPermissionAspect.checkProcessInstanceId(performanceReportProductQuery.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(performanceReportProductOwnService.insertBorrowForForwardOrSpecialItemSeparation(performanceReportProductQuery));
    }

    @PostMapping("/addPerformanceReportProductSeparationAll")
    @Operation(summary = "重新匹配所有软硬分离产品")
    @PreFlowPermission(hasAnyNodes = {"sid-0D23126B-4A0A-45CC-BF90-84A18C8BC86C","sid-0024E5FC-BD42-4351-9D30-2F1A79F29FE3"})
    public JsonObject<Boolean> addPerformanceReportProductSeparationAll(@RequestBody PerformanceReportProductQuery performanceReportProductQuery){
        PreFlowPermissionAspect.checkProcessInstanceId(performanceReportProductQuery.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(performanceReportProductOwnService.insertPerformanceReportProductSeparationAll(performanceReportProductQuery));
    }

    @PostMapping("/getPerformanceReportProductSeparationHardware")
    @Operation(summary = "查询 选择硬件物料代码")
    @PreFlowPermission(hasAnyNodes = {"sid-0D23126B-4A0A-45CC-BF90-84A18C8BC86C","sid-0024E5FC-BD42-4351-9D30-2F1A79F29FE3"})
    public JsonObject<List<CrmProductSeparationRelVo>> getPerformanceReportProductSeparationHardware(@RequestBody PerformanceReportProductQuery performanceReportProductQuery){
        PerformanceReportProductOwn performanceReportProductOwn = performanceReportProductOwnService.getById(performanceReportProductQuery.getPerformanceReportProductId());
        PreFlowPermissionAspect.checkProcessInstanceId(performanceReportProductOwn.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        CrmProductSeparationRelQuery crmProductSeparationRelQuery = new CrmProductSeparationRelQuery();
        crmProductSeparationRelQuery.setStuffCode(performanceReportProductOwn.getStuffCode());
        crmProductSeparationRelQuery.setContractCompanyName(performanceReportProductQuery.getSupplierName());

        List<CrmProductSeparationRelVo> performanceReportProductSeparationDTOS = performanceReportProductOwnService.selectPerformanceReportProductSeparationHardware(crmProductSeparationRelQuery);
        return new JsonObject<>(performanceReportProductSeparationDTOS);
    }

    @PostMapping("/editPerformanceReportProductSeparationSoft")
    @Operation(summary = "修改 OS物料代码")
    @PreFlowPermission(hasAnyNodes = {"sid-0D23126B-4A0A-45CC-BF90-84A18C8BC86C","sid-0024E5FC-BD42-4351-9D30-2F1A79F29FE3"})
    public JsonObject<Boolean> editPerformanceReportProductSeparationSoft(@RequestBody PerformanceReportProductSeparationDTO performanceReportProductSeparationDTO){
        PerformanceReportProductOwn performanceReportProductOwn = performanceReportProductOwnService.getById(performanceReportProductSeparationDTO.getPerformanceReportProductId());
        PreFlowPermissionAspect.checkProcessInstanceId(performanceReportProductOwn.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(performanceReportProductOwnService.updatePerformanceReportProductSeparationSoft(performanceReportProductSeparationDTO));
    }


    @GetMapping("/getPerformanceReportProductSeparationSoft")
    @Operation(summary = "查询 选择OS物料代码")
    @PreFlowPermission(hasAnyNodes = {"sid-0D23126B-4A0A-45CC-BF90-84A18C8BC86C","sid-0024E5FC-BD42-4351-9D30-2F1A79F29FE3"})
    public JsonObject<List<CrmProductSeparationRelVo>> getPerformanceReportProductSeparationSoft(@RequestParam String supplierName){
        List<CrmProductSeparationRelVo> productSeparationRelVos = performanceReportProductOwnService.selectPerformanceReportProductSeparationSoft(supplierName);
        return new JsonObject<>(productSeparationRelVos);
    }

    @PostMapping("/editPerformanceReportProductSeparationHardware")
    @Operation(summary = "修改 硬件物料代码")
    @PreFlowPermission(hasAnyNodes = {"sid-0D23126B-4A0A-45CC-BF90-84A18C8BC86C","sid-0024E5FC-BD42-4351-9D30-2F1A79F29FE3"})
    public JsonObject<Boolean> editPerformanceReportProductSeparationHardware(@RequestBody PerformanceReportProductQuery performanceReportProductQuery){
        PreFlowPermissionAspect.checkProcessInstanceId(performanceReportProductQuery.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(performanceReportProductOwnService.updatePerformanceReportProductSeparationHardware(performanceReportProductQuery));
    }

    @PostMapping("/getPerformanceReportProductSeparationSn")
    @Operation(summary = "查询库存序列号")
    @PreFlowPermission
    public JsonObject<List<CrmProductAgentInventoryVO>> getPerformanceReportProductSeparationSn(@RequestBody PerformanceReportProductQuery performanceReportProductQuery){
        PreFlowPermissionAspect.checkProcessInstanceId(performanceReportProductQuery.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(performanceReportProductOwnService.getPerformanceReportProductSeparationSn(performanceReportProductQuery));
    }

    @PostMapping("/editPerformanceReportProductSeparationSn")
    @Operation(summary = "国代修改序列号")
    @PreFlowPermission(hasAnyNodes = {"sid-0D23126B-4A0A-45CC-BF90-84A18C8BC86C","sid-0024E5FC-BD42-4351-9D30-2F1A79F29FE3"})
    public JsonObject<Boolean> editPerformanceReportProductSeparationSn(@RequestBody PerformanceReportProductSeparationDTO performanceReportProductSeparationDTO){
        PerformanceReportProductOwn performanceReportProductOwn = performanceReportProductOwnService.getOne(new LambdaQueryWrapper<PerformanceReportProductOwn>()
                .eq(PerformanceReportProductOwn::getProcessInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID))
                .eq(PerformanceReportProductOwn::getDelFlag, 0)
                .eq(PerformanceReportProductOwn::getId, performanceReportProductSeparationDTO.getPerformanceReportProductId()));
        if (performanceReportProductOwn == null) {
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
        return new JsonObject<>(performanceReportProductOwnService.updatePerformanceReportProductSeparationSn(performanceReportProductSeparationDTO));
    }

    @PostMapping("/editPerformanceReportProductSn")
    @Operation(summary = "省代修改序列号")
    @PreFlowPermission(hasAnyNodes = {"sid-9D4E9504-3166-4E0A-B0A3-767A830141E1","sid-B961E1EB-8D21-4C72-A853-6D7E1BA27F8E"})
    public JsonObject<Boolean> editPerformanceReportProductSn(@RequestBody PerformanceReportProductOwnDTO performanceReportProductOwnDTO){
        PerformanceReportProductOwn performanceReportProductOwn = performanceReportProductOwnService.getOne(new LambdaQueryWrapper<PerformanceReportProductOwn>()
                .eq(PerformanceReportProductOwn::getProcessInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID))
                .eq(PerformanceReportProductOwn::getDelFlag, 0)
                .eq(PerformanceReportProductOwn::getId, performanceReportProductOwnDTO.getId()));
        if (performanceReportProductOwn == null) {
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
        return new JsonObject<>(performanceReportProductOwnService.updatePerformanceReportProductSn(performanceReportProductOwnDTO));
    }

    @DeleteMapping("/deletePerformanceReportProductSeparationSn")
    @Operation(summary = "清空序列号")
    @PreFlowPermission(hasAnyNodes = {"sid-0D23126B-4A0A-45CC-BF90-84A18C8BC86C","sid-0024E5FC-BD42-4351-9D30-2F1A79F29FE3"})
    public JsonObject<Boolean> deletePerformanceReportProductSeparationSn(@RequestParam String performanceReportProductId){
        PerformanceReport performanceReport = performanceReportService.selectPerformanceReportSupplierName(performanceReportProductId);
        PreFlowPermissionAspect.checkProcessInstanceId(performanceReport.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(performanceReportProductOwnService.deletePerformanceReportProductSn(performanceReportProductId));
    }

    @GetMapping(value="/export")
    @Operation(summary = "导出序列号")
    @PreFlowPermission
    public void exportPerformanceReportProductSn() throws Exception{
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        List<PsnVO> psnVOS = performanceReportService.exportPerformanceReportProductSn(processInstanceId);
        ExcelUtil<PsnVO> excelUtil = new ExcelUtil<>(PsnVO.class);
        excelUtil.exportExcel(response, psnVOS,"关联序列号");
    }

    @GetMapping("/getDynastyApprover")
    @Operation(summary = "查询国代审批人")
    @PreFlowPermission
    public JsonObject<List<FlowPerson>> getDynastyApprover() {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject<>(performanceReportService.selectDynastyApprover(processInstanceId));
    }

    @PostMapping("/updatePerformanceReportProductAcceptOrder")
    @Operation(summary = "选择是否接单")
    @PreFlowPermission(hasAnyNodes = {"sid-9D4E9504-3166-4E0A-B0A3-767A830141E1","sid-0D23126B-4A0A-45CC-BF90-84A18C8BC86C"})
    public JsonObject<Boolean> updatePerformanceReportProductAcceptOrder(@RequestBody PerformanceReportProcessVO performanceReportProcessVO){
        PreFlowPermissionAspect.checkProcessInstanceId(performanceReportProcessVO.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(performanceReportService.updatePerformanceReportProductAcceptOrder(performanceReportProcessVO));
    }

    // 1.登录人与发起人一致 则展示配件信息 child
    // 2.登录人为国代 展示软硬分离
    // 3.登录人为省代 展示出货序列号
    // 4.其他情况同1
    @GetMapping("/getPerformanceReportDetailsByProcessInstanceId")
    @Operation(summary = "根据业绩上报流程实例ID查询业绩上报流程详细信息")
    @PreFlowPermission
    public JsonObject<PerformanceReportDetailInfo> getPerformanceReportDetailsByProcessInstanceId(){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        PerformanceReportDetailInfo detail = performanceReportService.getPerformanceReportDetailsByProcessInstanceId(processInstanceId);
        PerformanceReportVO baseInfo = detail.getBaseInfo();
        String supplierId = baseInfo.getSupplierId();
        if (StringUtils.isNotBlank(supplierId)){
            String supplierCustomId = Optional.ofNullable(remoteCustomerService.batchFindByAgentId(List.of(supplierId)))
                    .map(JsonObject::getObjEntity)
                    .orElse(Collections.emptyList())
                    .stream().findFirst()
                    .map(CrmCustomerVo::getId).orElse(null);
            baseInfo.setSupplierCustomId(supplierCustomId);
        }

        String createUser = baseInfo.getCreateUser();
        String agentId = UserInfoHolder.getCurrentAgentId();
        String currentPersonId = UserInfoHolder.getCurrentPersonId();
        String saleId = baseInfo.getSaleId();
        if (StringUtils.isNotBlank(saleId)){
            String saleName = Optional.ofNullable(tosEmployeeClient.findVisibleInfoById(saleId))
                    .map(JsonObject::getObjEntity)
                    .map(item -> {
                        return Stream.of(item.getName(), item.getJobNo()).filter(StringUtils::isNotBlank)
                                .collect(Collectors.joining());
                    }).orElse(null);
            baseInfo.setSaleName(saleName);
        }
        Integer currentAgentLevel=null;
        if (StringUtils.isNotBlank(agentId)) {
            JsonObject<CrmAgentVo> agentInfo = remoteAgentService.getAgentInfo(agentId);
            currentAgentLevel = agentInfo.isSuccess() && agentInfo.getObjEntity() != null ? agentInfo.getObjEntity().getLevel() : null;
        }


        List<PerformanceReportProductOwnDTO> productList = detail.getProductList();
        if (Objects.equals(createUser, currentPersonId)){
            //登录人为创建人展示树形结构
            detail.setProductList(buildProductTree(productList));
        }else if (Objects.equals(AgentEnum.AgentLevelEnum.NATIONAL_DISTRIBUTOR.getLevel(), currentAgentLevel)) {
            //登录人为国代展示软硬分离
            for (PerformanceReportProductOwnDTO performanceReportProductOwnDTO : productList) {
                //将上报序列号设为空
                performanceReportProductOwnDTO.setPerformanceReportProductOwnSnDTOS(Collections.emptyList());
            }
        }else if (Objects.equals(AgentEnum.AgentLevelEnum.PROVINCIAL_DISTRIBUTOR.getLevel(), currentAgentLevel)){
            //登录人为省代
            for (PerformanceReportProductOwnDTO performanceReportProductOwnDTO : productList) {
                //将软硬分离序列号设为空
                performanceReportProductOwnDTO.setPerformanceReportProductSeparationDTOS(Collections.emptyList());
            }
        }else {
            //其他情况展示树形结构
            detail.setProductList(buildProductTree(productList));
        }

        return new JsonObject<>(detail);
    }

    @PostMapping("/launch")
    @Operation(summary = "发起业绩上报")
    @PreAuthorize(hasAnyPermission = {"crm_flow_performance_report","crm_agent_flow_performance_report"})
    public JsonObject<Boolean> launch(@Valid @RequestBody PerformanceReportFlowLaunchDTO launchDTO){
        return new JsonObject<>(performanceReportProcessService.launch(launchDTO));
    }

    @PostMapping("/updateDeliveryNo")
    @Operation(summary = "更新序列号发货基础信息")
    public JsonObject<Boolean> updateDeliveryNo(@Valid @NotEmpty @RequestBody List<DeliveryNoDTO> dtoList){
        return new JsonObject<>(performanceReportProductOwnSnService.updateDeliveryNo(dtoList));
    }

    @PostMapping("/savePerformanceReportProductInfo")
    @Operation(summary = "02步保存产品信息")
    @PreFlowPermission(hasAnyNodes = {"sid-0D23126B-4A0A-45CC-BF90-84A18C8BC86C","sid-0024E5FC-BD42-4351-9D30-2F1A79F29FE3"})
    public JsonObject<Boolean> savePerformanceReportProductInfo(@RequestBody PerformanceReportVO performanceReportVO){
        PreFlowPermissionAspect.checkProcessInstanceId(performanceReportVO.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(performanceReportService.savePerformanceReportProductInfo(performanceReportVO));

    }

    @PostMapping("/editSigningContractNumber")
    @Operation(summary = "01/02修改签订合同号")
    @PreFlowPermission(hasAnyNodes = {"sid-9D4E9504-3166-4E0A-B0A3-767A830141E1","sid-0D23126B-4A0A-45CC-BF90-84A18C8BC86C"})
    public JsonObject<Boolean> editSigningContractNumber(@RequestBody PerformanceReportVO performanceReportVO){
        PreFlowPermissionAspect.checkProcessInstanceId(performanceReportVO.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(performanceReportService.updateSigningContractNumber(performanceReportVO));

    }

    @PostMapping("/savePerformanceReportDoc")
    @Operation(summary = "上传附件")
    @PreFlowPermission
    public JsonObject<Boolean> savePerformanceReportDoc(@RequestBody List<PerformanceReportDocDTO> performanceReportDocDTOS){
        PerformanceReportDocDTO performanceReportDocDTO = performanceReportDocDTOS.get(0);
        PreFlowPermissionAspect.checkProcessInstanceId(performanceReportDocDTO.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        performanceReportDocDTOS.forEach(performanceReportDocDTO1 -> {
            performanceReportDocDTO1.setProcessInstanceId(performanceReportDocDTO.getProcessInstanceId());
        });
        return new JsonObject<>(performanceReportDocService.insertPerformanceReportDocDTO(performanceReportDocDTOS));
    }

    @DeleteMapping("/deletePerformanceReportDoc")
    @Operation(summary = "删除附件")
    @PreFlowPermission
    public JsonObject<Boolean> deletePerformanceReportDoc(@RequestParam String id){
        PerformanceReportDoc byId = performanceReportDocService.getById(id);
        PreFlowPermissionAspect.checkProcessInstanceId(byId.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(performanceReportDocService.deletePerformanceReportDocDTO(id));
    }

    @PostMapping("/editPerformanceReportDoc")
    @Operation(summary = "修改附件")
    @PreFlowPermission
    public JsonObject<Boolean> editPerformanceReportDoc(@RequestBody PerformanceReportDocDTO performanceReportDocDTO){
        PreFlowPermissionAspect.checkProcessInstanceId(performanceReportDocDTO.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(performanceReportDocService.updatePerformanceReportDocDTO(performanceReportDocDTO));
    }

    @GetMapping("/getPerformanceReportDoc")
    @Operation(summary = "查询附件")
    @PreFlowPermission
    public JsonObject<List<PerformanceReportDocDTO>> getPerformanceReportDoc(){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        List<PerformanceReportDoc> performanceReportDocs = performanceReportDocService.selectPerformanceReportDocDTO(processInstanceId);

        return new JsonObject<>(HyperBeanUtils.copyListPropertiesByJackson(performanceReportDocs, PerformanceReportDocDTO.class));
    }

    @PostMapping("/editPerformanceReportPaymentTerms")
    @Operation(summary = "修改付款条款")
    @PreFlowPermission
    public JsonObject<Boolean> editPerformanceReportPaymentTerms(@RequestBody PerformanceReportPaymentTermsDTO performanceReportPaymentTermsDTO){
        PreFlowPermissionAspect.checkProcessInstanceId(performanceReportPaymentTermsDTO.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(performanceReportPaymentTermsService.updatePerformanceReportPaymentTerms(performanceReportPaymentTermsDTO));
    }

    @PostMapping("/addPerformanceReportPaymentTerms")
    @Operation(summary = "新增付款条款")
    @PreFlowPermission
    public JsonObject<Boolean> addPerformanceReportPaymentTerms(@RequestBody PerformanceReportPaymentTermsDTO performanceReportPaymentTermsDTO){
        PreFlowPermissionAspect.checkProcessInstanceId(performanceReportPaymentTermsDTO.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(performanceReportPaymentTermsService.insertPerformanceReportPaymentTerms(performanceReportPaymentTermsDTO));
    }

    @GetMapping("/getPerformanceReportPaymentTerms")
    @Operation(summary = "查询付款条款")
    @PreFlowPermission
    public JsonObject<List<PerformanceReportPaymentTermsDTO>> getPerformanceReportPaymentTerms(){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        List<PerformanceReportPaymentTerms> performanceReportPaymentTerms = performanceReportPaymentTermsService.selectPerformanceReportPaymentTerms(processInstanceId);

        return new JsonObject<>(HyperBeanUtils.copyListPropertiesByJackson(performanceReportPaymentTerms, PerformanceReportPaymentTermsDTO.class));
    }

    @DeleteMapping("/deletePerformanceReportPaymentTerms")
    @Operation(summary = "删除付款条款")
    @PreFlowPermission
    public JsonObject<Boolean> deletePerformanceReportPaymentTerms(@RequestParam String id){
        PerformanceReportPaymentTerms byId = performanceReportPaymentTermsService.getById(id);
        PreFlowPermissionAspect.checkProcessInstanceId(byId.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(performanceReportPaymentTermsService.deletePerformanceReportPaymentTerms(id));
    }

    @PostMapping("/addPerformanceReportPaymentInfo")
    @Operation(summary = "新增付款信息")
    @PreFlowPermission
    public JsonObject<Boolean> addPerformanceReportPaymentInfo(@RequestBody PerformanceReportPaymentInfoDTO performanceReportPaymentInfoDTO){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        performanceReportPaymentInfoDTO.setProcessInstanceId(processInstanceId);
        return new JsonObject<>(performanceReportPaymentInfoService.insertPerformanceReportPaymentInfo(performanceReportPaymentInfoDTO));
    }

    @PostMapping("/editPerformanceReportPaymentInfo")
    @Operation(summary = "修改付款信息")
    @PreFlowPermission
    public JsonObject<Boolean> editPerformanceReportPaymentInfo(@RequestBody PerformanceReportPaymentInfoDTO performanceReportPaymentInfoDTO){
        PreFlowPermissionAspect.checkProcessInstanceId(performanceReportPaymentInfoDTO.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(performanceReportPaymentInfoService.updatePerformanceReportPaymentInfo(performanceReportPaymentInfoDTO));
    }

    @GetMapping("/getPerformanceReportPaymentInfo")
    @Operation(summary = "查询付款信息")
    @PreFlowPermission
    public JsonObject<List<PerformanceReportPaymentInfoDTO>> getPerformanceReportPaymentInfo(){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        List<PerformanceReportPaymentInfo> performanceReportPaymentInfos = performanceReportPaymentInfoService.selectPerformanceReportPaymentInfoByProcessInstanceId(processInstanceId);
        return new JsonObject<>(HyperBeanUtils.copyListPropertiesByJackson(performanceReportPaymentInfos, PerformanceReportPaymentInfoDTO.class));
    }

    @DeleteMapping("/deletePerformanceReportPaymentInfo")
    @Operation(summary = "删除付款信息")
    @PreFlowPermission
    public JsonObject<Boolean> deletePerformanceReportPaymentInfo(@RequestParam String id){
        PerformanceReportPaymentInfo byId = performanceReportPaymentInfoService.getById(id);
        PreFlowPermissionAspect.checkProcessInstanceId(byId.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(performanceReportPaymentInfoService.deletePerformanceReportPaymentInfo(id));
    }

    @GetMapping("/getApprover")
    @Operation(summary = "查询04合同管理部审批人")
    @PreFlowPermission
    public JsonObject<FlowPerson> getApprover() {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        PerformanceReport performanceReport = performanceReportService.getOne(new QueryWrapper<PerformanceReport>().eq("process_instance_id", processInstanceId));
        String deptId = "";

        if (performanceReport.getProjectType() == 1){
            EmployeeVO employeeVO = tosEmployeeClient.findById(performanceReport.getSaleId()).getObjEntity();
            if (employeeVO != null && employeeVO.getDept() != null){
                deptId = employeeVO.getDept().getUuid();
            }
        }else {
            AgentAuthentication agentAuthentication = agentAuthenticationMapper.selectAgentAuthenticationThisYear(performanceReport.getChannelCompanyId());
            if (agentAuthentication != null && StringUtils.isNotBlank(agentAuthentication.getSaleId())){
                EmployeeVO employeeVO = tosEmployeeClient.findById(agentAuthentication.getSaleId()).getObjEntity();
                if (employeeVO != null && employeeVO.getDept() != null){
                    deptId = employeeVO.getDept().getUuid();
                }
            }else {
                CrmAgentVo crmAgentVo = remoteAgentService.getAgentInfo(performanceReport.getChannelCompanyId()).getObjEntity();
                JsonObject<CrmProjectAgentDirectorConfigVO> listJsonObject = remoteProjectAgentDirectorConfigService.getAgentDirectorByAreaId(crmAgentVo.getProvinceCode());
                if (listJsonObject.isSuccess() && listJsonObject.getObjEntity() != null && !listJsonObject.getObjEntity().getApprover().isEmpty()) {
                    CrmProjectAgentDirectorConfigVO.Person person = listJsonObject.getObjEntity().getApprover().get(0);
                    deptId = tosEmployeeClient.findById(person.getId()).getObjEntity().getDept().getUuid();
                }
            }
        }
        if (StringUtils.isBlank(deptId)){
            throw new CrmException("04审批人为空！");
        }
        //deptId="221";
        JsonObject<FlowPerson> byPersonId = remoteContractReviewService.getByPersonIdToPerformanceReport(deptId);
        if (byPersonId.isSuccess() && byPersonId.getObjEntity() != null) {
            return new JsonObject<>(byPersonId.getObjEntity());
        }else {
            throw new CrmException("04审批人为空！");
        }

    }

    @PostMapping("/completionConditions")
    @Operation(summary = "办理完毕条件")
    @PreFlowPermission
    public JsonObject<Map<String, Object>> completionConditions(@RequestBody PerformanceReportProductQuery performanceReportProductQuery){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        performanceReportProductQuery.setProcessInstanceId(processInstanceId);
        return new JsonObject<>(performanceReportService.selectCompletionConditions(performanceReportProductQuery));
    }

    @PostMapping("/backConditions")
    @Operation(summary = "流程退回判断")
    @PreFlowPermission
    public JsonObject<Map<String, Object>> backConditions(@RequestBody PerformanceReportProductQuery performanceReportProductQuery){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        performanceReportProductQuery.setProcessInstanceId(processInstanceId);
        return new JsonObject<>(performanceReportService.backConditions(performanceReportProductQuery));
    }

    @PostMapping("/deleteConditions")
    @Operation(summary = "流程删除判断")
    @PreFlowPermission
    public JsonObject<Map<String, Object>> deleteConditions(@RequestBody PerformanceReportProductQuery performanceReportProductQuery){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        performanceReportProductQuery.setProcessInstanceId(processInstanceId);
        return new JsonObject<>(performanceReportService.deleteConditions(performanceReportProductQuery));
    }

    @GetMapping("/querySelectedProductDeliveryInfo")
    @Operation(summary = "查询选中的产品发货信息")
    @PreFlowPermission
    public JsonObject<List<PerformanceReportContractDeliveryDTO>> querySelectedProductDeliveryInfo(@RequestParam Set<String> recordIdSet){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject<>(performanceReportService.querySelectedProductDeliveryInfo(processInstanceId,recordIdSet));
    }

    @GetMapping("/getAgentApprover")
    @Operation(summary = "查询代理商审批人")
    @PreFlowPermission
    public JsonObject<List<FlowPerson>> getAgentApprover(@RequestParam String supplierId) {
        return new JsonObject<>(performanceReportService.selectAgentApprover(supplierId));
    }

    @PostMapping("/pagePerformanceReport")
    @Operation(summary = "业绩上报分页")
    public JsonObject<PageUtils<PerformanceReportVO>> pagePerformanceReport(@RequestBody PerformanceReportQuery query){
        remoteProjectDirectlyClient.hasRight(query.getProjectId(), UserInfoHolder.getCurrentPersonId());
        CrmAssert.hasText(query.getProjectId(),"项目id不能为空");
        IPage<PerformanceReportVO> convert = performanceReportService.pagePerformanceReport(query).convert(PerformanceReportConvertor.INSTANCE::toReportVO);
        return new JsonObject<>(new PageUtils<>(convert));
    }

    @GetMapping("/getCustomerInfoByProcessInstanceId")
    @Operation(summary = "根据业绩上报流程实例ID查询最终用户")
    @PreFlowPermission
    public JsonObject<ScarceGoodsOrderDTO> getCustomerInfoByperformanceReportId() {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject<>(performanceReportService.getCustomerInfoByprocessInstanceId(processInstanceId));
    }

    @GetMapping("/productInfoByProcessInstanceId")
    @Operation(summary = "根据业绩上报流程实例ID查询产品列表【缺货下单】")
    @PreFlowPermission
    public JsonObject<TableDataInfo> getproductInfoByProcessInstanceId(){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject<>(performanceReportService.productInfoByProcessInstanceId(processInstanceId));
    }

    @PostMapping("/addOrder")
    @Operation(summary = "提交国代缺货下单")
    @PreFlowPermission
    public JsonObject<Boolean> addOrder(@RequestBody ScarceGoodsOrderDTO scarceGoodsOrderDTO){
        return new JsonObject<>(scarceGoodsOrderService.addOrder(scarceGoodsOrderDTO));
    }

    @GetMapping("/getOrderStatus")
    @Operation(summary = "判断订单是否为特价单或有账期签约单")
    @PreFlowPermission
    public JsonObject<Integer> getOrderStatus() {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject<>(performanceReportService.getOrderStatus(processInstanceId));
    }

    @GetMapping("/exportDeliveryInfo")
    @Operation(summary = "导出发货信息")
    @PreFlowPermission
    public void exportDeliveryInfo( HttpServletResponse response)   {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        List<PerformanceReportContractDeliveryDTO> deliveryVOList= performanceReportService.getContractDeliveryVOList(processInstanceId);
        //这个主要给导入用
        List<PerformanceReportDeliveryExportDTO.ExpressProductExtendInfo> productExtendInfoList = new ArrayList<>();
        List<PerformanceReportDeliveryExportDTO> deliveryExportDtoList = deliveryVOList.stream().map(item -> {
            PerformanceReportDeliveryExportDTO deliveryExportDto = PerformanceReportConvertor.INSTANCE.toDeliveryExportDto(item);
            deliveryExportDto.setConsigneeAreaCode(Optional.ofNullable(remoteAreaService.queryByCode(item.getConsigneeAreaCode())).map(JsonObject::getObjEntity).map(AreaVO::getName).orElse(null));
            Map<String, List<PerformanceReportProductOwnSnDTO>> deliveryInfoMap = item.getDeliveryInfoMap();
            if (MapUtils.isNotEmpty(deliveryInfoMap)){
                List<PerformanceReportDeliveryExportDTO.ExpressProductInfo> expressWaybillNumbers = deliveryInfoMap.entrySet().stream().map(entry -> {

                    List<PerformanceReportDeliveryExportDTO.ExpressProductInfo.ProductInfo> productInfoList = ListUtils.emptyIfNull(entry.getValue()).stream().map(product -> {
                        PerformanceReportDeliveryExportDTO.ExpressProductInfo.ProductInfo productInfo = new PerformanceReportDeliveryExportDTO.ExpressProductInfo.ProductInfo();
                        String performanceReportProductOwnId = product.getPerformanceReportProductOwnId();
                        PerformanceReportProductOwn byId = performanceReportProductOwnService.getById(performanceReportProductOwnId);
                        productInfo.setProductName(byId.getProductName());
                        productInfo.setProductSn(product.getPsn());
                        productInfo.setProductPn(byId.getPnCode());
                        productInfo.setStuffCode(byId.getStuffCode());

                        PerformanceReportDeliveryExportDTO.ExpressProductExtendInfo expressProductExtendInfo=new PerformanceReportDeliveryExportDTO.ExpressProductExtendInfo();
                        expressProductExtendInfo.setProductName(byId.getProductName());
                        expressProductExtendInfo.setProductSn(product.getPsn());
                        expressProductExtendInfo.setProductPn(byId.getPnCode());
                        expressProductExtendInfo.setStuffCode(byId.getStuffCode());
                        expressProductExtendInfo.setProductOwnId(byId.getId());
                        expressProductExtendInfo.setContractDeliveryId(product.getDeliveryId());
                        productExtendInfoList.add(expressProductExtendInfo);

                        return productInfo;
                    }).collect(Collectors.toList());

                    String deliveryId = entry.getKey();
                    String deliveryNo = Optional.ofNullable(remoteCrmLogisticsService.getLogisticsBasicInfoById(deliveryId))
                            .map(JsonObject::getObjEntity)
                            .map(CrmLogisticsInfoVO::getDeliveryNo)
                            .orElse(null);

                    PerformanceReportDeliveryExportDTO.ExpressProductInfo expressWaybillNumber = new PerformanceReportDeliveryExportDTO.ExpressProductInfo();
                    expressWaybillNumber.setProductInfoList(productInfoList);
                    expressWaybillNumber.setExpressWaybillNumber(deliveryNo);
                    return expressWaybillNumber;
                }).collect(Collectors.toList());
                deliveryExportDto.setDeliveryInfoProductList(expressWaybillNumbers);
            }
            return deliveryExportDto;
        }).collect(Collectors.toList());

        List<Map<String, Object>> sheetList=new ArrayList<>();
        ExportParams exportParams=new ExportParams();
        exportParams.setType(ExcelType.XSSF);
        exportParams.setSheetName("发货信息");
        Map<String,Object> sheet1Map = new HashMap<>();
        sheet1Map.put("title",exportParams);
        sheet1Map.put("data", deliveryExportDtoList);
        sheet1Map.put("entity", PerformanceReportDeliveryExportDTO.class);
        sheetList.add(sheet1Map);

        if(CollectionUtils.isNotEmpty(productExtendInfoList)){
            exportParams=new ExportParams();
            exportParams.setType(ExcelType.XSSF);
            exportParams.setSheetName("导入使用");

            Map<String,Object> sheet2Map = new HashMap<>();
            sheet2Map.put("title",exportParams);
            sheet2Map.put("data", productExtendInfoList);
            sheet2Map.put("entity", PerformanceReportDeliveryExportDTO.ExpressProductExtendInfo.class);
            sheetList.add(sheet2Map);
        }

        try (Workbook workbook = ExcelExportUtil.exportExcel(sheetList,ExcelType.XSSF)){

            response.addHeader(HttpHeaders.CONTENT_DISPOSITION, WebFilenameUtils.disposition("发货信息.xlsx"));
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            workbook.write(response.getOutputStream());
        }catch (Exception e){
            logger.error("导出发货信息",e);
            throw new CrmException("导出失败", ResultEnum.FAIL.getResult(), e);
        }

    }

    @GetMapping("/getBorrowForForwardOrSpecialItemNum")
    @Operation(summary = "查询产品关联借试用或专项备货序列号数量")
    @PreFlowPermission
    public JsonObject<Integer> getBorrowForForwardOrSpecialItemNum(@RequestParam String performanceReportProductId) {
        PerformanceReportProductOwn performanceReportProductOwn = performanceReportProductOwnService.getById(performanceReportProductId);
        PreFlowPermissionAspect.checkProcessInstanceId(performanceReportProductOwn.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(performanceReportProductOwnService.selectBorrowForForwardOrSpecialItemNum(performanceReportProductId));
    }

    @GetMapping("/bindingDeviceSerialNumber")
    @Operation(summary = "校验设备序列号是否为真实存在的序列号")
    @PreFlowPermission
    public JsonObject<Boolean> bindingDeviceSerialNumber(@RequestParam String bindingDeviceSerialNumber){
        return new JsonObject<>(performanceReportService.bindingDeviceSerialNumber(bindingDeviceSerialNumber));
    }

    private List<PerformanceReportProductOwnDTO> buildProductTree(List<PerformanceReportProductOwnDTO> list) {
        if (list == null || list.isEmpty()) {
            return Collections.emptyList();
        }
        Map<String, PerformanceReportProductOwnDTO> map = new HashMap<>();
        List<PerformanceReportProductOwnDTO> roots = new ArrayList<>();

        // 将所有节点放入哈希表
        for (PerformanceReportProductOwnDTO dto : list) {
            map.put(dto.getProjectRecordId(), dto); // 假设存在 getId() 方法
        }

        // 遍历列表，构建树形结构
        for (PerformanceReportProductOwnDTO dto : list) {
            String parentId = dto.getParentId();
            if (parentId == null || "0".equals(parentId)) { // 根据实际情况判断根节点
                roots.add(dto);
            } else {
                PerformanceReportProductOwnDTO parent = map.get(parentId);
                if (parent != null) {
                    if (parent.getChildren() == null) {
                        parent.setChildren(new ArrayList<>());
                    }
                    parent.getChildren().add(dto); // 假设存在 getChildren() 和 setChildren() 方法
                }else {
                    roots.add(dto);
                }
            }
        }

        return roots;
    }

    @GetMapping(value="/exportPerformanceReportProductList")
    @Operation(summary = "导出产品清单")
    @PreFlowPermission
    public void exportPerformanceReportProductList() throws Exception {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        List<PerformanceReportProductListVO> performanceReportProductListVOS = performanceReportProductOwnService.exportPerformanceReportProductList(processInstanceId, null);
        ExcelUtil<PerformanceReportProductListVO> excelUtil = new ExcelUtil<>(PerformanceReportProductListVO.class);
        excelUtil.exportExcel(response, performanceReportProductListVOS,"产品清单");
    }

    @PostMapping("/importPerformanceReportProductList")
    @Operation(summary = "导入【产品清单】")
    @PreFlowPermission(hasAnyNodes = "sid-0024E5FC-BD42-4351-9D30-2F1A79F29FE3")
    public JsonObject<Boolean> importPerformanceReportProductList(MultipartFile file) throws Exception {
        if(file == null || file.isEmpty()){
            throw new CrmException("参数异常", ResultEnum.FAIL.getResult());
        }
        return new JsonObject<>(performanceReportProductOwnService.importPerformanceReportProductList(file));
    }

    @PostMapping("/saveDeliveryInfo")
    @Operation(summary = "添加发货详情信息")
    @PreFlowPermission(hasAnyNodes = {"sid-B961E1EB-8D21-4C72-A853-6D7E1BA27F8E","sid-0024E5FC-BD42-4351-9D30-2F1A79F29FE3"})
    public JsonObject<Boolean> saveDeliverySnInfo(@RequestBody PerformanceReportContractDeliveryDetailDTO performanceReportContractDeliveryDetailDTO){
        PerformanceReportContractDelivery performanceReportContractDelivery1 = performanceReportContractDelivery.getById(performanceReportContractDeliveryDetailDTO.getContractDeliveryId());
        PreFlowPermissionAspect.checkProcessInstanceId(performanceReportContractDelivery1.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(performanceReportProductOwnService.insertDeliverySnInfo(performanceReportContractDeliveryDetailDTO));
    }

    @GetMapping("/getDeliverySnInfo")
    @Operation(summary = "查询发货详情信息")
    @PreFlowPermission
    public JsonObject<List<LogisticsInfoVO>> getDeliverySnInfo(@RequestParam String contractDeliveryId){
        PerformanceReportContractDelivery performanceReportContractDelivery1 = performanceReportContractDelivery.getById(contractDeliveryId);
        PreFlowPermissionAspect.checkProcessInstanceId(performanceReportContractDelivery1.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        List<LogisticsInfoVO> logisticsInfoVOList = new ArrayList<>();
        List<DeliveryProductSnDTO> deliveryProductSnDTOS = performanceReportProductOwnService.selectDeliverySnInfo(contractDeliveryId);
        // 快递信息
        ListUtils.emptyIfNull(deliveryProductSnDTOS).forEach(deliveryProductSnDTO -> {
            BatchSubParam batchSubParam=new BatchSubParam();
            batchSubParam.setCompany(deliveryProductSnDTO.getCompanyName());
            batchSubParam.setKdybCom(deliveryProductSnDTO.getCompanyCode());
            batchSubParam.setNumber(deliveryProductSnDTO.getDeliveryNo());
            batchSubParam.setPhone("1111");
            List<LogisticsInfoDetail> logisticsInfoDetails =new ArrayList<>();
            logisticsInfoDetails.add(new LogisticsInfoDetail("3", deliveryProductSnDTO.getDeliveryNo(), "[洛阳市]您的快件已签收，如有疑问请电联快递员【徐好，电话：18336768057】，感谢您使用顺丰，期待再次为您服务。（主单总件数：1件）", "2024-08-17", "河南,洛阳市", "已签收", "洛阳市", "112.45404,34.619682", "luo yang shi", "3"));
            logisticsInfoDetails.add(new LogisticsInfoDetail("2", deliveryProductSnDTO.getDeliveryNo(), "[洛阳市]快件交给徐好，正在派送途中（联系电话：18336768057，顺丰已开启“安全呼叫”保护您的电话隐私,请放心接听！）（主单总件数：1件）", "2024-07-17", "河南,洛阳市", "派件中", "洛阳市", "112.45404,34.619682", "luo yang shi", "5"));
            logisticsInfoDetails.add(new LogisticsInfoDetail("1", deliveryProductSnDTO.getDeliveryNo(), "[洛阳市]快件已发车", "2024-06-17", "河南,洛阳市", "揽收", "洛阳市", "112.45404,34.619682", "luo yang shi", "103"));
            JsonObject<LogisticsInfo> info = new JsonObject<>();
            info.setResult(0);
            info.setObjEntity(new LogisticsInfo("1", "shutdown", "ZTO", "中通快递", "3", "1", "2023-11-11","北京市","洛阳市", logisticsInfoDetails, "1"));

            LogisticsInfoVO logisticsInfoVO=new LogisticsInfoVO();
            if (info.isSuccess()){
                logisticsInfoVO.setLogisticsInfo(info.getObjEntity());
            }
            logisticsInfoVO.setDeliveryProductSnDTO(deliveryProductSnDTO);
            logisticsInfoVOList.add(logisticsInfoVO);
        });
        return new JsonObject<>(logisticsInfoVOList);
    }

    @GetMapping("/queryProductDeliverySnInfo")
    @Operation(summary = "查询选中的产品发货序列号")
    @PreFlowPermission
    public JsonObject<List<ContractDeliveryProductSnVO>> queryProductDeliverySnInfo(@RequestParam String contractDeliveryId){
        PerformanceReportContractDelivery performanceReportContractDelivery1 = performanceReportContractDelivery.getById(contractDeliveryId);
        PreFlowPermissionAspect.checkProcessInstanceId(performanceReportContractDelivery1.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        return new JsonObject<>(performanceReportProductOwnService.selectProductDeliverySnInfo(contractDeliveryId));
    }

    @GetMapping("/deleteDeliverySnInfo")
    @Operation(summary = "删除发货信息")
    @PreFlowPermission(hasAnyNodes = {"sid-B961E1EB-8D21-4C72-A853-6D7E1BA27F8E","sid-0024E5FC-BD42-4351-9D30-2F1A79F29FE3"})
    public JsonObject<Boolean> deleteDeliverySnInfo(@RequestParam String contractDeliveryDetailId){
        PerformanceReportContractDeliveryDetail performanceReportContractDeliveryDetail = performanceReportContractDeliveryDetailMapper.selectById(contractDeliveryDetailId);
        PerformanceReportContractDelivery performanceReportContractDelivery1 = performanceReportContractDelivery.getById(performanceReportContractDeliveryDetail.getContractDeliveryId());
        PreFlowPermissionAspect.checkProcessInstanceId(performanceReportContractDelivery1.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        return new JsonObject<>(performanceReportProductOwnService.deleteDeliverySnInfo(contractDeliveryDetailId));
    }

    @PostMapping("/importProductDeliverySn")
    @Operation(summary = "导入发货信息序列号")
    @PreFlowPermission(hasAnyNodes = {"sid-B961E1EB-8D21-4C72-A853-6D7E1BA27F8E","sid-0024E5FC-BD42-4351-9D30-2F1A79F29FE3"})
    public JsonObject<List<ContractDeliveryProductSnVO>> importProductDeliverySn(String contractDeliveryId, MultipartFile file) throws Exception {
        PerformanceReportContractDelivery performanceReportContractDelivery1 = performanceReportContractDelivery.getById(contractDeliveryId);
        PreFlowPermissionAspect.checkProcessInstanceId(performanceReportContractDelivery1.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        if(file == null || file.isEmpty()){
            throw new CrmException("参数异常", ResultEnum.FAIL.getResult());
        }
        return new JsonObject<>(performanceReportProductOwnService.importProductDeliverySn(file, contractDeliveryId));
    }

    @GetMapping("/getSupplierNameByChannelCompanyId")
    @Operation(summary = "根据签约单位查询业绩上报供货单位")
    @PreAuthorize(hasAnyPermission = {"crm_flow_performance_report","crm_agent_flow_performance_report"})
    public JsonObject<List<CrmAgentAuthenticationVO>> getSupplierNameByChannelCompanyId(@RequestParam String channelCompanyId, @RequestParam String projectId){
        List<CrmAgentAuthenticationVO> agentAuthenticationList = performanceReportService.selectSupplierNameByChannelCompanyId(channelCompanyId, projectId);
        return new JsonObject<>(agentAuthenticationList);
    }

    @GetMapping("/getPerformanceReportAvailableAdvancePayment")
    @Operation(summary = "查询业绩上报剩余可用预付款")
    @PreAuthorize(hasAnyPermission = {"crm_flow_performance_report","crm_agent_flow_performance_report"})
    public JsonObject<BigDecimal> getPerformanceReportAvailableAdvancePayment(@RequestParam String channelCompanyName, @RequestParam String supplierName){
        BigDecimal availableAdvancePayment = performanceReportService.selectPerformanceReportAvailableAdvancePaymentInfo(channelCompanyName, supplierName);
        return new JsonObject<>(availableAdvancePayment);
    }

    @GetMapping("/getAvailableAdvancePaymentDetail")
    @Operation(summary = "查询可用预付款信息列表")
    @PreAuthorize(hasAnyPermission = {"crm_flow_performance_report","crm_agent_flow_performance_report"})
    public JsonObject<List<AgentPrePaymentVerificationVO>> getAvailableAdvancePaymentDetail(@RequestParam String channelCompanyId, @RequestParam String supplierId){
        List<AgentPrePaymentVerificationVO> availableAdvancePayment = performanceReportService.selectAvailableAdvancePaymentList(channelCompanyId, supplierId);
        return new JsonObject<>(availableAdvancePayment);
    }

    @GetMapping("/checkBeforeFillingJsonObj")
    @Operation(summary = "流程填写之前的校验")
    @PreAuthorize(rightHandler = ProjectRightHandler.class)
    public JsonObject<Void> checkBeforeFillingJsonObj(@RequestParam String projectId){
        return performanceReportProcessService.checkBeforeFillingJsonObj(projectId);
    }

    @GetMapping("/download/productDeliverySn")
    @Operation(summary = "发货序列号模板")
    @PreFlowPermission
    public void downloadProductDeliverySn(HttpServletResponse response) throws IOException {
        downloadAgentFile(response, "发货序列号模板.xlsx");
    }
    @GetMapping("/getProcessInfoByPerformanceReportId")
    @Operation(summary = "缺货订单列表")
    @PreFlowPermission
    public JsonObject<List<ContractReviewMainBaseInfoDTO>> getProcessInfoByPerformanceReportId(@RequestParam Integer processSource, @RequestParam String performanceReportId,
                                                                                               @RequestParam(required = false) String contractProcessNumber){
        PerformanceReport byId = performanceReportService.getById(performanceReportId);
        String headerValue = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        if (processSource == 1){
            PreFlowPermissionAspect.checkProcessInstanceId(byId.getProcessInstanceId(), headerValue);
        }else if(processSource == 2) {
            PerformanceReportReturnExchange one = performanceReportReturnExchangeService.getOne(new LambdaQueryWrapper<PerformanceReportReturnExchange>().eq(PerformanceReportReturnExchange::getProcessInstanceId, headerValue));
            if(one != null) {
                PreFlowPermissionAspect.checkProcessInstanceId(byId.getProcessInstanceId(), one.getParentProcessInstanceId());
            }
        }
        String currentAgentId = UserInfoHolder.getCurrentAgentId();
        if (StringUtils.isNotEmpty(currentAgentId)){
            if(byId.getSupplierId().equals(currentAgentId)) {
                return new JsonObject<>(contractReviewFlowService.getProcessInfoByPerformanceReportId(performanceReportId, contractProcessNumber));
            }
        }
        return new JsonObject<>();
    }

    public void downloadAgentFile(HttpServletResponse response, @RequestParam String fileName) throws IOException {
        // 构建资源路径
        String resourcePath = "classpath:word/" + fileName;
        // 加载资源
        Resource resource = resourceLoader.getResource(resourcePath);
        if (!resource.exists()) {
            response.sendError(HttpServletResponse.SC_NOT_FOUND, "File not found");
            return;
        }

        String mimeType = getMimeType(fileName);
        // 设置响应头
        response.setContentType(mimeType);
        // 对文件名进行 URL 编码
        String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
        response.setHeader("Content-disposition", "attachment;filename=" + encodedFileName);
        // 读取文件内容并写入响应输出流
        try (InputStream inputStream = new BufferedInputStream(resource.getInputStream());
             OutputStream outputStream = response.getOutputStream()) {

            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
        }
    }

    private String getMimeType(String fileName) {
        String type = "application/octet-stream";
        String fileExtension = "";
        int dotIndex = fileName.lastIndexOf('.');
        if (dotIndex != -1) {
            fileExtension = fileName.substring(dotIndex + 1).toLowerCase();

            switch (fileExtension) {
                case "doc":
                    type = "application/msword";
                    break;
                case "docx":
                    type = "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
                    break;
                case "xlsx":
                    type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                    break;
            }
        }
        return type;
    }

    @PostMapping("/00edit")
    @Operation(summary = "00步编辑流程")
    @PreAuthorize(rightHandler = Step00RemoveEditProcessRightHandler.class)
    public JsonObject<Boolean> editProcess00(@RequestBody PerformanceReportFlowLaunchDTO priceReviewFlowLaunchDTO){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        priceReviewFlowLaunchDTO.setProcessInstanceId(processInstanceId);
        performanceReportProcessService.edit00(priceReviewFlowLaunchDTO);
        return new JsonObject<>(true);
    }

}
