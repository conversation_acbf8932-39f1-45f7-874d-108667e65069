package com.topsec.crm.flow.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.topsec.crm.flow.api.dto.loan.loanVerification.LoanVerificationDTO;
import com.topsec.crm.flow.core.entity.LoanVerification;
import com.topsec.crm.flow.core.mapper.LoanVerificationMapper;
import com.topsec.crm.flow.core.service.LoanVerificationService;
import com.topsec.crm.framework.common.enums.LoanEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.tos.common.HyperBeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class LoanVerificationServiceImpl extends ServiceImpl<LoanVerificationMapper, LoanVerification> implements LoanVerificationService {
    @Override
    public List<LoanVerificationDTO> getListByLoanProcessId(String loanProcessId) {
        List<LoanVerification> loanVerifications = baseMapper.selectList(new LambdaQueryWrapper<LoanVerification>()
                .eq(LoanVerification::getDelFlag, false)
                .eq(LoanVerification::getProcessInstanceId, loanProcessId)
        );
        return HyperBeanUtils.copyListProperties(loanVerifications, LoanVerificationDTO::new);
    }

    @Override
    public Boolean saveVerification(LoanVerificationDTO loanVerification) {
        return save(HyperBeanUtils.copyProperties(loanVerification, LoanVerification::new));
    }

    @Override
    public Boolean updateVerification(LoanVerificationDTO loanVerification) {
        return updateById(HyperBeanUtils.copyProperties(loanVerification, LoanVerification::new));
    }

    @Override
    public Boolean deleteVerification(String verificationId) {
        LoanVerification byId = getById(verificationId);
        if (byId != null){
            byId.setDelFlag(true);
            return updateById(byId);
        }else {
            throw new CrmException("删除失败，该数据不存在");
        }
    }

    @Override
    public Boolean handleToCostData(String processInstanceId, BigDecimal toCostAmount) {

        if (processInstanceId == null || toCostAmount == null) {
            throw new CrmException("processInstanceId 和 toCostAmount 不能为 null");
        }

        LambdaQueryWrapper<LoanVerification> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LoanVerification::getProcessInstanceId, processInstanceId)
                .eq(LoanVerification::getDelFlag, false);

        LoanVerification one = getOne(queryWrapper);
        if (one == null) {
            throw new CrmException("未找到对应的借款记录，processInstanceId: " + processInstanceId);
        }

        LoanVerificationDTO loanVerificationDTO = new LoanVerificationDTO();
        loanVerificationDTO.setId(one.getId());
        loanVerificationDTO.setProcessInstanceId(processInstanceId);
        loanVerificationDTO.setVerificationAmount(toCostAmount);
        loanVerificationDTO.setVerificationType(LoanEnum.LoanVerificationTypeEnum.BID_TRANSFER.getCode());

        return save(HyperBeanUtils.copyProperties(loanVerificationDTO, LoanVerification::new));
    }

    @Override
    public Map<String, BigDecimal> getAmountByProcessInstanceIds(List<String> processInstanceIdList) {

        if (processInstanceIdList == null || processInstanceIdList.isEmpty()) {
            return Collections.emptyMap();
        }

        // 根据流程实例ID列表获取核销金额
        List<LoanVerification> loanVerifications = baseMapper.selectList(
                new LambdaQueryWrapper<LoanVerification>()
                        .eq(LoanVerification::getDelFlag, false)
                        .in(LoanVerification::getProcessInstanceId, processInstanceIdList)
        );

        // 按流程实例ID分组并累加核销金额
        return loanVerifications.stream()
                .filter(item -> item.getProcessInstanceId() != null)
                .collect(Collectors.groupingBy(
                        LoanVerification::getProcessInstanceId,
                        Collectors.mapping(
                                item -> item.getVerificationAmount() == null ? BigDecimal.ZERO : item.getVerificationAmount(),
                                Collectors.reducing(BigDecimal.ZERO, BigDecimal::add)
                        )
                ));
    }

}
