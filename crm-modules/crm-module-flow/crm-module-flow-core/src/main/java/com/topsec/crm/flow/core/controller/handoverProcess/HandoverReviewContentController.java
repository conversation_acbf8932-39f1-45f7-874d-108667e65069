package com.topsec.crm.flow.core.controller.handoverProcess;

import com.topsec.crm.flow.api.vo.handoverProcess.HandoverReviewContentVo;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.entity.HandoverReviewContent;
import com.topsec.crm.flow.core.service.HandoverReviewConfigService;
import com.topsec.crm.flow.core.service.HandoverReviewContentService;
import com.topsec.crm.framework.common.name.NameUtils;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 评审内容交接信息表
 *
 * <AUTHOR>
 * @email
 * @date 2025-07-22 14:30:20
 */

@RestController
@RequestMapping("/handoverReviewContent")
@Tag(name = "评审内容交接Controller", description = "/handoverReviewContent")
@RequiredArgsConstructor
@Validated
public class HandoverReviewContentController extends BaseController {

    private final HandoverReviewContentService handoverReviewContentService;
    private final HandoverReviewConfigService handoverReviewConfigService;


    /**
     * 根据流程实例id查询评审内容列表
     */
    @GetMapping("/findHandoverContentList")
    @Operation(summary = "查询评审内容列表")
    @PreFlowPermission
    public JsonObject<List<HandoverReviewContentVo>> findHandoverContentList(@RequestParam String processInstanceId) {
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        List<HandoverReviewContentVo> handoverReviewContentVos = HyperBeanUtils.copyListPropertiesByJackson(handoverReviewContentService.findHandoverContentList(processInstanceId), HandoverReviewContentVo.class);
        NameUtils.setName(handoverReviewContentVos, "personId", "personName");
        return new JsonObject<>(handoverReviewContentVos);
    }



    /**
     * 根据流程实例id和节点id和人员id查询评审内容
     */
    @PostMapping("/findHandoverContent")
    @Operation(summary = "查询评审内容")
    @PreFlowPermission
    public JsonObject<HandoverReviewContentVo> findHandoverContent(@RequestParam String processInstanceId, @RequestParam String activityId) {
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        CrmAssert.hasText(activityId, "节点id不能为空");
        HandoverReviewContent handoverReviewContent = handoverReviewContentService.findHandoverContent(processInstanceId, activityId, getCurrentPersonId());
        HandoverReviewContentVo handoverReviewContentVo = HyperBeanUtils.copyPropertiesByJackson(handoverReviewContent, HandoverReviewContentVo.class);
        if (handoverReviewContent != null && StringUtils.isNotEmpty(handoverReviewContent.getPersonId())) {

            NameUtils.setName(handoverReviewContentVo, "personId", "personName");
        }

        return new JsonObject<>(handoverReviewContentVo== null?new HandoverReviewContentVo():handoverReviewContentVo);
    }

    /**
     * 新增或修改评审内容
     */
    @PostMapping("/saveOrUpdateHandoverContent")
    @Operation(summary = "修改评审内容")
    @PreFlowPermission
    public JsonObject<Boolean> saveOrUpdateHandoverContent(@RequestBody HandoverReviewContentVo handoverReviewContentVo) {
        CrmAssert.hasText(handoverReviewContentVo.getId(), "id不能为空");
        CrmAssert.hasText(handoverReviewContentVo.getProcessInstanceId(), "流程实例id不能为空");
        CrmAssert.hasText(handoverReviewContentVo.getActivityId(), "节点id不能为空");
        handoverReviewContentVo.setPersonId(UserInfoHolder.getCurrentPersonId());
        handoverReviewContentVo.setDeptId(getDepartmentId());
        HandoverReviewContent handoverReviewContent = HyperBeanUtils.copyPropertiesByJackson(handoverReviewContentVo, HandoverReviewContent.class);
        return new JsonObject<>(handoverReviewContentService.updateHandoverContent(handoverReviewContent));
    }

    @GetMapping("/getContentByNodeId")
    @Operation(summary = "根据节点id获取内容")
    @PreFlowPermission
    public JsonObject<String> getContentByNodeId(@RequestParam String nodeId) {
        String contentByNodeId = handoverReviewConfigService.getContentByNodeId(nodeId);
        return new JsonObject<>(contentByNodeId);
    }



}
