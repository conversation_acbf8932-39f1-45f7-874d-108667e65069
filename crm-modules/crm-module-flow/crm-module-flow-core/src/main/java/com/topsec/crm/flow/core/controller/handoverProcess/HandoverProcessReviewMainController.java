package com.topsec.crm.flow.core.controller.handoverProcess;

import com.topsec.crm.flow.api.vo.ProcessAttachmentVo;
import com.topsec.crm.flow.api.vo.handoverProcess.CostAllocationVo;
import com.topsec.crm.flow.api.vo.handoverProcess.HandoverInfoVo;
import com.topsec.crm.flow.api.vo.handoverProcess.HandoverProcessReviewMainVo;
import com.topsec.crm.flow.api.vo.handoverProcess.HandoverUpdateVo;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.HandoverProcessReviewMain;
import com.topsec.crm.flow.core.service.HandoverProcessReviewMainService;
import com.topsec.crm.flow.core.service.ThreeProcurementPaymentReviewMainService;
import com.topsec.crm.framework.common.bean.HandoverProcessQuery;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.project.api.client.RemoteBorrowForProbationClient;
import com.topsec.crm.project.api.dto.BorrowForProbationDevicePageQuery;
import com.topsec.crm.project.api.entity.CrmBorrowForProbationDeviceVO;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsNodeClient;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.vo.node.ApproveNode;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;


/**
 * 
 *
 * <AUTHOR>
 * @email 
 * @date 2025-07-21 15:49:31
 */
@RestController
@RequestMapping("/handoverProcess")
@Tag(name = "交接流程Controller", description = "/handoverProcess")
@RequiredArgsConstructor
@Validated
public class HandoverProcessReviewMainController extends BaseController {


    private final HandoverProcessReviewMainService handoverProcessReviewMainService;

    private final ThreeProcurementPaymentReviewMainService threeProcurementPaymentReviewMainService;

    private final TfsNodeClient tfsNodeClient;

    private final RemoteBorrowForProbationClient remoteBorrowForProbationClient;


    /**
     * 查询人员交接基本信息
     */
    @GetMapping("/getHandoverProcessInfo")
    @Operation(summary = "查询人员交接基本信息")
    @PreFlowPermission
    public HandoverProcessReviewMainVo getHandoverProcessInfo(@RequestParam String processInstanceId) {
        CrmAssert.hasText(processInstanceId, "流程实例ID不能为空");
        HandoverProcessReviewMain handoverProcessReviewMain = handoverProcessReviewMainService.getHandoverProcessInfoByProcessInstanceId(processInstanceId);
        return HyperBeanUtils.copyPropertiesByJackson(handoverProcessReviewMain, HandoverProcessReviewMainVo.class);
    }

    /**
     * 修改人员交接基本信息
     */
    @PostMapping("/updateHandoverProcessInfo")
    @Operation(summary = "修改人员交接基本信息")
    @PreFlowPermission
    public Boolean updateHandoverProcessInfo(@RequestBody HandoverProcessReviewMainVo handoverProcessReviewMainVo) {
        HandoverProcessReviewMain handoverProcessReviewMain = HyperBeanUtils.copyPropertiesByJackson(handoverProcessReviewMainVo, HandoverProcessReviewMain.class);
        return handoverProcessReviewMainService.updateHandoverProcessInfo(handoverProcessReviewMain);
    }


    @PostMapping("/{type}/handoverGenericListPage")
    @Operation(summary = "待选泽-通用分页查询方法-需要传流程实例id")
    @PreFlowPermission
    public JsonObject<PageUtils<?>> handoverGenericListPage(
            @RequestBody HandoverProcessQuery query,
            @PathVariable String type) {
        setParams(query, type);
        if ("salesAgreement".equals(type)){
            query.setDeptId(getDepartmentId());
        }
        return handoverProcessReviewMainService.getListPage(query, type);
    }



    @PostMapping("/{type}/handoverGenericPage")
    @Operation(summary = "选择后-通用分页查询方法-需要传流程实例id")
    @PreFlowPermission
    public JsonObject<PageUtils<?>> handoverGenericPage(
            @RequestBody HandoverProcessQuery query,
            @PathVariable String type) {
           setParams(query, type);
          return handoverProcessReviewMainService.getPage(query, type);
    }


    @PostMapping("/handoverGenericInfoPage")
    @Operation(summary = "详情页汇总分页集合-通用分页查询方法")
    @PreFlowPermission
    public JsonObject<List<HandoverInfoVo>> handoverGenericInfoPage(@RequestParam String processInstanceId,  @RequestParam(required = false) String taskId) {
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        HandoverProcessQuery query = new HandoverProcessQuery();
        query.setTaskId(taskId);
        query.setProcessInstanceId(processInstanceId);
        setParams(query, "all");
        return handoverProcessReviewMainService.getInfoPage(query);
    }



    /**
     * 添加项目交接信息
     */


    @PostMapping("/{type}/handoverGenericAdd")
    @Operation(summary = "通用新增方法")
    @PreFlowPermission
    public JsonObject<Boolean> handoverGenericAdd(
            @RequestBody Object vo,
            @PathVariable String type) {
        // 类型检查和转换
        if (!(vo instanceof List)) {
            throw new IllegalArgumentException("参数必须是列表类型");
        }

        List<?> voList = (List<?>) vo;
        return handoverProcessReviewMainService.genericAddHandover(voList, type);
    }

    @PostMapping("/{type}/handoverGenericUpdate")
    @Operation(summary = "通用更新方法")
    @PreFlowPermission
    public JsonObject<Boolean> handoverGenericUpdate(
            @Valid @RequestBody HandoverUpdateVo vo,
            @PathVariable String type) {
        if (vo == null){
            throw  new CrmException("参数不能为空");
        }
        CrmAssert.hasText(vo.getId(), "交接记录ID不能为空");
        CrmAssert.hasText(vo.getReceiverId(), "接收人id不能为空");
        CrmAssert.hasText(vo.getReceiverName() ,"接收人姓名不能为空");
        return handoverProcessReviewMainService.genericUpdateHandover(vo, type);
    }



    /**
     * 通用删除方法
     */
    @PostMapping("/{type}/delete")
    @Operation(summary = "通用删除方法")
    @PreFlowPermission
    public JsonObject<Boolean> genericDelete(
            @RequestBody String[] ids,
            @PathVariable String type) {
        return handoverProcessReviewMainService.genericDelete(ids, type);
    }



    @PostMapping("/selectAttachmentList")
    @Operation(summary = "查询交接附件列表")
    @PreFlowPermission
    public JsonObject<List<ProcessAttachmentVo>> selectAttachmentList(@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        HandoverProcessReviewMain handoverProcessReviewMain = handoverProcessReviewMainService.getHandoverProcessInfoByProcessInstanceId(processInstanceId);
        List<ProcessAttachmentVo> listThreeProcurementAttachment = threeProcurementPaymentReviewMainService.getListThreeProcurementAttachment(handoverProcessReviewMain.getAttachmentIds());
        return new JsonObject<>(listThreeProcurementAttachment);
    }



    @PostMapping("/upload")
    @Operation(summary = "01交接人确认-上传合同交接明细")
    @PreFlowPermission(hasAnyNodes = {"sid-BBBED0D5-A82C-4652-A631-D47A3C2A26D0"})
    public JsonObject<Boolean> upload(@RequestBody HandoverProcessReviewMainVo handoverProcessReviewMainVo){
        HandoverProcessReviewMain handoverProcessReviewMain = HyperBeanUtils.copyPropertiesByJackson(handoverProcessReviewMainVo, HandoverProcessReviewMain.class);
        return new JsonObject<>(handoverProcessReviewMainService.uploadHandoverProcessAttachment(handoverProcessReviewMain));
    }


    /**
     * 新增成本划分
     */
    @PostMapping("/addCostDivision")
    @Operation(summary = "新增成本划分")
    @PreFlowPermission
    public JsonObject<Boolean> addCostDivision(@Valid @RequestBody CostAllocationVo costAllocationVo) {
        String processInstanceId = costAllocationVo.getProcessInstanceId();
        HandoverProcessReviewMain handoverProcessReviewMain = handoverProcessReviewMainService.getHandoverProcessInfoByProcessInstanceId(processInstanceId);
        Integer handoverType = handoverProcessReviewMain.getHandoverType();
        if (handoverType != 0){
            throw new CrmException("调动流程才有成本划分");
        }
        handoverProcessReviewMain.getTransferDetail().setCostAllocationVo(costAllocationVo);
        return new JsonObject<>(handoverProcessReviewMainService.updateHandoverProcessInfo(handoverProcessReviewMain));
    }


    /**
     * 清空成本信息
     */
    @PostMapping("/clearCostDivision")
    @Operation(summary = "清空成本信息")
    @PreFlowPermission
    public JsonObject<Boolean> clearCostDivision(@RequestParam String processInstanceId) {
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        HandoverProcessReviewMain handoverProcessReviewMain = handoverProcessReviewMainService.getHandoverProcessInfoByProcessInstanceId(processInstanceId);
        Integer handoverType = handoverProcessReviewMain.getHandoverType();
        if (handoverType != 0){
            throw new CrmException("调动流程才有成本划分");
        }
        handoverProcessReviewMain.getTransferDetail().setCostAllocationVo(null);
        return new JsonObject<>(handoverProcessReviewMainService.updateHandoverProcessInfo(handoverProcessReviewMain));
    }


    /**
     * 查询成本划分
     */
    @GetMapping("/getCostDivision")
    @Operation(summary = "查询成本划分")
    @PreFlowPermission
    public JsonObject<CostAllocationVo> getCostDivision(@RequestParam String processInstanceId) {
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        return new JsonObject<>(handoverProcessReviewMainService.queryCostDivision(processInstanceId));
    }




    @PostMapping("/{type}/batchApproveOrReject")
    @Operation(summary = "接收人合同确认：批量processInstanceId必传，id不传，单个不传processInstanceId，只传id")
    @PreFlowPermission
    public JsonObject<Boolean> batchApproveOrReject(@Valid @RequestBody HandoverUpdateVo vo,@PathVariable String type) {
        CrmAssert.hasText(type, "类型不能为不能为空");
        return new JsonObject<>(handoverProcessReviewMainService.batchApproveOrReject(vo, type));
    }



    private void setParams(HandoverProcessQuery query, String type) {
        CrmAssert.hasText(type, "业务类型不能为空");
        CrmAssert.hasText(query.getProcessInstanceId(), "流程实例id不能为空");
        String currentPersonId = UserInfoHolder.getCurrentPersonId();
        String taskId = query.getTaskId();
        if (StringUtils.isNotEmpty(taskId)) {
            ApproveNode approveNode = Optional.ofNullable(tfsNodeClient.showCurrentNode(taskId)).map(JsonObject::getObjEntity).orElse(new ApproveNode());
            String nodeId = approveNode.getNodeId();
            //01和03
            if ("sid-BBBED0D5-A82C-4652-A631-D47A3C2A26D0".equals(nodeId)){
                query.setPersonId(currentPersonId);
            }else if ("sid-BF43B4C9-4A92-4295-889E-19E7C9B7E9C7".equals(nodeId)) {
                HandoverProcessReviewMain handoverProcessReviewMain = handoverProcessReviewMainService.getHandoverProcessInfoByProcessInstanceId(query.getProcessInstanceId());
                if (handoverProcessReviewMain == null) {
                    throw new CrmException("流程实例不存在");
                }
                query.setPersonId(handoverProcessReviewMain.getPersonId());

            }else if ("personnelHandoverProcess_03".equals(nodeId)){
                query.setReceiverId(currentPersonId);
            }else {
                query.setPersonId(currentPersonId);
            }
        }else {
            query.setPersonId(currentPersonId);
        }

    }


    /**
     * 查询交接人名下的设备
     */
    @PostMapping("/queryHandoverPersonDevice")
    @Operation(summary = "查询交接人名下的设备")
    @PreFlowPermission
    public JsonObject<PageUtils<CrmBorrowForProbationDeviceVO>> queryHandoverPersonDevice(@RequestBody BorrowForProbationDevicePageQuery borrowForProbationDevicePageQuery) {
        CrmAssert.hasText(borrowForProbationDevicePageQuery.getProcessNumber(), "审批单号不能为空");
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        HandoverProcessReviewMain handoverProcessReviewMain = handoverProcessReviewMainService.getHandoverProcessInfoByProcessInstanceId(processInstanceId);
        if (handoverProcessReviewMain == null) {
            throw new CrmException("交接流程不存在");
        }

        borrowForProbationDevicePageQuery.setPersonId(handoverProcessReviewMain.getPersonId());
       return remoteBorrowForProbationClient.deptBorrowForProbationDevicePage(borrowForProbationDevicePageQuery);


    }



}
