package com.topsec.crm.flow.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.topsec.crm.framework.common.bean.FlowPerson;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@TableName(value ="loan_apply", autoResultMap = true)
@Data
public class LoanApply extends FlowBaseEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 上级流程实例ID
     */
    private String parentProcessInstanceId;

    /**
     * 上级流程单号
     */
    private String parentProcessNumber;

    /**
     * 流程分类
     */
    private String processDefinitionKey;

    /**
     * 借款人ID
     */
    private String loanUser;

    /**
     * 核算部门ID（三级部门）
     */
    private String checkDeptId;

    /**
     *签订公司ID
     */
    private String signCompanyId;

    /**
     * 借款分类 1-投标保证金 2-保函 3-履约保证金 4-标书费 5-中标服务费 6-采购借款 7-费用
     */
    private Integer loanType;

    /**
     * 借款金额
     */
    private BigDecimal loanAmount;

    /**
     * 付款方式 1-支票 2-现金 3-电汇 4-其他
     */
    private Integer paymentType;

    /**
     * 付款对象类型 1-个人 2-招标公司 3-用户
     */
    private Integer payerType;

    /**
     * 付款对象名称
     */
    private String payerName;

    /**
     * 联系人
     */
    private String linkman;

    /**
     * 联系电话
     */
    private String linkPhone;

    /**
     * 开户银行名称
     */
    private String bankName;

    /**
     * 银行账号
     */
    private String bankAccount;

    /**
     * 预计归还日期
     */
    private LocalDateTime repaymentDueDate;

    /**
     * 付款日期
     */
    private LocalDateTime repaymentDate;

    /**
     * 业务编号
     */
    private String businessNo;

    /**
     * 业务ID
     */
    private String businessId;


    /**
     * 标的金额
     */
    private BigDecimal subjectAmount;

    /**
     * 比例，借款利率
     */
    private BigDecimal loanRate;

    /**
     * 申请原因
     */
    private String applyReason;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 是否是借款承诺 0-否 1-是
     */
    private Boolean isLoanPromise;

    /**
     * 转费用金额
     */
    private BigDecimal toCostAmount;

    /**
     * 转费用类型 3-履约保证金 5-中标服务费
     */
    private Integer toCostType;

    /**
     * 出纳ID
     */
    private String loanTellerPersonId;

    /**
     * 删除标志 0-正常 1-删除
     */
    private Boolean delFlag;

}
