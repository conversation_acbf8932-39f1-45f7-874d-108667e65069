package com.topsec.crm.flow.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 *
 * @TableName handover_review_config
 */
@TableName(value ="handover_review_config")
@Data
public class HandoverReviewConfig {
    /**
     *
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 节点id
     */
    private String nodeId;

    /**
     * 评审内容
     */
    private String content;

    /**
     * 离职需要
     */
    private Integer quitRequired;

    /**
     * 调动需要
     */
    private Integer transferRequired;
}
