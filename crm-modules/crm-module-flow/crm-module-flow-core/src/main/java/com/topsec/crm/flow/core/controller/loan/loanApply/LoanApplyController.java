package com.topsec.crm.flow.core.controller.loan.loanApply;

import com.topsec.crm.flow.api.dto.loan.loanApply.*;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.process.impl.LoanApplyProcessService;
import com.topsec.crm.flow.core.service.LoanApplyService;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/loanApply")
@Tag(name = "【借款流程-流程】", description = "loanApply")
@RequiredArgsConstructor
@Validated
public class LoanApplyController extends BaseController {

    private final LoanApplyService loanApplyService;
    private final LoanApplyProcessService processService;

    @PostMapping("/launch")
    @Operation(summary = "发起借款流程")
    @PreAuthorize(hasPermission = "crm_loan")
    public JsonObject<Boolean> launch(@RequestBody LoanApplyLaunchDTO req){
        return new JsonObject<>(processService.launch(req));
    }

    @PostMapping("/uploadLoanProofAttachments")
    @Operation(summary = "上传借款凭证附件-07步",parameters = {
            @Parameter(name = "processInstanceId",description = "流程实例ID",required = true),
            @Parameter(name = "loanApplyInfo.loanProofAttachments",description = "借款单",required = true)
    })
    @PreFlowPermission(hasAnyNodes = {"sid-C034680A-CF9E-4E40-8F58-1860C0BF5B0D"})
    public JsonObject<Boolean> uploadLoanProofAttachments(@RequestBody LoanApplyLaunchDTO  req){
        return new JsonObject<>(
                loanApplyService.uploadLoanProofAttachments(
                        req.getProcessInstanceId(),
                        req.getLoanApplyInfo().getLoanProofFiles(),
                        req.getLoanApplyInfo().getLoanTeller().getPersonId()
                )
        );
    }

    @PostMapping("/fillRepaymentDate")
    @Operation(summary = "填写付款日期-10步")
    @PreFlowPermission(hasAnyNodes = {"sid-89CDE477-1081-41EB-A680-C5C30EE34E7D"})
    public JsonObject<Boolean> fillRepaymentDate(@RequestBody LoanApplyLaunchDTO req){
        return new JsonObject<>(loanApplyService.fillRepaymentDate(
                req.getProcessInstanceId(),
                req.getLoanApplyInfo().getRepaymentDate(),
                req.getLoanApplyInfo().getLoanProofFiles()
        ));
    }

    @GetMapping("/queryByProcessInstanceId")
    @Operation(summary = "查询流程实例信息",parameters = {
            @Parameter(name = "processInstanceId",description = "流程实例ID",required = true)
    })
    @PreFlowPermission
    public JsonObject<LoanApplyDTO> queryByProcessInstanceId(@RequestParam String processInstanceId){
        return new JsonObject<>(loanApplyService.queryByProcessInstanceId(processInstanceId));
    }


    @PostMapping("/projectSelectPage")
    @Operation(summary = "关联项目分页查询",parameters = {
            @Parameter(name = "param",description = "查询参数")
    })
    @PreAuthorize(hasPermission = "crm_loan")
    public JsonObject<PageUtils<LoanProjectPageInfo>> projectSelectPage(@RequestBody LoanProjectPageInfo req){
        PageUtils<LoanProjectPageInfo> loanProjectPageInfoPageUtils = loanApplyService.projectSelectPage(req);
        return new JsonObject<>(loanProjectPageInfoPageUtils);
    }

    @GetMapping("/purchaseSelectPage")
    @Operation(summary = "关联采购分页查询",parameters = {
            @Parameter(name = "param",description = "查询参数")
    })
    @PreAuthorize(hasPermission = "crm_loan")
    public JsonObject<PageUtils<LoanPurchasePageInfo>> purchaseSelectPage(@RequestParam(required = false) String param){
        startPage();
        return new JsonObject<>(loanApplyService.purchaseSelectPage(param));
    }

    @GetMapping("/loanTellerSelectList")
    @Operation(summary = "查询出纳人员列表")
    @PreFlowPermission(hasAnyNodes = {"sid-C034680A-CF9E-4E40-8F58-1860C0BF5B0D"})
    public JsonObject<List<LoanTellerVO>> loanTellerSelectList(){
        return new JsonObject<>(loanApplyService.loanTellerSelectList());
    }

}
