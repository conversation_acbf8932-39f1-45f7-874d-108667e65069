package com.topsec.crm.flow.core.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.topsec.crm.agent.api.entity.CrmAgentAuthenticationVO;
import com.topsec.crm.framework.common.bean.AgentPrePaymentVerificationVO;
import com.topsec.crm.flow.api.dto.performancereport.*;
import com.topsec.crm.flow.core.entity.PerformanceReport;
import com.topsec.crm.framework.common.bean.FlowPerson;
import com.topsec.crm.framework.common.web.page.TableDataInfo;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface PerformanceReportService extends IService<PerformanceReport> {
    List<PerformanceReportProductOwnDTO> getPerformanceReportProductByProcessInstanceId(String processInstanceId);



    PerformanceReportDetailInfo getPerformanceReportDetailsByProcessInstanceId(String processInstanceId);

    boolean updatePerformanceReportProductAcceptOrder(PerformanceReportProcessVO performanceReportVO);

    PerformanceReport selectPerformanceReportSupplierName(String supplierName);

    boolean savePerformanceReportProductInfo(PerformanceReportVO performanceReportVO);

    Map<String, Object> selectCompletionConditions(PerformanceReportProductQuery performanceReportProductQuery);

    /**
     * 查询退换货流程发货信息
     * @param returnProcessInstanceId returnProcessInstanceId
     * @return 查询退换货流程发货信息
     */
    List<PerformanceReportContractDeliveryDTO> getReturnContractDeliveryVOList(String returnProcessInstanceId);

    /**
     * 查询流程发货信息
     * @param processInstanceId 流程实例id
     * @return 查询流程发货信息
     */
    List<PerformanceReportContractDeliveryDTO> getContractDeliveryVOList(String processInstanceId);
    /**
     * 查询选中的产品发货信息
     * @param processInstanceId 流程实例id
     * @param recordIdSet 选择的产品recordId
     * @return 发货信息
     */
    List<PerformanceReportContractDeliveryDTO> querySelectedProductDeliveryInfo(String processInstanceId, Set<String> recordIdSet);

    ScarceGoodsOrderDTO getCustomerInfoByprocessInstanceId(String processInstanceId);

    TableDataInfo productInfoByProcessInstanceId(String processInstanceId);



    Page<PerformanceReport> pagePerformanceReport(PerformanceReportQuery query);

    List<PsnVO> exportPerformanceReportProductSn(String processInstanceId);

    Integer getOrderStatus(String processInstanceId);

    /**
     * 缺货下单产品绑定设备序列号
     * @param serialNumber
     * @return
     */
    Boolean bindingDeviceSerialNumber(String serialNumber);

    PerformanceReportProductStatisticsDTO getPerformanceReportProductStatistics(String processInstanceId);

    List<FlowPerson> selectDynastyApprover(String processInstanceId);

    List<FlowPerson> selectAgentApprover(String supplierId);

    List<CrmAgentAuthenticationVO> selectSupplierNameByChannelCompanyId(String channelCompanyId, String projectId);

    BigDecimal selectPerformanceReportAvailableAdvancePayment(String channelCompanyId, String supplierId);

    List<String> selectCrmProjectProductOwnByProjectId(String projectId);

    Map<String, String> selectPerformanceReportProcessNumberByIds(List<String> ids);

    PerformanceReportSelloutVO selectPerformanceReportSelloutVO(Integer lockType, String processNumber);

    Map<String, Object> backConditions(PerformanceReportProductQuery performanceReportProductQuery);

    Boolean updateSigningContractNumber(PerformanceReportVO performanceReportVO);

    Map<String, Object> deleteConditions(PerformanceReportProductQuery performanceReportProductQuery);

    Boolean snInPerformanceReport(String sn);

    List<String> queryPerformanceReportBySaleId(String saleId);

    BigDecimal selectPerformanceReportAvailableAdvancePaymentInfo(String channelCompanyName, String supplierName);

    List<AgentPrePaymentVerificationVO> selectAvailableAdvancePaymentList(String channelCompanyId, String supplierId);
}
