package com.topsec.crm.flow.core.service.impl;

import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.pagehelper.PageInfo;
import com.topsec.crm.contract.api.RemoteContractExecuteService;
import com.topsec.crm.contract.api.entity.contractexecute.CrmContractExecuteVO;
import com.topsec.crm.flow.api.dto.collectionhandle.CollectionLettersDTO;
import com.topsec.crm.flow.api.dto.collectionhandle.CollectionLettersHandleDTO;
import com.topsec.crm.flow.api.dto.collectionhandle.CollectionLettersQuery;
import com.topsec.crm.flow.api.vo.handoverProcess.HandoverContractVo;
import com.topsec.crm.flow.core.entity.ContractCollectionLetterMain;
import com.topsec.crm.flow.core.entity.ContractReviewMain;
import com.topsec.crm.flow.core.service.ContractCollectionLetterMainService;
import com.topsec.crm.flow.core.service.ContractCollectionLetterService;
import com.topsec.crm.flow.core.service.ContractCollectionLettersHandleService;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.security.config.AuthorizeContextHolder;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsNodeClient;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.vo.node.ApproveNode;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ContractCollectionLetterServiceImpl implements ContractCollectionLetterService {

    private final ContractCollectionLettersHandleService handleService;
    private final ContractCollectionLetterMainService mainService;
    private final RemoteContractExecuteService remoteContractExecuteService;
    private final TfsNodeClient tfsNodeClient;

    @Override
    public Boolean handleSuggest(String processInstanceId) {
        CollectionLettersHandleDTO handle = handleService.getByProcessInstanceId(processInstanceId);
        if (handle == null || handle.getHandleSuggest() == null) {
            throw new CrmException("处理意见为必填");
        }
        return true;
    }

    @Override
    public Boolean letterInfo(String processInstanceId) {
        CollectionLettersHandleDTO handleDTO = handleService.getByProcessInstanceId(processInstanceId);
        if (handleDTO == null) {
            throw new CrmException("催款函信息为空");
        }
        Integer letterType = handleDTO.getLetterType();
        CrmAssert.notNull(handleDTO.getLetterType(), "催款函类型为必填");
        if (letterType == 3) {
            // 3申请发函金额已回 什么都可以不用填
            return true;
        }
        CrmAssert.notNull(handleDTO.getLetterDate(), "催款函单号为必填");
        CrmAssert.notNull(handleDTO.getCourierNumber(), "快递单号为必填");
        if (letterType == 1) {
            CrmAssert.notNull(handleDTO.getDocs(), "催款函附件为必填");
        }
        if (letterType == 2) {
            CrmAssert.notNull(handleDTO.getSignatureDate(), "签收日期为必填");
            CrmAssert.notNull(handleDTO.getCommitmentTime(), "承诺付款时间为必填");
        }
        return true;
    }

    @Override
    public PageUtils<CollectionLettersDTO> pageCollectionLetters(CollectionLettersQuery query) {
        DataScopeParam dataScopeParam = AuthorizeContextHolder.getDataScopeParam();
        Set<String> personIdList = dataScopeParam.getPersonIdList();
        List<ContractCollectionLetterMain> list = mainService.list(new LambdaQueryWrapper<ContractCollectionLetterMain>()
                .like(StringUtils.isNotEmpty(query.getContractNumber()), ContractCollectionLetterMain::getContractNumber, query.getContractNumber())
                .like(StringUtils.isNotEmpty(query.getContractCompanyName()), ContractCollectionLetterMain::getContractCompanyName, query.getContractCompanyName())
                .like(StringUtils.isNotEmpty(query.getFinalCustomerName()), ContractCollectionLetterMain::getFinalCustomerName, query.getFinalCustomerName())
                .eq(StringUtils.isNotEmpty(query.getContractOwnerDeptId()), ContractCollectionLetterMain::getContractOwnerDeptId, query.getContractOwnerDeptId())
                .eq(StringUtils.isNotEmpty(query.getContractOwnerId()), ContractCollectionLetterMain::getContractOwnerId, query.getContractOwnerId())
                .between(query.getCollectionDateStart() != null && query.getCollectionDateEnd() != null, ContractCollectionLetterMain::getCollectionDate, query.getCollectionDateStart(), query.getCollectionDateEnd())
                .in(!CollectionUtils.isEmpty(personIdList), ContractCollectionLetterMain::getContractOwnerId, personIdList)
                .eq(ContractCollectionLetterMain::getDelFlag, false));
        PageUtils<CollectionLettersDTO> pageUtils = new PageUtils<>();
        pageUtils.setTotalCount((int) new PageInfo(list).getTotal());
        if (CollectionUtils.isEmpty(list)) {
            return pageUtils;
        }

        Set<String> contractNumbers = new HashSet<>();
        Set<String> processInstanceIds = new HashSet<>();
        list.forEach(item -> {
            contractNumbers.add(item.getContractNumber());
            processInstanceIds.add(item.getProcessInstanceId());
        });

        // rpc 合同执行
        List<CrmContractExecuteVO> crmContractExecuteVOS = remoteContractExecuteService.getByContractNumberBatch(contractNumbers).getObjEntity();
        Map<String, CrmContractExecuteVO> contractExecuteVOMap = crmContractExecuteVOS.stream().collect(Collectors.toMap(CrmContractExecuteVO::getContractNumber, v -> v, (v1, v2) -> v1));

        // 催款函建议
        List<CollectionLettersHandleDTO> lettersHandleDTOS = handleService.getByProcessInstanceIdBatch(processInstanceIds);
        Map<String, CollectionLettersHandleDTO> lettersHandleDTOMap = lettersHandleDTOS.stream().collect(Collectors.toMap(CollectionLettersHandleDTO::getProcessInstanceId, v -> v, (v1, v2) -> v1));

        // 流程进度
        Map<String, Set<ApproveNode>> nodesById = tfsNodeClient.queryNodeByProcessInstanceIdList(ListUtils.emptyIfNull(list).stream()
                .map(ContractCollectionLetterMain::getProcessInstanceId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList())).getObjEntity();

        List<CollectionLettersDTO> lettersDTOS = list.stream().map(item -> {
            CollectionLettersDTO dto = new CollectionLettersDTO();
            CrmContractExecuteVO crmContractExecuteVO = contractExecuteVOMap.get(item.getContractNumber());
            if (crmContractExecuteVO != null) {
                HyperBeanUtils.copyProperties(crmContractExecuteVO, dto);
            }
            dto.setProcessInstanceId(item.getProcessInstanceId());
            dto.setCollectionDate(item.getCollectionDate());
            dto.setProcessNumber(item.getProcessNumber());
            CollectionLettersHandleDTO handleDTO = lettersHandleDTOMap.get(item.getProcessInstanceId());
            if (handleDTO != null) {
                dto.setHandleSuggest(handleDTO.getHandleSuggest());
                dto.setCourierNumber(handleDTO.getCourierNumber());
                dto.setSigningLetterTime(handleDTO.getSigningTime());
                dto.setLetterType(handleDTO.getLetterType());
            }
            Set<ApproveNode> approveNodes = nodesById.get(item.getProcessInstanceId());
            if (approveNodes != null) {
                dto.setApprovalNode(approveNodes);
            }

            return dto;
        }).toList();
        pageUtils.setList(lettersDTOS);
        return pageUtils;
    }

    @Override
    public Boolean updateContractOwner(List<HandoverContractVo> handoverContractVo) {
        // key 人 value personVo
        Map<String, HandoverContractVo> handoverContractVoMap = new HashMap<>();
        Map<String, Set<String>> contractMap = handoverContractVo.stream().filter(item -> {
            if (StringUtils.isNotEmpty(item.getReceiverId())) {
                handoverContractVoMap.put(item.getReceiverId(), item);
                return true;
            }
            return false;
        }).collect(Collectors.groupingBy(HandoverContractVo::getReceiverId,
                Collectors.collectingAndThen(Collectors.toList(), vos -> vos.stream().map(HandoverContractVo::getContractNumber).filter(Objects::nonNull).collect(Collectors.toSet()))));

        // 循环
        if (MapUtils.isEmpty(contractMap)){
            return true;
        }
        contractMap.forEach((id, contractNumbers) -> {
            if (CollectionUtils.isEmpty(contractNumbers)) {
                return;
            }
            HandoverContractVo contractVo = handoverContractVoMap.get(id);
            String receiverId = contractVo.getReceiverId();
            String deptId = contractVo.getDeptId();
            // update
            mainService.update(new LambdaUpdateWrapper<ContractCollectionLetterMain>()
                    .set(ContractCollectionLetterMain::getContractOwnerId, receiverId)
                    .set(ContractCollectionLetterMain::getContractOwnerDeptId, deptId)
                    .in(ContractCollectionLetterMain::getContractNumber, contractNumbers));
        });
        return true;
    }


}
