package com.topsec.crm.flow.core.controller.agentauthentication;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.topsec.crm.agent.api.RemoteCrmAgentAuthenticationService;
import com.topsec.crm.agent.api.entity.CrmAgentAuthenticationVO;
import com.topsec.crm.contract.api.RemoteCrmAgentPaymentVerificationService;
import com.topsec.crm.contract.api.entity.crmagentpayment.agentpaymentverification.AgentPaymentHiddenPageQuery;
import com.topsec.crm.contract.api.entity.crmagentpayment.agentpaymentverification.AgentPaymentVerificationVO;
import com.topsec.crm.flow.api.dto.agentauthentication.*;
import com.topsec.crm.flow.api.vo.sealApplication.SealApplicationInfoVO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.entity.AgentAuthentication;
import com.topsec.crm.flow.core.entity.AgentAuthenticationDoc;
import com.topsec.crm.flow.core.handler.right.Step00RemoveEditProcessRightHandler;
import com.topsec.crm.flow.core.mapstruct.AgentAuthenticationConvertor;
import com.topsec.crm.flow.core.process.impl.AgentAuthenticationProcessService;
import com.topsec.crm.flow.core.service.AgentAuthenticationDocService;
import com.topsec.crm.flow.core.service.AgentAuthenticationService;
import com.topsec.crm.flow.core.service.SealApplicationService;
import com.topsec.crm.framework.common.bean.AreaVO;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.AgentEnum;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsHistoryClient;
import com.topsec.tos.api.client.TosEmployeeClient;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

@RestController
@RequestMapping("/agentAuthentication")
@Tag(name = "渠道认证", description = "/agentAuthentication")
@RequiredArgsConstructor
@Validated
public class AgentAuthenticationController extends BaseController {
    private final AgentAuthenticationProcessService agentAuthenticationProcessService;

    private final AgentAuthenticationService agentAuthenticationService;
    private final AgentAuthenticationDocService agentAuthenticationDocService;

    private final TosEmployeeClient tosEmployeeClient;
    private final RemoteCrmAgentAuthenticationService remoteCrmAgentAuthenticationService;
    private final ResourceLoader resourceLoader;
    private final SealApplicationService sealApplicationService;

    private final RemoteCrmAgentPaymentVerificationService remoteCrmAgentPaymentVerificationService;
    private final TfsHistoryClient tfsHistoryClient;

    @PostMapping("/launch")
    @Operation(summary = "发起渠道认证")
    @PreAuthorize(hasPermission = "crm_agent_authentication_launch")
    public JsonObject<Boolean> launch(@Valid @RequestBody AgentAuthenticationFlowLaunchDTO launchDTO){
        return new JsonObject<>(agentAuthenticationProcessService.launch(launchDTO));
    }


    @GetMapping("/getAgentAuthenticationDetailByProcessInstanceId")
    @Operation(summary = "根据渠道认证实例ID查询流程详细信息")
    @PreFlowPermission
    public JsonObject<AgentAuthenticationDetailVO> getAgentAuthenticationDetailByProcessInstanceId(){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject<>(agentAuthenticationService.selectAgentAuthenticationDetailByProcessInstanceId(processInstanceId));
    }

    /*@GetMapping("/getAgentInfo")
    @Operation(summary = "渠道基本信息")
    @PreAuthorize(hasPermission = "crm_agent_authentication_launch")
    public JsonObject<CrmAgentVo> getAgentInfo(@RequestParam String agentId){
        return new JsonObject<>(remoteAgentService.getAgentInfo(agentId).getObjEntity());
    }*/

    @GetMapping("/isTaskApproved")
    @Operation(summary = "查询流程是否审批过03B")
    @PreFlowPermission
    public JsonObject<Boolean> isTaskApproved(){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return tfsHistoryClient.isTaskApproved(processInstanceId,"channelAuthentication_03B");
    }

    @GetMapping("/getAgentClassification")
    @Operation(summary = "申请渠道分类")
    @PreAuthorize(hasPermission = "crm_agent_authentication_launch")
    public JsonObject<List<AgentClassificationVO>> getAgentClassification(@RequestParam String agentId){
        return new JsonObject<>(agentAuthenticationService.selectAgentClassification(agentId));
    }

    /*@PostMapping("/getAgentAuthenticationManageList")
    @Operation(summary = "渠道认证管理列表")
    @PreAuthorize(hasPermission = "crm_agent_authentication", dataScope = "crm_agent_authentication")
    public JsonObject<TableDataInfo> getAgentAuthenticationManageList(@RequestBody(required = false) AgentAuthenticationQuery agentAuthenticationQuery){
        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();
        startPage();
        TableDataInfo tableDataInfo = agentAuthenticationService.selectAgentAuthenticationManageList(agentAuthenticationQuery, dataScopeParam);
        return new JsonObject<>(tableDataInfo);
    }*/

    @GetMapping("/getManageListAgentClassification")
    @Operation(summary = "渠道认证管理列表-渠道分类查询列表")
    @PreAuthorize
    public JsonObject<List<AgentClassificationVO>> getManageListAgentClassification(){
        return new JsonObject<>(agentAuthenticationService.selectManageListAgentClassification());
    }

    @GetMapping("/getGeneralAgentList")
    @Operation(summary = "总代信息")
    @PreAuthorize(hasPermission = "crm_agent_authentication_launch")
    public JsonObject<List<CrmAgentAuthenticationVO>> getGeneralAgentList(@RequestParam String agentId){
        return new JsonObject<>(remoteCrmAgentAuthenticationService.getGeneralAgentList(agentId).getObjEntity());
    }

    @PostMapping("/getAgentAreaProvince")
    @Operation(summary = "查询代理区域-省份")
    @PreAuthorize(hasPermission = "crm_agent_authentication_launch")
    public JsonObject<List<AreaVO>> getAgentAreaProvince(@RequestBody AgentAuthenticationQuery agentAuthenticationQuery){
        return new JsonObject<>(agentAuthenticationService.selectAgentAreaProvince(agentAuthenticationQuery));
    }

    @PostMapping("/getAgentAreaCity")
    @Operation(summary = "查询代理区域-市县")
    @PreAuthorize(hasPermission = "crm_agent_authentication_launch")
    public JsonObject<List<AreaVO>> getAgentAreaCity(@RequestBody AgentAuthenticationQuery agentAuthenticationQuery){
        return new JsonObject<>(agentAuthenticationService.selectAgentAreaCity(agentAuthenticationQuery));
    }

    @PostMapping("/getAgentTaskAmount")
    @Operation(summary = "查询代理任务金额")
    @PreAuthorize(hasPermission = "crm_agent_authentication_launch")
    public JsonObject<AgentAuthenticationTaskAmountDTO> getAgentTaskAmount(@RequestBody AgentAuthenticationQuery agentAuthenticationQuery){
        return new JsonObject<>(agentAuthenticationService.selectAgentTaskAmount(agentAuthenticationQuery));

    }

    @GetMapping("/getAgentDirector")
    @Operation(summary = "查询渠道总监")
    @PreAuthorize(hasPermission = "crm_agent_authentication_launch")
    public JsonObject<List<AgentDirectorVO>> getAgentDirector(@RequestParam String agentId){
        return new JsonObject<>(agentAuthenticationService.selectAgentDirector(agentId));
    }

    @PostMapping("/getAgentAuthenticationTechnician")
    @Operation(summary = "查询技术人员数量")
    @PreAuthorize(hasPermission = "crm_agent_authentication_launch")
    public JsonObject<AgentAuthenticationTechnicianDTO> getAgentAuthenticationTechnician(@RequestBody AgentAuthenticationQuery agentAuthenticationQuery){
        return new JsonObject<>(agentAuthenticationService.selectAgentAuthenticationTechnician(agentAuthenticationQuery));

    }

    @PostMapping("/getAgentAuthenticationRegCapital")
    @Operation(summary = "提交检验注册资金")
    @PreAuthorize(hasPermission = "crm_agent_authentication_launch")
    public JsonObject<Boolean> getAgentAuthenticationRegCapital(@RequestBody AgentAuthenticationQuery agentAuthenticationQuery){
        return new JsonObject<>(agentAuthenticationService.selectAgentAuthenticationRegCapital(agentAuthenticationQuery));
    }

    @GetMapping("/checkSaleName")
    @Operation(summary = "检验销售人员和渠道总监名字是否正确")
    @PreAuthorize(hasPermission = "crm_agent_authentication_launch")
    public JsonObject<String> checkSaleName(@RequestParam String saleName, @RequestParam String agentDirectorName){
        return new JsonObject<>(agentAuthenticationService.selectSaleName(saleName, agentDirectorName));
    }

    @GetMapping("/getAgentArrearsAmount")
    @Operation(summary = "查询渠道-欠款金额 超期应收")
    @PreAuthorize(hasPermission = "crm_agent_authentication_launch")
    public JsonObject<AgentArrearsAmountVO> getAgentArrearsAmount(@RequestParam String agentId){
        return new JsonObject<>(agentAuthenticationService.selectAgentArrearsAmount(agentId));
    }

    @PostMapping("/saveFileInfo")
    @Operation(summary = "保存文件信息")
    @PreFlowPermission
    public JsonObject<Boolean> saveFileInfo(@RequestBody @Valid @NotEmpty List<AgentAuthenticationDocDTO> fileInfoList){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        for (AgentAuthenticationDocDTO agentAuthenticationDocDTO : fileInfoList) {
            agentAuthenticationDocDTO.setProcessInstanceId(processInstanceId);
        }
        return new JsonObject<>(agentAuthenticationDocService.saveBatch( AgentAuthenticationConvertor.INSTANCE.toDocList(fileInfoList)));
    }

    @PostMapping("/deleteFileInfo")
    @Operation(summary = "删除文件信息")
    @PreFlowPermission
    public JsonObject<Boolean> deleteFileInfo(@RequestBody @NotEmpty List<AgentAuthenticationDocDTO> fileInfoList){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        boolean remove = agentAuthenticationDocService.remove(new QueryWrapper<AgentAuthenticationDoc>()
                .eq("process_instance_id", processInstanceId)
                .in("id", fileInfoList.stream().map(AgentAuthenticationDocDTO::getId).toList()));
        return new JsonObject<>(remove);
    }


    @GetMapping("/getAgentAdvancePaymentBalance")
    @Operation(summary = "查询渠道预付款余额")
    @PreFlowPermission
    //@PreAuthorize(hasPermission = "crm_agent_authentication")
    public JsonObject<Boolean> getAgentAdvancePaymentBalance(){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject<>(agentAuthenticationService.selectAgentAdvancePaymentBalance(processInstanceId));
    }

    @PostMapping("/getAgentAuthenticationProvince")
    @Operation(summary = "认证国代判断代理区域是否有其他国代代理")
    @PreAuthorize(hasPermission = "crm_agent_authentication_launch")
    public JsonObject<List<String>> getAgentAuthenticationProvince(@RequestBody List<String> areaCodeList){
        return new JsonObject<>(agentAuthenticationService.selectAgentAuthenticationProvince(areaCodeList));
    }

    @GetMapping("/valid03")
    @Operation(summary = "03标记终稿")
    @PreFlowPermission
    public JsonObject<Boolean> valid03(){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        //盖章协议校验
        boolean v3 = agentAuthenticationDocService.exists(new QueryWrapper<AgentAuthenticationDoc>()
                .eq("process_instance_id", processInstanceId)
                .eq("final_doc", 1)
        );
        if (!v3) {
            return JsonObject.errorT("请标记终稿");
        }

        return new JsonObject<>(true);
    }

    @GetMapping("/valid04")
    @Operation(summary = "04申请人提交盖章协议校验")
    @PreFlowPermission
    public JsonObject<Boolean> valid04(){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        //预付款金额校验
        boolean v1 = agentAuthenticationService.selectAgentAdvancePaymentBalance(processInstanceId);
        if (!v1){
            return JsonObject.errorT("抱歉，当前预付款余额不足，请及时缴纳预付款并发起付款申请");
        }
        // 印鉴申请校验
        List<SealApplicationInfoVO> sealApplications = sealApplicationService.listSealInfoByParentProcessInstanceId(processInstanceId);
        if (CollectionUtils.isEmpty(sealApplications)){
            return JsonObject.errorT("请发起印鉴申请");
        }else {
            List<SealApplicationInfoVO> list = sealApplications.stream().filter(sealApplication -> sealApplication.getProcessState() != 2).toList();
            if (CollectionUtils.isNotEmpty(list)){
                return JsonObject.errorT("印鉴申请流程未办结");
            }
        }

        AgentAuthentication agentAuthentication = agentAuthenticationService
                .getOne(new QueryWrapper<AgentAuthentication>().eq("process_instance_id", processInstanceId).eq("del_flag", 0));
        AgentEnum.AgentLevelEnum agentLevelEnum = AgentEnum.AgentLevelEnum.fromLevel(agentAuthentication.getAgentClassification());

        // PLATINUM_AGENT(6, "铂金代理商"),
        //         GOLD_AGENT(7, "金牌代理商"),
        //         SILVER_AGENT(8, "银牌代理商"),
        // 付款凭证：仅限制授权合作伙伴（铂金、金、银牌）必填上传付款凭证，其他身份非必填
        if (AgentEnum.AgentLevelEnum.PLATINUM_AGENT .equals(agentLevelEnum)  ||
            AgentEnum.AgentLevelEnum.GOLD_AGENT .equals(agentLevelEnum)||
            AgentEnum.AgentLevelEnum.SILVER_AGENT .equals(agentLevelEnum)){
            //付款凭证校验
            boolean v2 = agentAuthenticationDocService.exists(new QueryWrapper<AgentAuthenticationDoc>()
                    .eq("process_instance_id", processInstanceId)
                    .eq("type", "付款凭证")
            );
            if (!v2) {
                return JsonObject.errorT("请上传付款凭证");
            }
        }

        //盖章协议校验
        boolean v3 = agentAuthenticationDocService.exists(new QueryWrapper<AgentAuthenticationDoc>()
                .eq("process_instance_id", processInstanceId)
                .eq("type", "盖章协议")
        );
        if (!v3) {
            return JsonObject.errorT("请上传盖章协议");
        }

        return new JsonObject<>(true);
    }

    @GetMapping("/valid05")
    @Operation(summary = "05制作授权证书校验")
    @PreFlowPermission
    public JsonObject<Boolean> valid05(){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        //预付款金额校验
        boolean v1 = agentAuthenticationService.selectAgentAdvancePaymentBalance(processInstanceId);
        if (!v1){
            return JsonObject.errorT("抱歉，当前预付款余额不足，请及时缴纳预付款并发起付款申请");
        }
        // 印鉴申请校验
        List<SealApplicationInfoVO> sealApplications = sealApplicationService.listSealInfoByParentProcessInstanceId(processInstanceId);
        if (CollectionUtils.isEmpty(sealApplications)){
            return JsonObject.errorT("请发起印鉴申请");
        }else {
            List<SealApplicationInfoVO> list = sealApplications.stream().filter(sealApplication -> sealApplication.getProcessState() != 2).toList();
            if (CollectionUtils.isNotEmpty(list)){
                return JsonObject.errorT("印鉴申请流程未办结");
            }
        }
        //协议原件
        boolean v3 = agentAuthenticationDocService.exists(new QueryWrapper<AgentAuthenticationDoc>()
                .eq("process_instance_id", processInstanceId)
                .eq("type", "协议原件")
        );
        if (!v3) {
            return JsonObject.errorT("请上传协议原件");
        }

        //授权证书
        boolean v2 = agentAuthenticationDocService.exists(new QueryWrapper<AgentAuthenticationDoc>()
                .eq("process_instance_id", processInstanceId)
                .eq("type", "授权证书")
        );
        if (!v2) {
            return JsonObject.errorT("请上传授权证书");
        }
        return new JsonObject<>(true);
    }


    @GetMapping("/getAgentAuthenticationClassification")
    @Operation(summary = "前端调用-渠道认证可选择的所有渠道分类")
    @PreAuthorize
    public JsonObject<List<AgentClassificationVO>> getAgentAuthenticationClassification(){
        return new JsonObject<>(agentAuthenticationService.selectAgentAuthenticationClassification());
    }

    @GetMapping("/download/strategicPartner")
    @Operation(summary = "天融信战略合作伙伴合作协议")
    @PreAuthorize(hasPermission = "crm_agent_authentication_launch")
    public void downloadStrategicPartner(HttpServletResponse response) throws IOException {
        downloadAgentFile(response, "2024年天融信战略合作伙伴合作协议.docx");
    }

    @GetMapping("/download/provincialDistributor")
    @Operation(summary = "2024年天融信省级总经销商合作协议")
    @PreAuthorize(hasPermission = "crm_agent_authentication_launch")
    public void downloadProvincialDistributor(HttpServletResponse response) throws IOException {
        downloadAgentFile(response, "2024年天融信省级总经销商合作协议.docx");
    }

    @GetMapping("/download/authorizedPartner")
    @Operation(summary = "2024年天融信授权合作伙伴合作协议")
    @PreAuthorize(hasPermission = "crm_agent_authentication_launch")
    public void downloadAuthorizedPartner(HttpServletResponse response) throws IOException {
        downloadAgentFile(response, "2024年天融信授权合作伙伴合作协议.docx");
    }


    public void downloadAgentFile(HttpServletResponse response, @RequestParam String fileName) throws IOException {
        // 构建资源路径
        String resourcePath = "classpath:word/" + fileName;
        // 加载资源
        Resource resource = resourceLoader.getResource(resourcePath);
        if (!resource.exists()) {
            response.sendError(HttpServletResponse.SC_NOT_FOUND, "File not found");
            return;
        }

        String mimeType = getMimeType(fileName);
        // 设置响应头
        response.setContentType(mimeType);
        // 对文件名进行 URL 编码
        String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
        response.setHeader("Content-disposition", "attachment;filename=" + encodedFileName);
        // 读取文件内容并写入响应输出流
        try (InputStream inputStream = new BufferedInputStream(resource.getInputStream());
             OutputStream outputStream = response.getOutputStream()) {

            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
        }
    }

    private String getMimeType(String fileName) {
        String type = "application/octet-stream";
        String fileExtension = "";
        int dotIndex = fileName.lastIndexOf('.');
        if (dotIndex != -1) {
            fileExtension = fileName.substring(dotIndex + 1).toLowerCase();

            switch (fileExtension) {
                case "doc":
                    type = "application/msword";
                    break;
                case "docx":
                    type = "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
                    break;
                case "xlsx":
                    type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                    break;
            }
        }
        return type;
    }

    @PostMapping("/getAgentPaymentDisburseList")
    @Operation(summary = "渠道预付款汇总列表")
    @PreAuthorize(hasPermission = "crm_agent_repayment_stats",dataScope="crm_agent_repayment_stats")
    public JsonObject<PageUtils<AgentPaymentDisburseVO>> getAgentPaymentDisburseList(@RequestBody(required = false) AgentPaymentDisburseQuery agentPaymentDisburseQuery){
        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();
        return new JsonObject<>(agentAuthenticationService.selectAgentPaymentDisburseList(agentPaymentDisburseQuery, dataScopeParam));
    }

    @PostMapping("/getAgentPaymentDisburseInfo")
    @Operation(summary = "渠道预付款汇总详情")
    @PreAuthorize(hasPermission = "crm_agent_repayment_stats",dataScope="crm_agent_repayment_stats")
    public JsonObject<AgentPaymentDisburseVO> getAgentPaymentDisburseInfo(@RequestBody(required = false) AgentPaymentDisburseQuery agentPaymentDisburseQuery){
        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();
        return new JsonObject<>(agentAuthenticationService.selectAgentPaymentDisburseInfo(agentPaymentDisburseQuery, dataScopeParam));
    }

    @PostMapping("/verificationPage")
    @Operation(summary = "渠道预付款汇总详情-回款核销分页列表")
    @PreAuthorize(hasPermission = "crm_agent_repayment_stats",dataScope="crm_agent_repayment_stats")
    public JsonObject<PageUtils<AgentPaymentVerificationVO>> verificationHiddenPage(@RequestBody AgentPaymentDisburseQuery agentPaymentDisburseQuery){
        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();
        AgentPaymentDisburseVO agentPaymentDisburseVO = agentAuthenticationService.selectAgentPaymentDisburseInfo(agentPaymentDisburseQuery, dataScopeParam);
        if (agentPaymentDisburseVO == null){
            return new JsonObject<>(PageUtils.empty());
        }
        AgentPaymentHiddenPageQuery query = new AgentPaymentHiddenPageQuery();
        query.setPayerAgentName(agentPaymentDisburseVO.getAgentName());
        query.setReceivedAgentName(agentPaymentDisburseVO.getGeneralAgentName());
        return remoteCrmAgentPaymentVerificationService.verificationHiddenPage(query);
    }

    @GetMapping("/getAgentAreaInfo")
    @Operation(summary = "判断渠道是否港澳地区")
    @PreAuthorize
    public JsonObject<Boolean> getAgentAreaInfo(@RequestParam String agentId){
        return new JsonObject<>(agentAuthenticationService.getAgentAreaInfo(agentId));
    }

    @GetMapping("/backConditions")
    @Operation(summary = "04，05步判断能否退回")
    @PreFlowPermission(hasAnyNodes = {"sid-188EB87D-5207-4284-9D62-E1C5CBCD32B7","sid-45DA519B-6AF8-410F-8AFB-CAFE76975104"})
    public JsonObject<Boolean> backConditions(){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        // 印鉴申请校验
        List<SealApplicationInfoVO> sealApplications = sealApplicationService.listSealInfoByParentProcessInstanceId(processInstanceId);
        List<SealApplicationInfoVO> list = ListUtils.emptyIfNull(sealApplications).stream().filter(sealApplication -> sealApplication.getProcessState() == 2).toList();
        if (CollectionUtils.isNotEmpty(list)) {
            return JsonObject.errorT("检测到当前印鉴申请已办结，不允许退回该流程。");
        }

        return new JsonObject<>(true);
    }

    @PostMapping("/00edit")
    @Operation(summary = "00步编辑流程")
    @PreAuthorize(rightHandler = Step00RemoveEditProcessRightHandler.class)
    public JsonObject<Boolean> editProcess00(@RequestBody AgentAuthenticationFlowLaunchDTO agentAuthenticationFlowLaunchDTO){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        agentAuthenticationFlowLaunchDTO.setProcessInstanceId(processInstanceId);
        agentAuthenticationProcessService.edit00(agentAuthenticationFlowLaunchDTO);
        return new JsonObject<>(true);
    }

    @PostMapping("/editFileInfo")
    @Operation(summary = "修改文件信息")
    @PreFlowPermission
    public JsonObject<Boolean> editFileInfo(@RequestBody AgentAuthenticationDocDTO fileInfoList){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        fileInfoList.setProcessInstanceId(processInstanceId);
        if (fileInfoList.getFinalDoc() == 1){
            agentAuthenticationDocService.update(new LambdaUpdateWrapper<AgentAuthenticationDoc>().eq(AgentAuthenticationDoc::getProcessInstanceId, processInstanceId)
                    .eq(AgentAuthenticationDoc::getFinalDoc, 1).set(AgentAuthenticationDoc::getFinalDoc, null));
        }
        return new JsonObject<>(agentAuthenticationDocService.updateById(HyperBeanUtils.copyProperties(fileInfoList, AgentAuthenticationDoc::new)));
    }
}
