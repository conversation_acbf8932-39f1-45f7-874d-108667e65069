package com.topsec.crm.flow.core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.topsec.crm.flow.api.dto.loan.loanApply.*;
import com.topsec.crm.flow.api.dto.loan.loanToCost.LoanToCostFlowBaseInfoLaunchDTO;
import com.topsec.crm.flow.api.dto.loan.loanToCost.LoanToCostPageQuery;
import com.topsec.crm.flow.api.dto.loan.loanVerification.LoanVerificationQuery;
import com.topsec.crm.flow.api.dto.loan.loanVerification.VerificationPageInfo;
import com.topsec.crm.flow.api.vo.ProcessFileInfoVO;
import com.topsec.crm.flow.api.vo.handoverProcess.HandoverLoanVo;
import com.topsec.crm.flow.core.entity.LoanApply;
import com.topsec.crm.flow.core.entity.ProcessExtensionInfo;
import com.topsec.crm.framework.common.bean.HandoverProcessQuery;
import com.topsec.crm.framework.common.util.PageUtils;

import java.time.LocalDateTime;
import java.util.List;

public interface LoanApplyService extends IService<LoanApply> {

    /**
     * 查询流程列表 借款/借款转费用
     */
    PageUtils<LoanApplyDTO> loanApplyPage(LoanApplyQuery query);

    /**
     * 查询流程列表 借款转费用
     */
    PageUtils<LoanApplyDTO> loanToCostPage(LoanToCostPageQuery query);

    /**
     * 借款核销分页列表
     */
    PageUtils<VerificationPageInfo> loanVerificationPage(LoanVerificationQuery query);

    /**
     * 导出流程列表
     */
    List<LoanApplyDTO> loanApplyExport(LoanApplyQuery query);


    /**
     * 根据流程实例ID查询流程信息
     */
    LoanApplyDTO queryByProcessInstanceId(String processInstanceId);


    /**
     * 根据流程实例ID 查询流程信息批量
     */
    List<LoanApplyDTO> queryByProcessInstanceIdList(List<String> processInstanceIdList);

    /**
     * 根据ID查询流程信息
     */
    LoanApplyDTO queryById(String id);

    /**
     * 保存流程快照
     */
    String saveSnapshot(ProcessExtensionInfo processInfo,LoanApplyLaunchDTO req);

    /**
     * 上传借款凭证附件
     */
    Boolean uploadLoanProofAttachments(String processInstanceId, List<ProcessFileInfoVO> fileInput,String loanTellerPersonId);

    /**
     * 填写付款日期
     */
    Boolean fillRepaymentDate(String processInstanceId, LocalDateTime repaymentDate,List<ProcessFileInfoVO> fileInput);

    /**
     * 判断是否可借款转费用
     * @param processInstanceId 流程实例ID
     * @return true:可借款转费用，false:不可借款转费用
     */
    Boolean isCanToCost(String processInstanceId);

    /**
     * 项目列表分页查询
     * @param param 查询参数
     * @return 项目列表
     */
    PageUtils<LoanProjectPageInfo> projectSelectPage(LoanProjectPageInfo req);

    /**
     * 采购列表分页查询
     * @param param 采购列表
     * @return 采购列表
     */
    PageUtils<LoanPurchasePageInfo> purchaseSelectPage(String param);

    /**
     * 人员交接 借款查询-分页
     */
    PageUtils<HandoverLoanVo> handoverLoanSelectPage(HandoverProcessQuery  query);

    /**
     * 人员交接 借款查询-全量
     */
    List<HandoverLoanVo> handoverLoanSelectList(HandoverProcessQuery  query);

    /**
     * 借款人正在审批中的流程列表
     */
    List<String> processingList(String personId);

    /**
     * 借款核销 修改借款信息
     */
    Boolean updateLoanInfo(LoanApplyDTO loanApplyDTO);

    /**
     * 修改借款转费用
     * @param infoLaunchDTO
     * @return
     */
    Boolean upLoanToCostInfo(LoanToCostFlowBaseInfoLaunchDTO infoLaunchDTO);

    /**
     * 借款出纳列表
     */
    List<LoanTellerVO> loanTellerSelectList();

}
