package com.topsec.crm.flow.core.service.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.topsec.crm.account.api.client.RemoteAccountService;
import com.topsec.crm.agent.api.RemoteAgentRebateService;
import com.topsec.crm.agent.api.RemoteAgentService;
import com.topsec.crm.agent.api.entity.CrmAgentDetailForAgentVo;
import com.topsec.crm.agent.api.entity.CrmAgentVo;
import com.topsec.crm.contract.api.RemoteContractExecuteService;
import com.topsec.crm.contract.api.RemoteContractReviewService;
import com.topsec.crm.customer.api.RemoteCustomerService;
import com.topsec.crm.customer.api.entity.CrmCustomerVo;
import com.topsec.crm.flow.api.RemotePerformanceExecuteService;
import com.topsec.crm.flow.api.dto.contractreview.*;
import com.topsec.crm.flow.api.dto.contractreview.baseinfo.ContractBasicInfoDTO;
import com.topsec.crm.flow.api.dto.contractreview.productinfo.ContractProductOwnDTO;
import com.topsec.crm.flow.api.dto.contractreview.productinfo.ContractProductThirdDTO;
import com.topsec.crm.flow.api.dto.contractreview.projectinfo.CrmProjectOtherVO;
import com.topsec.crm.flow.api.dto.contractreview.projectinfo.CrmProjectProductMaintainVO;
import com.topsec.crm.flow.api.dto.contractreview.salemaninfo.SalesSelectDTO;
import com.topsec.crm.flow.api.dto.contractreview.sninfo.ContractProductSnVO;
import com.topsec.crm.flow.api.dto.contractreview.statistics.ContractExecuteStatisticsVO;
import com.topsec.crm.flow.api.dto.flow.DraftsAble;
import com.topsec.crm.flow.api.dto.performancereport.PerformanceExecuteStatisticsVO;
import com.topsec.crm.flow.api.vo.handoverProcess.HandoverContractVo;
import com.topsec.crm.flow.core.draft.DraftsTypeEnum;
import com.topsec.crm.flow.core.draft.impl.AbstractDraftsService;
import com.topsec.crm.flow.core.entity.*;
import com.topsec.crm.flow.core.mapper.ContractReviewMainMapper;
import com.topsec.crm.flow.core.mapper.ContractReviewPaymentProvisionMapper;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.flow.core.util.FlowThreadExecutor;
import com.topsec.crm.framework.common.bean.CrmProjectProductSnVO;
import com.topsec.crm.framework.common.bean.FlowPerson;
import com.topsec.crm.framework.common.bean.HomeNameValue;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.AccountAccquireUtils;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.util.date.DateUtils;
import com.topsec.crm.operation.api.RemoteContractReviewConfigService;
import com.topsec.crm.operation.api.RemoteIndustryService;
import com.topsec.crm.operation.api.RemoteSystemIntegratorService;
import com.topsec.crm.operation.api.entity.ContractReviewConfig.ContractKeywordVO;
import com.topsec.crm.operation.api.entity.ContractReviewConfig.ContractSignCompanyVO;
import com.topsec.crm.operation.api.entity.ContractReviewConfig.ContractTypeVO;
import com.topsec.crm.operation.api.entity.CrmIndustryVO;
import com.topsec.crm.operation.api.entity.SystemIntegrator.SystemIntegratorVO;
import com.topsec.crm.project.api.RemoteProjectDynastyService;
import com.topsec.crm.project.api.client.RemoteProjectDirectlyClient;
import com.topsec.crm.project.api.client.RemoteProjectMemberClient;
import com.topsec.crm.project.api.client.RemoteProjectPerformanceNegotiationClient;
import com.topsec.crm.project.api.client.RemoteProjectSignAgentClient;
import com.topsec.crm.project.api.entity.*;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tbscommon.vo.PersonVO;
import com.topsec.tfs.api.client.TfsNodeClient;
import com.topsec.tos.api.client.TosEmployeeClient;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.tos.common.vo.EmployeeVO;
import com.topsec.vo.node.ApproveNode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.util.Strings;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.BiPredicate;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 合同评审service 实现类
 *
 * <AUTHOR>
 * @date 2024/7/9 17:36
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ContractReviewMainServiceImpl extends AbstractDraftsService<ContractReviewMainMapper, ContractReviewMain, ContractReviewMainFlowLaunchDTO> implements ContractReviewMainService, InitializingBean {

    private final Set<ContractPredicate> formPredicates = new TreeSet<>(Comparator.comparing(ContractPredicate::getRuleOrder));

    private final ContractReviewCustomerService contractReviewCustomerService;
    private final ContractReviewSpecialCodeService contractReviewSpecialCodeService;
    private final ContractReviewNoticeArriveService contractReviewNoticeArriveService;
    private final ContractReviewSignContractService contractReviewSignContractService;
    private final ContractReviewProductOwnService contractReviewProductOwnService;
    private final ContractReviewProductThirdService contractReviewProductThirdService;
    private final RemoteContractReviewConfigService remoteContractReviewConfigService;
    private final RemoteProjectDirectlyClient remoteProjectDirectlyClient;
    private final RemoteProjectDynastyService remoteProjectDynastyService;
    private final RemoteCustomerService remoteCustomerService;
    private final RemoteProjectPerformanceNegotiationClient remoteProjectPerformanceNegotiationClient;
    private final RemoteAccountService remoteAccountService;
    private final RemoteProjectMemberClient remoteProjectMemberClient;
    private final RemoteProjectSignAgentClient remoteProjectSignAgentClient;
    private final RemoteSystemIntegratorService remoteSystemIntegratorService;
    private final TosEmployeeClient tosEmployeeClient;
    private final RemoteIndustryService remoteIndustryService;

    private final RemoteContractReviewService remoteContractReviewService;
    private final RemoteAgentService remoteAgentService;
    private final ContractReviewPaymentProvisionMapper contractReviewPaymentProvisionMapper;
    private final RedisTemplate<String, String> redisTemplate;
    private final TfsNodeClient tfsNodeClient;
    private final RemoteContractExecuteService remoteContractExecuteService;
    private final RemotePerformanceExecuteService remotePerformanceExecuteService;
    private final RedissonClient redissonClient;
    private final ContractReviewNumberRecordService contractReviewNumberRecordService;
    private final RemoteAgentRebateService remoteAgentRebateService;

    public static final double ONE_MONTH = 30.417;

    @Override
    public void afterPropertiesSet() throws Exception {
        // 初始化判断条件
        this.formPredicateInit();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveBusiness(ContractReviewMainFlowLaunchDTO contractReviewMainFlowLaunchDTO) {
        // 从各个服务取值
        String projectId = contractReviewMainFlowLaunchDTO.getBaseInfoDTO().getProjectId();
        // 产品dto 取项目的 方便后续判断
        // 取客户相关信息，方便后续判断
        boolean isUpdate = !StringUtils.isEmpty(contractReviewMainFlowLaunchDTO.getBaseInfoDTO().getId());
        // todo request多线程获取不到 先改成单线程的
//        CompletableFuture<List<ContractProductOwnDTO>> productOwnFuture = FlowThreadExecutor.supplyAsync(() -> contractReviewProductOwnService.convertToProductOwn(contractReviewMainFlowLaunchDTO.getContractProductOwnDTOS(), projectId, isUpdate));
//        CompletableFuture<List<ContractProductThirdDTO>> productThirdFuture = FlowThreadExecutor.supplyAsync(() -> contractReviewProductThirdService.convertToProductThird(contractReviewMainFlowLaunchDTO.getContractProductThirdDTOS(), projectId, isUpdate));
//
//        contractReviewMainFlowLaunchDTO.setContractProductOwnDTOS(productOwnFuture.join());
//        contractReviewMainFlowLaunchDTO.setContractProductThirdDTOS(productThirdFuture.join());
        contractReviewMainFlowLaunchDTO.setContractProductOwnDTOS(contractReviewProductOwnService.convertToProductOwn(contractReviewMainFlowLaunchDTO.getContractProductOwnDTOS(), projectId, isUpdate));
        contractReviewMainFlowLaunchDTO.setContractProductThirdDTOS(contractReviewProductThirdService.convertToProductThird(contractReviewMainFlowLaunchDTO.getContractProductThirdDTOS(), projectId, isUpdate));

        // 直签项目的客户信息
        Pair<CrmProjectDirectlyVo, CrmCustomerVo> projectInfoDTO = Pair.of(new CrmProjectDirectlyVo(), new CrmCustomerVo());

        // 判断
        if (!StringUtils.hasText(contractReviewMainFlowLaunchDTO.getBaseInfoDTO().getPerformanceReportId()) && (
                contractReviewMainFlowLaunchDTO.getBaseInfoDTO().getProjectSource() == null ||
                        contractReviewMainFlowLaunchDTO.getBaseInfoDTO().getProjectSource() == 1)) {
            projectInfoDTO = getCustomerInfo(projectId);
            // 公司直签项目 非一键代下单或者是GD项目
            for (ContractPredicate predicate : formPredicates) {
                // 判断所有条件 不需要取checkRule 其他比如只修改合同信息则需要判断checkRule
                if (!predicate.getPredicate().test(contractReviewMainFlowLaunchDTO, projectInfoDTO)) {
                    throw new CrmException(predicate.getMessage());
                }
            }
        }
        // 2025 0428 之前是新增的时候校验这个，移动到前面统一校验，测试未测试出来，不确定逻辑是否正确 检查是否没传产品
        if (CollectionUtils.isEmpty(contractReviewMainFlowLaunchDTO.getContractProductOwnDTOS()) &&
                CollectionUtils.isEmpty(contractReviewMainFlowLaunchDTO.getContractProductThirdDTOS())) {
            throw new CrmException("请先添加产品信息");
        }
        // 保存主表
        ContractReviewMain contractReviewMain = HyperBeanUtils.copyProperties(contractReviewMainFlowLaunchDTO.getBaseInfoDTO(), ContractReviewMain::new);
        // 主表相关字段赋值
        contractReviewMain.setIsImportantContractIn(projectInfoDTO.getLeft().getPointIndustry());
        if (projectInfoDTO.getLeft().getSigningType() != null) {
            contractReviewMain.setSigningType(projectInfoDTO.getLeft().getSigningType());
        }
        // 业务类型
        contractReviewMain.setBusinessType(projectInfoDTO.getLeft().getBusinessType());
        // 判断产品是否关联了重点行业序列号
        contractReviewMain.setIsImportantContract(hasImportantSerial(contractReviewMainFlowLaunchDTO.getContractProductOwnDTOS()));
        // 保存相关信息
        if (isUpdate) {
            // 如果是修改 合同号为空 重新生成合同号
            refreshContractNumber(contractReviewMain);
        }
        this.saveOrUpdate(contractReviewMain);
        // 保存子表
        if (!isUpdate) {
            // 新增
            saveRelateTable(contractReviewMainFlowLaunchDTO, contractReviewMain.getId());

            String contractNumber = generateContractNumber(contractReviewMain.getSigningCompany(), true);

            update(new LambdaUpdateWrapper<ContractReviewMain>()
                    .set(ContractReviewMain::getContractNumber, contractNumber)
                    .eq(ContractReviewMain::getId, contractReviewMain.getId())
            );

            contractReviewProductOwnService.saveProductInfo(contractReviewMainFlowLaunchDTO.getContractProductOwnDTOS(), contractReviewMain.getId(), projectId);
            contractReviewProductThirdService.saveProductInfo(contractReviewMainFlowLaunchDTO.getContractProductThirdDTOS(), contractReviewMain.getId(), projectId);
        } else {
            updateRelateTable(contractReviewMainFlowLaunchDTO);
            contractReviewProductOwnService.updateProductInfo(contractReviewMainFlowLaunchDTO.getContractProductOwnDTOS(), contractReviewMain.getId(), projectId);
            contractReviewProductThirdService.updateProductInfo(contractReviewMainFlowLaunchDTO.getContractProductThirdDTOS(), contractReviewMain.getId(), projectId);

            // 如果是无纸质合同，删除所有的付款条款
            if (contractReviewMain.getContractForm() == 2) {
                contractReviewPaymentProvisionMapper.update(null, new LambdaUpdateWrapper<ContractReviewPaymentProvision>()
                        .set(ContractReviewPaymentProvision::getDelFlag, 1)
                        .eq(ContractReviewPaymentProvision::getContractReviewMainId, contractReviewMain.getId()));
            }
        }
        contractReviewProductOwnService.writeBackProject(contractReviewMain.getId(), 0);
        return contractReviewMain.getId();
    }

    private void refreshContractNumber(ContractReviewMain contractReviewMain) {
        ContractReviewMain contractReviewMain1 = this.getById(contractReviewMain.getId());
        if (contractReviewMain1 == null) {
            throw new CrmException("合同评审信息不存在");
        }
        // 判断是否修改了签订公司
        if (!contractReviewMain1.getSigningCompany().equals(contractReviewMain.getSigningCompany())) {
            contractReviewMain.setContractNumber(generateContractNumber(contractReviewMain.getSigningCompany(), true));
        }
    }

    /**
     * 根据签订公司生成合同号
     *
     * @param signCompanyId 签订公司
     * @return 合同号
     */
    public String generateContractNumber(String signCompanyId, boolean isUpdate) {
        // 生成合同号取 合同的签订公司
        JsonObject<ContractSignCompanyVO> result = remoteContractReviewConfigService.getBySignCompanyId(signCompanyId);
        if (!result.isSuccess()) {
            throw new CrmException("未找到对应的签订公司信息");
        }
        String threePrefix = result.getObjEntity().getThreePrefix();
        if (StringUtils.isEmpty(threePrefix)) {
            throw new CrmException("该签订公司缺少生成合同评审的账套信息!");
        }
        String lockKey = "contractNumberGenerate:" + signCompanyId;
        RLock rLock = redissonClient.getLock(lockKey);
        try {
            // 取出这个前缀今年最大序号 +1 key是前缀 + 当前年月
            rLock.lock();
            int year = LocalDate.now().getYear();

            // 取所有的 包括删除的
            ContractReviewNumberRecord record = contractReviewNumberRecordService.getOne(new LambdaQueryWrapper<ContractReviewNumberRecord>()
                    .eq(ContractReviewNumberRecord::getSignCompanyId, signCompanyId)
                    .eq(ContractReviewNumberRecord::getYearAndMonth, year));
            Integer count;
            if (record == null) {
                record = new ContractReviewNumberRecord();
                record.setSignCompanyId(signCompanyId);
                count = 1;
                record.setContractCount(count);
                record.setYearAndMonth(String.valueOf(year));
                if (isUpdate) {
                    contractReviewNumberRecordService.save(record);
                }
            } else {
                count = record.getContractCount() + 1;
                record.setContractCount(count);
                if (isUpdate) {
                    contractReviewNumberRecordService.updateById(record);
                }
            }
            return threePrefix + DateUtils.dateTimeNow("yyyyMM") + com.topsec.crm.framework.common.util.StringUtils.leftPad(String.valueOf(count), 5, "0");
        } finally {
            rLock.unlock();
        }

    }

    @Override
    public void generateFillContractNumber(String contractId, String contractNumber) {
        update(new LambdaUpdateWrapper<ContractReviewMain>()
                .set(ContractReviewMain::getContractNumber, contractNumber)
                .eq(ContractReviewMain::getId, contractId));
    }

    @Override
    public ContractReviewMainFlowLaunchDTO customerAndSignContractByProject(String projectId) {
        Pair<CrmProjectDirectlyVo, CrmCustomerVo> customerInfo = this.getCustomerInfo(projectId);
        // 需要的信息：签约模式，最终用户，一级行业，二级行业，签约单位，统一信用代码
        ContractReviewMainFlowLaunchDTO contractReviewMainFlowLaunchDTO = new ContractReviewMainFlowLaunchDTO();

        ContractReviewCustomerDTO contractReviewCustomerDTO = ContractReviewCustomerDTO.builder()
                .customerId(customerInfo.getRight().getId())
                .signingType(customerInfo.getLeft().getSigningType())
                .customerName(customerInfo.getRight().getName())
                .firstIndustry(customerInfo.getRight().getIndustryId())
                .secondIndustry(customerInfo.getRight().getIndustryIdTwo())
                .customerAddress(customerInfo.getRight().getDetailAddress())
                .provinceId(customerInfo.getRight().getProvinceCode())
                .cityId(customerInfo.getRight().getCityCode())
                .countyId(customerInfo.getRight().getCountyCode())
                .build();

        ContractReviewSignContractDTO contractReviewSignContractDTO = null;
        if (customerInfo.getLeft().getSigningType() == 1) {
            // 直签
            contractReviewSignContractDTO = ContractReviewSignContractDTO.builder()
                    .contractCompanyId(customerInfo.getRight().getId())
                    .contractCompanyName(customerInfo.getRight().getName())
                    .contractCreditCode(Optional.ofNullable(customerInfo.getRight()).map(CrmCustomerVo::getBusinessInfo).map(jsonObj -> jsonObj.getString("creditCode")).orElse(null))
                    .contractTelephone(Optional.ofNullable(customerInfo.getRight()).map(CrmCustomerVo::getMobile).orElse(null))
                    .contractBankAccount(Optional.ofNullable(customerInfo.getRight()).map(CrmCustomerVo::getBankAccount).orElse(null))
                    .contractCompanyBank(Optional.ofNullable(customerInfo.getRight()).map(CrmCustomerVo::getBankDeposit).orElse(null))
                    .registeredAddress(Optional.ofNullable(customerInfo.getRight()).map(CrmCustomerVo::getBusinessInfo).map(jsonObj -> jsonObj.getString("regLocation")).orElse(null))
                    .build();
        }
        if (customerInfo.getLeft().getSigningType() == 0) {
            CrmAgentDetailForAgentVo agentNode = getAgent(projectId);
            String agentId = agentNode.getId();
            if (!StringUtils.isEmpty(agentId)) {
                // 取渠道的工商信息 统一信用代码填入
                JsonObject<CrmAgentVo> agentInfo = remoteAgentService.getAgentInfo(agentId);
                if (!agentInfo.isSuccess()) {
                    throw new CrmException("获取渠道信息失败");
                }
                contractReviewSignContractDTO = ContractReviewSignContractDTO.builder()
                        .contractCompanyId(agentId)
                        .contractCompanyName(agentNode.getAgentName())
                        .contractCreditCode(Optional.ofNullable(agentInfo.getObjEntity()).map(CrmAgentVo::getCrmAgentBusinessInfoVo).map(jsonObj -> jsonObj.getString("creditCode")).orElse(null))
                        .contractTelephone(Optional.ofNullable(agentInfo.getObjEntity()).map(CrmAgentVo::getMobile).orElse(null))
                        .contractBankAccount(Optional.ofNullable(agentInfo.getObjEntity()).map(CrmAgentVo::getBankAccount).orElse(null))
                        .contractCompanyBank(Optional.ofNullable(agentInfo.getObjEntity()).map(CrmAgentVo::getDepositBank).orElse(null))
                        .registeredAddress(Optional.ofNullable(agentInfo.getObjEntity()).map(CrmAgentVo::getCrmAgentBusinessInfoVo).map(jsonObj -> jsonObj.getString("regLocation")).orElse(null))
                        .build();
            }
        }
        contractReviewMainFlowLaunchDTO.setContractReviewCustomerDTO(contractReviewCustomerDTO);
        contractReviewMainFlowLaunchDTO.setContractReviewSignContractDTO(contractReviewSignContractDTO);
        CrmProjectOtherVO otherVO = new CrmProjectOtherVO();
        otherVO.setWinningWebsite(Optional.ofNullable(customerInfo.getLeft()).map(CrmProjectDirectlyVo::getCrmProjectSigningInfo).map(CrmProjectSigningInfoVo::getWinningWebsite).orElse(null));
        otherVO.setIsDirect(Optional.ofNullable(customerInfo.getLeft()).map(CrmProjectDirectlyVo::getSigningType).orElse(0) == 1);
        contractReviewMainFlowLaunchDTO.setCrmProjectOtherVO(otherVO);
        return contractReviewMainFlowLaunchDTO;
    }

    private CrmAgentDetailForAgentVo getAgent(String projectId) {
        // 渠道
        JsonObject<List<AgentTreeSelect>> result = remoteProjectSignAgentClient.tree(projectId);
        if (!result.isSuccess()) {
            throw new CrmException("渠道信息有误");
        }
        List<AgentTreeSelect> agentTreeSelects = result.getObjEntity().stream().filter(agentTreeSelect ->
                "天融信".equals(agentTreeSelect.getAgentNode().getAgentName())).collect(Collectors.toList());
        // 取children
        return Optional.ofNullable(agentTreeSelects.get(0)).map(AgentTreeSelect::getChildren).map(list -> list.get(0)).map(AgentTreeSelect::getAgentNode).orElse(new CrmAgentDetailForAgentVo());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ContractReviewMainFlowLaunchDTO contractInfo(String contractId, Boolean isTile, Boolean needProduct) {
        ContractReviewMainFlowLaunchDTO contractReviewMainFlowLaunchDTO = new ContractReviewMainFlowLaunchDTO();
        // 组装合同信息
        ContractReviewMain contractReviewMain = getOne(new LambdaQueryWrapper<ContractReviewMain>().eq(ContractReviewMain::getId, contractId).eq(ContractReviewMain::getDelFlag, false));
        // 如果合同号为空 补充合同号 老版逻辑如此 查看详情 如果没有会补充合同号
        if (contractReviewMain.getContractNumber() == null) {
            String newContractNumber = generateContractNumber(contractReviewMain.getSigningCompany(), true);
            generateFillContractNumber(contractReviewMain.getId(), newContractNumber);
        }
        boolean isGenerate = StringUtils.hasText(contractReviewMain.getPerformanceReportId()) || Objects.equals(contractReviewMain.getProjectSource(), 3);
        // 判断是否存在
        Optional.ofNullable(contractReviewMain).orElseThrow(() -> new CrmException("合同不存在"));

        // 项目中没有的产品需要删除
        FlowThreadExecutor.runAsync(() -> {
            contractReviewProductOwnService.deleteProductProjectNotExist(contractId, contractReviewMain.getProjectId());
            contractReviewProductThirdService.deleteProductProjectNotExist(contractId, contractReviewMain.getProjectId());
        });

        // 异步取各个表的信息

        // 产品信息 组装产品绑定的序列号 以及毛利相关信息
        // request多线程获取不到 先改成单线程的
        // 串行取值 后续删除 =====
        List<ContractProductOwnDTO> contractProductOwnDTOS = null;
        List<ContractProductThirdDTO> contractProductThirdDTOS = null;
        // 串行取值 后续删除 =====
        // 项目信息
        Pair<CrmProjectDirectlyVo, CrmCustomerVo> projectInfo;
        CrmProjectDynastyVo dynastyInfo = null;
        ContractBasicInfoDTO baseInfoFuture;
        CompletableFuture<CrmAgentDetailForAgentVo> agentFuture = null;
        CompletableFuture<List<ContractProductOwnDTO>> contractProductOwnDTOSTask = null;
        CompletableFuture<List<ContractProductThirdDTO>> contractProductThirdDTOSTask = null;
        if (isGenerate) {
            // 非web环境
            if (needProduct) {
                // 不需要产品 不拼这些数据了 产品接口太慢了
                if (isTile) {
                    contractProductOwnDTOS = contractReviewProductOwnService.productOwnInfoTileByContractId(contractId, true);
                } else {
                    contractProductOwnDTOS = contractReviewProductOwnService.productInfoByContractId(contractId, true);
                }
                contractReviewProductOwnService.initOtherDetail(contractProductOwnDTOS, contractId, isTile);
                contractProductThirdDTOS = contractReviewProductThirdService.productThirdInfoByContractId(contractId, true);
                contractReviewProductThirdService.initSerialDetail(contractProductThirdDTOS, contractId);
            }

            // 多线程会导致事务失效 如果是代下单 需要直接用串行取值
            baseInfoFuture = contractBasicInfoSerial(contractId);
            dynastyInfo = remoteProjectDynastyService.getInfo(contractReviewMain.getProjectId()).getObjEntity();
            projectInfo = Pair.of(new CrmProjectDirectlyVo(), new CrmCustomerVo());
        } else {
            // 是web环境 可以并行
            if (needProduct) {
                // 不需要产品 不拼这些数据了 产品接口太慢了
                contractProductOwnDTOSTask = FlowThreadExecutor.supplyAsync(() -> {
                    if (isTile) {
                        return contractReviewProductOwnService.productOwnInfoTileByContractId(contractId, true);
                    } else {
                        return contractReviewProductOwnService.productInfoByContractId(contractId, true);
                    }
                }).thenApplyAsync((result) -> contractReviewProductOwnService.initOtherDetail(result, contractId, isTile));
                contractProductThirdDTOSTask = FlowThreadExecutor.supplyAsync(() -> contractReviewProductThirdService.productThirdInfoByContractId(contractId, true))
                        .thenApplyAsync((result) -> contractReviewProductThirdService.initSerialDetail(result, contractId));
            }
            baseInfoFuture = contractBasicInfo(contractId, true);
            projectInfo = getCustomerInfo(contractReviewMain.getProjectId());
            if (contractReviewMain.getSigningType() != null && contractReviewMain.getSigningType() == 0) {
                // 取值提前 如果是非多线程环境 且满足这个条件 先把任务扔进去
                agentFuture = FlowThreadExecutor.supplyAsync(() -> getAgent(contractReviewMain.getProjectId()));
            }
        }

        contractReviewMainFlowLaunchDTO.initBasicInfo(baseInfoFuture);
        // 取字典、人员的基础信息
        initBaseInfoDTO(contractReviewMainFlowLaunchDTO.getBaseInfoDTO(), contractReviewMain);

        if (needProduct) {
            if (isGenerate) {
                contractReviewMainFlowLaunchDTO.setContractProductOwnDTOS(contractProductOwnDTOS);
                contractReviewMainFlowLaunchDTO.setContractProductThirdDTOS(contractProductThirdDTOS);
            } else {
                contractReviewMainFlowLaunchDTO.setContractProductOwnDTOS(contractProductOwnDTOSTask.join());
                contractReviewMainFlowLaunchDTO.setContractProductThirdDTOS(contractProductThirdDTOSTask.join());
            }
        }
        // 行业取字典的
        JsonObject<CrmIndustryVO> firstIndustry = remoteIndustryService.industryByUuid(contractReviewMainFlowLaunchDTO.getContractReviewCustomerDTO().getFirstIndustry());
        if (firstIndustry.isSuccess() && firstIndustry.getObjEntity() != null) {
            contractReviewMainFlowLaunchDTO.getContractReviewCustomerDTO().setFirstIndustryName(firstIndustry.getObjEntity().getName());
        }
        JsonObject<CrmIndustryVO> secondIndustry = remoteIndustryService.industryByUuid(contractReviewMainFlowLaunchDTO.getContractReviewCustomerDTO().getSecondIndustry());
        if (secondIndustry.isSuccess() && secondIndustry.getObjEntity() != null) {
            contractReviewMainFlowLaunchDTO.getContractReviewCustomerDTO().setSecondIndustryName(secondIndustry.getObjEntity().getName());
        }
        if (needProduct) {
            // 项目中的报价赋值
            CrmProjectDirectlyPriceStatisticsVO priceStatisticsVO = initContractPriceStatistics(contractReviewMainFlowLaunchDTO, isTile);
            initSpecificInfo(contractReviewMainFlowLaunchDTO.getContractProductOwnDTOS(), isTile);
            // 单行毛利init
            initRowGross(priceStatisticsVO, contractReviewMainFlowLaunchDTO.getContractProductOwnDTOS(), contractReviewMainFlowLaunchDTO.getContractProductThirdDTOS());
        }

        // 项目相关信息赋值
//        Pair<CrmProjectDirectlyVo, CrmCustomerVo> projectInfo = projectInfoFuture.join();
        contractReviewMainFlowLaunchDTO.getBaseInfoDTO().setBiddingWebsite(Optional.ofNullable(projectInfo.getLeft()).map(CrmProjectDirectlyVo::getCrmProjectSigningInfo).map(CrmProjectSigningInfoVo::getBiddingWebsite).orElse(null));
        // 使用合同中的中标网址
//        contractReviewMainFlowLaunchDTO.getBaseInfoDTO().setWinningWebsite(Optional.ofNullable(projectInfo.getLeft()).map(CrmProjectDirectlyVo::getCrmProjectSigningInfo).map(CrmProjectSigningInfoVo::getWinningWebsite).orElse(null));
        contractReviewMainFlowLaunchDTO.getBaseInfoDTO().setBusinessType(Optional.ofNullable(projectInfo.getLeft()).map(CrmProjectDirectlyVo::getBusinessType).orElse(null));
        contractReviewMainFlowLaunchDTO.getBaseInfoDTO().setProjectNo(Optional.ofNullable(projectInfo.getLeft()).map(CrmProjectDirectlyVo::getProjectNo).orElse(null));
        contractReviewMainFlowLaunchDTO.getBaseInfoDTO().setProjectSmType(Optional.ofNullable(projectInfo.getLeft()).map(CrmProjectDirectlyVo::getSmType).orElse(null));
        if (dynastyInfo != null) {
            // 适配国代项目
            contractReviewMainFlowLaunchDTO.getBaseInfoDTO().setProjectNo(dynastyInfo.getProjectNo());
        }

        // 最终用户电话
        contractReviewMainFlowLaunchDTO.getContractReviewCustomerDTO().setMobile(Optional.ofNullable(projectInfo.getRight()).map(CrmCustomerVo::getMobile).orElse(null));

        // 如果是支持渠道 取授信级别和分类
        if (contractReviewMain.getSigningType() != null && contractReviewMain.getSigningType() == 0) {
            CrmAgentDetailForAgentVo agent;
            if (agentFuture != null) {
                // 如果有异步任务 等待异步的
                agent = agentFuture.join();
            } else {
                // 没有异步任务 直接取
                agent = getAgent(contractReviewMain.getProjectId());
            }
            contractReviewMainFlowLaunchDTO.getContractReviewSignContractDTO().setType(agent.getLevel());
            contractReviewMainFlowLaunchDTO.getContractReviewSignContractDTO().setCreditRating(agent.getCreditLevel());
            if (!StringUtils.isEmpty(agent.getId())) {
                JsonObject<CrmAgentVo> agentResult = remoteAgentService.getAgentInfo(agent.getId());
                if (agentResult != null && agentResult.isSuccess()) {
                    contractReviewMainFlowLaunchDTO.getContractReviewSignContractDTO()
                            .setActualCapital(Optional.ofNullable(agentResult.getObjEntity().getCrmAgentBusinessInfoVo()).map(obj -> obj.getString("actualCapital")).orElse(""));
                }
            }

        }
        // todo: 计算相关信息 超期应收个数 等等
        return contractReviewMainFlowLaunchDTO;
    }

    public ContractExecuteStatisticsVO getContractExecuteStatistics(String contractId) {
        ContractReviewMain contractReviewMain = getOne(new LambdaQueryWrapper<ContractReviewMain>().eq(ContractReviewMain::getId, contractId).eq(ContractReviewMain::getDelFlag, false));
        if (contractReviewMain == null) {
            throw new CrmException("合同不存在");
        }
        return remoteContractExecuteService.getExecuteStatisticsBySaleDept(contractReviewMain.getSaleDeptId()).getObjEntity();
    }

    @Override
    public PerformanceExecuteStatisticsVO getPerformanceExecuteStatistics(String contractId) {
        ContractReviewMain contractReviewMain = getOne(new LambdaQueryWrapper<ContractReviewMain>().eq(ContractReviewMain::getId, contractId).eq(ContractReviewMain::getDelFlag, false));
        if (contractReviewMain == null) {
            throw new CrmException("合同不存在");
        }
        return remotePerformanceExecuteService.getPerformanceExecuteStatisticsBySaleDept(contractReviewMain.getSaleDeptId()).getObjEntity();
    }

    @Override
    public List<ContractReviewMainBaseInfoDTO> getByContractNumber(String contractNumber) {
        List<ContractReviewMain> list = list(new LambdaQueryWrapper<ContractReviewMain>()
                .eq(ContractReviewMain::getContractNumber, contractNumber)
                .eq(ContractReviewMain::getDelFlag, false));
        return getContractReviewMainBaseInfoDTOS(list);
    }

    @Override
    public List<ContractReviewMainBaseInfoDTO> getByContractNumberBatch(Set<String> contractNumber) {
        if (CollectionUtils.isEmpty(contractNumber)) {
            return Collections.emptyList();
        }
        List<ContractReviewMain> list = list(new LambdaQueryWrapper<ContractReviewMain>()
                .in(ContractReviewMain::getContractNumber, contractNumber)
                .eq(ContractReviewMain::getDelFlag, false));
        return getContractReviewMainBaseInfoDTOS(list);
    }

    private List<ContractReviewMainBaseInfoDTO> getContractReviewMainBaseInfoDTOS(List<ContractReviewMain> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(item -> {
            ContractReviewMainBaseInfoDTO contractReviewMainBaseInfoDTO = HyperBeanUtils.copyProperties(item, ContractReviewMainBaseInfoDTO::new);
            contractReviewMainBaseInfoDTO.setProcessStateCode(item.getProcessState());
            return contractReviewMainBaseInfoDTO;
        }).toList();
    }

    @Override
    public List<ContractReviewMainBaseInfoDTO> getContractReviewByProjectId(String projectId) {
        return HyperBeanUtils.copyListProperties(list(new LambdaQueryWrapper<ContractReviewMain>()
                .eq(ContractReviewMain::getProjectId, projectId)
                .ne(ContractReviewMain::getProcessState, 0)
                .eq(ContractReviewMain::getDelFlag, false)), ContractReviewMainBaseInfoDTO::new);
    }

    @Override
    public List<ContractReviewMainBaseInfoDTO> getContractReviewBySaleId(String saleId, Integer status) {
        CrmAssert.hasText(saleId, "销售人员id不能为空");
        return HyperBeanUtils.copyListProperties(list(new LambdaQueryWrapper<ContractReviewMain>()
                .eq(ContractReviewMain::getSaleId, saleId)
                .eq(status != null, ContractReviewMain::getProcessState, status)
                .eq(ContractReviewMain::getDelFlag, false)), ContractReviewMainBaseInfoDTO::new);
    }

    @Override
    public List<ContractReviewMainBaseInfoDTO> getContractReviewEndByProjectId(String projectId) {
        return HyperBeanUtils.copyListProperties(list(new LambdaQueryWrapper<ContractReviewMain>()
                .eq(ContractReviewMain::getProjectId, projectId)
                .eq(ContractReviewMain::getProcessState, 2)
                .eq(ContractReviewMain::getDelFlag, false)), ContractReviewMainBaseInfoDTO::new);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSalesAgreements(Set<String> contractId, List<String> salesAgreements) {
        if (CollectionUtils.isEmpty(contractId)) {
            return;
        }
        // 循环
        for (String item : contractId) {
            ContractReviewMain main = getById(item);
            if (main == null || main.getDelFlag() == 1) {
                continue;
            }
            List<String> oldSalesAgreements = main.getSalesAgreements();
            // 合并salesAgreements 并且去掉重复的id
            salesAgreements = Stream.concat(oldSalesAgreements.stream(), salesAgreements.stream()).distinct().collect(Collectors.toList());
            String s = null;
            try {
                s = JacksonTypeHandler.getObjectMapper().writeValueAsString(salesAgreements);
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
            update(new LambdaUpdateWrapper<ContractReviewMain>().set(ContractReviewMain::getSalesAgreements, s)
                    .eq(ContractReviewMain::getId, item)
                    .eq(ContractReviewMain::getDelFlag, false));
        }
    }

    @Override
    public List<ContractReviewMainBaseInfoDTO> getContractReviewBySalesAgreementId(String salesAgreementId) {
        assert StringUtils.hasText(salesAgreementId);
        return HyperBeanUtils.copyListProperties(list(new LambdaQueryWrapper<ContractReviewMain>()
                .apply("JSON_CONTAINS(sales_agreements,CONCAT('\"',{0},'\"'))", salesAgreementId)
                .eq(ContractReviewMain::getDelFlag, false)), ContractReviewMainBaseInfoDTO::new);
    }

    @Override
    public Boolean updateContractOwner(List<HandoverContractVo> handoverContractVo) {
        // key 人 value personVo
        Map<String, HandoverContractVo> handoverContractVoMap = new HashMap<>();
        Map<String, Set<String>> contractMap = handoverContractVo.stream().filter(item -> {
            if (com.topsec.crm.framework.common.util.StringUtils.isNotEmpty(item.getReceiverId())) {
                handoverContractVoMap.put(item.getReceiverId(), item);
                return true;
            }
            return false;
        }).collect(Collectors.groupingBy(HandoverContractVo::getReceiverId,
                Collectors.collectingAndThen(Collectors.toList(), vos -> vos.stream().map(HandoverContractVo::getContractNumber).filter(Objects::nonNull).collect(Collectors.toSet()))));

        // 循环
        if (MapUtils.isEmpty(contractMap)) {
            return true;
        }
        contractMap.forEach((id, contractNumbers) -> {
            if (CollectionUtils.isEmpty(contractNumbers)) {
                return;
            }
            HandoverContractVo contractVo = handoverContractVoMap.get(id);
            String receiverId = contractVo.getReceiverId();
            String deptId = contractVo.getDeptId();
            // update
            this.update(new LambdaUpdateWrapper<ContractReviewMain>()
                    .set(ContractReviewMain::getSaleId, receiverId)
                    .set(ContractReviewMain::getSaleDeptId, deptId)
                    .in(ContractReviewMain::getContractNumber, contractNumbers));
        });
        return true;
    }

    private String getSigningCompanyName(String signingCompany) {
        JsonObject<ContractSignCompanyVO> bySignCompanyId = remoteContractReviewConfigService.getBySignCompanyId(signingCompany);
        if (bySignCompanyId.isSuccess() && bySignCompanyId.getObjEntity() != null) {
            return bySignCompanyId.getObjEntity().getCompanyNameShort();
        }
        return null;
    }

    private String getContractTypeName(String contractType) {
        JsonObject<ContractTypeVO> byContractType = remoteContractReviewConfigService.getByContractType(contractType);
        if (byContractType.isSuccess() && byContractType.getObjEntity() != null) {
            return byContractType.getObjEntity().getType();
        }
        return null;
    }

    private String getSaleName(String saleId) {
        // 销售人员姓名
        JsonObject<EmployeeVO> saleInfo = tosEmployeeClient.findById(saleId);
        if (saleInfo.isSuccess() && saleInfo.getObjEntity() != null) {
            return checkJobNoAndConcat(saleInfo.getObjEntity().getName(), saleInfo.getObjEntity().getJobNo());
        }
        return null;
    }

    private String getKeyWordName(List<String> keyWords) {
        return ListUtils.emptyIfNull(keyWords).stream().map(keywordId -> {
            JsonObject<ContractKeywordVO> result = remoteContractReviewConfigService.getByKeywordId(keywordId);
            if (result.isSuccess() && result.getObjEntity() != null) {
                return result.getObjEntity().getKeyword();
            } else {
                return "-";
            }
        }).collect(Collectors.joining(","));
    }

    private void initBaseInfoDTO(ContractReviewMainBaseInfoDTO baseInfoDTO, ContractReviewMain contractReviewMain) {
        baseInfoDTO.setSaleName(getSaleName(contractReviewMain.getSaleId()));
        // 取字典的
        baseInfoDTO.setKeyWordName(getKeyWordName(contractReviewMain.getKeyWord()));

        // 取签订公司
        baseInfoDTO.setSigningCompanyName(getSigningCompanyName(contractReviewMain.getSigningCompany()));
        baseInfoDTO.setContractTypeName(getContractTypeName(contractReviewMain.getContractType()));
        baseInfoDTO.setProcessStateCode(contractReviewMain.getProcessState());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ContractReviewMainFlowLaunchDTO contractInfoByProcessInstanceId(String processInstanceId) {
        ContractReviewMain main = getOne(new LambdaQueryWrapper<ContractReviewMain>()
                .eq(ContractReviewMain::getProcessInstanceId, processInstanceId)
                .eq(ContractReviewMain::getDelFlag, 0)
                .last("limit 1"));
        return contractInfo(main.getId(), false, true);
    }

    @Override
    public ContractReviewMainFlowLaunchDTO contractInfoBasicByProcessInstanceId(String processInstanceId) {
        ContractReviewMain main = getOne(new LambdaQueryWrapper<ContractReviewMain>()
                .eq(ContractReviewMain::getProcessInstanceId, processInstanceId)
                .eq(ContractReviewMain::getDelFlag, 0)
                .last("limit 1"));
        return contractInfo(main.getId(), false, false);
    }

    @Override
    public CrmProjectDirectlyPriceStatisticsVO initContractPriceStatistics(ContractReviewMainFlowLaunchDTO contractReviewMainFlowLaunchDTO, boolean isTile) {
        CrmContractProductVO crmContractProductVO = new CrmContractProductVO();
        List<CrmProjectProductOwnVO> owns = ListUtils.emptyIfNull(contractReviewMainFlowLaunchDTO.getContractProductOwnDTOS()).stream().map(contractProductOwnDTO -> {
            CrmProjectProductOwnVO crmProjectProductOwnVO = new CrmProjectProductOwnVO();
            crmProjectProductOwnVO.setId(contractProductOwnDTO.getId());
            crmProjectProductOwnVO.setProjectId(contractProductOwnDTO.getProjectId());
            crmProjectProductOwnVO.setProductNum(contractProductOwnDTO.getProductNum());
            // 这个地方用合同这边的 负的总价
            crmProjectProductOwnVO.setQuotedTotalPrice(contractProductOwnDTO.getQuotedTotalPrice() == null ? BigDecimal.ZERO : contractProductOwnDTO.getQuotedTotalPrice());
            crmProjectProductOwnVO.setDealTotalPrice(contractProductOwnDTO.getDealTotalPrice() == null ? BigDecimal.ZERO : contractProductOwnDTO.getDealTotalPrice());
            crmProjectProductOwnVO.setFinalTotalPrice(contractProductOwnDTO.getFinalTotalPrice() == null ? BigDecimal.ZERO : contractProductOwnDTO.getFinalTotalPrice());
            return crmProjectProductOwnVO;
        }).collect(Collectors.toList());
        if (!isTile) {
            List<CrmProjectProductOwnVO> components = ListUtils.emptyIfNull(contractReviewMainFlowLaunchDTO.getContractProductOwnDTOS()).stream().map(ContractProductOwnDTO::getContractProductOwnComponentDTOS).filter(Objects::nonNull).flatMap(List::stream).map(contractProductOwnDTO -> {
                CrmProjectProductOwnVO crmProjectProductOwnVO = new CrmProjectProductOwnVO();
                crmProjectProductOwnVO.setId(contractProductOwnDTO.getId());
                crmProjectProductOwnVO.setProjectId(contractProductOwnDTO.getProjectId());
                crmProjectProductOwnVO.setProductNum(contractProductOwnDTO.getProductNum());
                // 这个地方用合同这边的 负的总价
                crmProjectProductOwnVO.setQuotedTotalPrice(contractProductOwnDTO.getQuotedTotalPrice() == null ? BigDecimal.ZERO : contractProductOwnDTO.getQuotedTotalPrice());
                crmProjectProductOwnVO.setDealTotalPrice(contractProductOwnDTO.getDealTotalPrice() == null ? BigDecimal.ZERO : contractProductOwnDTO.getDealTotalPrice());
                crmProjectProductOwnVO.setFinalTotalPrice(contractProductOwnDTO.getFinalTotalPrice() == null ? BigDecimal.ZERO : contractProductOwnDTO.getFinalTotalPrice());
                return crmProjectProductOwnVO;
            }).collect(Collectors.toList());
            owns.addAll(components);
        }

        List<CrmProjectProductThirdVo> thirds = ListUtils.emptyIfNull(contractReviewMainFlowLaunchDTO.getContractProductThirdDTOS()).stream()
                .filter(Objects::nonNull).map(contractProductThirdDTO -> {
                    CrmProjectProductThirdVo crmProjectProductThirdVO = new CrmProjectProductThirdVo();
                    crmProjectProductThirdVO.setId(contractProductThirdDTO.getId());
                    crmProjectProductThirdVO.setProjectId(contractProductThirdDTO.getProjectId());
                    crmProjectProductThirdVO.setProductNum(contractProductThirdDTO.getProductNum());
                    crmProjectProductThirdVO.setDealTotalPrice(contractProductThirdDTO.getDealTotalPrice() == null ? BigDecimal.ZERO : contractProductThirdDTO.getDealTotalPrice());
                    crmProjectProductThirdVO.setFinalTotalPrice(contractProductThirdDTO.getFinalTotalPrice() == null ? BigDecimal.ZERO : contractProductThirdDTO.getFinalTotalPrice());
                    return crmProjectProductThirdVO;
                }).collect(Collectors.toList());

        crmContractProductVO.setOwnProjects(owns);
        crmContractProductVO.setThirdPartyProjects(thirds);
        try {
            JsonObject<CrmProjectDirectlyPriceStatisticsVO> contractPriceStatistics = remoteProjectDirectlyClient.getContractPriceStatistics(crmContractProductVO);
            if (contractPriceStatistics.isSuccess()) {
                //  赋值给dto
                contractReviewMainFlowLaunchDTO.setContractPriceStatistics((JSON) JSON.toJSON(contractPriceStatistics.getObjEntity()));
            }
            return contractPriceStatistics.getObjEntity();
        } catch (Exception e) {
            log.error("合同评审价格统计异常", e);
        }
        return new CrmProjectDirectlyPriceStatisticsVO();
    }

    @Override
    public void initRowGross(CrmProjectDirectlyPriceStatisticsVO projectDirectlyPriceStatisticsVO, List<ContractProductOwnDTO> owns, List<ContractProductThirdDTO> thirds) {
        List<CrmProjectProductOwnVO> crmProjectProduct = projectDirectlyPriceStatisticsVO.getCrmProjectProduct();
        List<CrmProjectProductThirdVo> crmProjectProductThird = projectDirectlyPriceStatisticsVO.getCrmProjectProductThird();
        Map<String, CrmProjectProductOwnVO> crmProjectProductMap = ListUtils.emptyIfNull(crmProjectProduct).stream().collect(Collectors.toMap(CrmProjectProductOwnVO::getId, v -> v, (v1, v2) -> v1));
        Map<String, CrmProjectProductThirdVo> crmProjectProductThirdMap = ListUtils.emptyIfNull(crmProjectProductThird).stream().collect(Collectors.toMap(CrmProjectProductThirdVo::getId, v -> v, (v1, v2) -> v1));
        ListUtils.emptyIfNull(owns).forEach(v -> {
            CrmProjectProductOwnVO crmProjectProductOwnVO = crmProjectProductMap.get(v.getId());
            if (crmProjectProductOwnVO != null) {
                v.setGrossMargin(crmProjectProductOwnVO.getGrossMargin());
                v.setGrossMarginRatio(crmProjectProductOwnVO.getGrossMarginRatio());
                v.setFinancialGrossMargin(crmProjectProductOwnVO.getFinancialGrossMargin());
                v.setDiscount(crmProjectProductOwnVO.getDiscount());
            }
            // 如果有配件 树结构的 直接赋值
            List<ContractProductOwnDTO> contractProductOwnComponentDTOS = v.getContractProductOwnComponentDTOS();
            ListUtils.emptyIfNull(contractProductOwnComponentDTOS).forEach(p -> {
                CrmProjectProductOwnVO crmProjectProductOwnVOComponent = crmProjectProductMap.get(p.getId());
                p.setGrossMargin(crmProjectProductOwnVOComponent.getGrossMargin());
                p.setGrossMarginRatio(crmProjectProductOwnVOComponent.getGrossMarginRatio());
                p.setFinancialGrossMargin(crmProjectProductOwnVOComponent.getFinancialGrossMargin());
                p.setDiscount(crmProjectProductOwnVOComponent.getDiscount());
            });
        });
        ListUtils.emptyIfNull(thirds).forEach(v -> {
            CrmProjectProductThirdVo crmProjectProductThirdVo = crmProjectProductThirdMap.get(v.getId());
            if (crmProjectProductThirdVo != null) {
                v.setGrossMargin(crmProjectProductThirdVo.getGrossMargin());
                v.setGrossMarginRatio(crmProjectProductThirdVo.getGrossMarginRatio());
                v.setFinancialGrossMargin(crmProjectProductThirdVo.getFinancialGrossMargin());
            }
        });
    }

    private void initSpecificInfo(List<ContractProductOwnDTO> contractProductOwnDTOS, boolean isTile) {
        List<String> productOwnIds = ListUtils.emptyIfNull(contractProductOwnDTOS).stream().map(ContractProductOwnDTO::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productOwnIds)) {
            return;
        }
        if (!isTile) {
            ListUtils.emptyIfNull(contractProductOwnDTOS).stream()
                    .map(ContractProductOwnDTO::getContractProductOwnComponentDTOS)
                    .filter(Objects::nonNull).flatMap(List::stream).forEach(contractProductOwnDTO -> {
                        productOwnIds.add(contractProductOwnDTO.getId());
                    });
        }
        List<ContractReviewSpecialCode> list = contractReviewSpecialCodeService.list(new LambdaQueryWrapper<ContractReviewSpecialCode>()
                .in(ContractReviewSpecialCode::getContractReviewId, productOwnIds)
                .eq(ContractReviewSpecialCode::getDelFlag, 0));
        Map<String, ContractReviewSpecialCode> specialCodeMap = list.stream().collect(Collectors.toMap(ContractReviewSpecialCode::getProductOwnId, v -> v, (newV, oldV) -> newV));
        contractProductOwnDTOS.forEach(own -> {
            ContractReviewSpecialCode contractReviewSpecialCode = specialCodeMap.get(own.getId());
            if (contractReviewSpecialCode != null) {
                own.setCrmProjectProductMaintain(HyperBeanUtils.copyPropertiesByJackson(contractReviewSpecialCode, CrmProjectProductMaintainVO.class));
            }
            if (!CollectionUtils.isEmpty(own.getContractProductOwnComponentDTOS())) {
                own.getContractProductOwnComponentDTOS().forEach(component -> {
                    ContractReviewSpecialCode componentSpecialCode = specialCodeMap.get(component.getId());
                    if (componentSpecialCode != null) {
                        component.setCrmProjectProductMaintain(HyperBeanUtils.copyPropertiesByJackson(componentSpecialCode, CrmProjectProductMaintainVO.class));
                    }
                });
            }
        });

    }

    @Override
    public SalesSelectDTO salesSelect(String projectId) {
        SalesSelectDTO salesSelectDTO = new SalesSelectDTO();
        // 取业绩协商的接口， 根据项目ID 取是否有有效的业绩协商，如果有，返回发起人
        JsonObject<CrmProjectPerformanceNegotiationVo> result = remoteProjectPerformanceNegotiationClient.getCrmProjectPerformanceNegotiationByProjectId(projectId);
        if (!result.isSuccess() || result.getObjEntity() == null) {
            // 无业绩协商 取项目成员集合
            JsonObject<List<String>> memberIdByProjectId = remoteProjectMemberClient.getMemberIdByProjectId(projectId);
            JsonObject<List<PersonVO>> listJsonObject = remoteAccountService.queryPersonByIds(memberIdByProjectId.getObjEntity());
            salesSelectDTO.setPersonVOList(ListUtils.emptyIfNull(listJsonObject.getObjEntity()).stream().map(SalesSelectDTO.PersonSelect::new).collect(Collectors.toList()));
            salesSelectDTO.setHasPerformanceNegotiated(false);
            return salesSelectDTO;
        }
        // 有业绩协商，取业绩协商的发起人
        salesSelectDTO.setHasPerformanceNegotiated(true);
        List<FlowPerson> flowPeople = AccountAccquireUtils.convertGetAccountByPersonId(Collections.singletonList(result.getObjEntity().getNegotiationPersonId()));
        salesSelectDTO.setPersonVOList(ListUtils.emptyIfNull(flowPeople).stream().map(SalesSelectDTO.PersonSelect::new).collect(Collectors.toList()));
        return salesSelectDTO;
    }

    @Override
    public List<HomeNameValue<String, String>> linkmanSelect(String projectId) {
        Pair<CrmProjectDirectlyVo, CrmCustomerVo> customerInfo = getCustomerInfo(projectId);
        if (customerInfo.getRight() == null) {
            return Collections.emptyList();
        }
        return customerInfo.getRight().getLinkmans().stream().map(crmLinkmanVo -> new HomeNameValue<>(crmLinkmanVo.getId(), crmLinkmanVo.getName())).collect(Collectors.toList());
    }

    @Override
    public boolean changeContractNumber(ContractReviewMainBaseInfoDTO contractReviewMainBaseInfoDTO) {
        JsonObject<Boolean> result = remoteContractReviewService.checkContractNumberExist(contractReviewMainBaseInfoDTO.getContractNumber());
        if (!result.isSuccess() || !result.getObjEntity()) {
            throw new CrmException("合同号无效!");
        }
        ContractReviewMain one = getOne(new LambdaQueryWrapper<ContractReviewMain>().eq(ContractReviewMain::getId, contractReviewMainBaseInfoDTO.getId()));
        if (one != null)
            remoteAgentRebateService.updateContractNumByReviewNum(one.getProcessNumber(), contractReviewMainBaseInfoDTO.getContractNumber());
        return update(new LambdaUpdateWrapper<ContractReviewMain>().set(ContractReviewMain::getContractNumber, contractReviewMainBaseInfoDTO.getContractNumber())
                .eq(ContractReviewMain::getId, contractReviewMainBaseInfoDTO.getId())
                .eq(ContractReviewMain::getDelFlag, 0));
    }

    @Override
    public boolean changeIsClassified(ContractReviewMainBaseInfoDTO contractReviewMainBaseInfoDTO) {
        return update(new LambdaUpdateWrapper<ContractReviewMain>().set(ContractReviewMain::getIsClassified, contractReviewMainBaseInfoDTO.getIsClassified())
                .eq(ContractReviewMain::getId, contractReviewMainBaseInfoDTO.getId())
                .eq(ContractReviewMain::getDelFlag, 0));
    }

    @Override
    public boolean changesBusinessType(ContractReviewMainBaseInfoDTO contractReviewMainBaseInfoDTO) {
        // todo 分布式事务 反写项目
        ContractReviewMain contract = getOne(new LambdaQueryWrapper<ContractReviewMain>()
                .eq(ContractReviewMain::getId, contractReviewMainBaseInfoDTO.getId())
                .eq(ContractReviewMain::getDelFlag, 0)
                .last("limit 1"));

        remoteProjectDirectlyClient.updateBusinessType(contractReviewMainBaseInfoDTO.getBusinessType(), contract.getProjectId());

        return update(new LambdaUpdateWrapper<ContractReviewMain>().set(ContractReviewMain::getBusinessType, contractReviewMainBaseInfoDTO.getBusinessType())
                .eq(ContractReviewMain::getId, contractReviewMainBaseInfoDTO.getId())
                .eq(ContractReviewMain::getDelFlag, 0));
    }

    @Override
    public boolean changeBaseInfoByProcessInstanceId(ContractReviewMainBaseInfoDTO baseInfoDTO) {
        ContractReviewMain contractReviewMain = getByProcessInstanceId(baseInfoDTO.getProcessInstanceId());
        if (contractReviewMain == null) {
            throw new CrmException("合同评审流程不存在");
        }
        baseInfoDTO.setId(contractReviewMain.getId());
        return updateById(HyperBeanUtils.copyProperties(baseInfoDTO, ContractReviewMain::new));
    }

    @Override
    public boolean changeStep(Integer step, String contractId) {
        ContractReviewMain contractReviewMain = getOne(new LambdaQueryWrapper<ContractReviewMain>()
                .eq(ContractReviewMain::getId, contractId)
                .eq(ContractReviewMain::getDelFlag, 0)
                .last("limit 1"));
        if (contractReviewMain != null) {
            contractReviewMain.setStep(step);
            updateById(contractReviewMain);
            return true;
        }
        return false;
    }

    @Override
    public ContractBasicInfoDTO contractBasicInfo(String contractId, Boolean needProcessInfo) {
        ContractBasicInfoDTO baseInfoDTO = new ContractBasicInfoDTO();
        // 合同基本信息
        ContractReviewMain contractReviewMain = getOne(new LambdaQueryWrapper<ContractReviewMain>().eq(ContractReviewMain::getId, contractId).eq(ContractReviewMain::getDelFlag, false));
        if (contractReviewMain == null) {
            throw new CrmException("合同不存在");
        }
        CompletableFuture<ContractReviewCustomer> contractReviewCustomerCompletableFuture = FlowThreadExecutor.supplyAsync(() ->
                contractReviewCustomerService.getOne(new LambdaQueryWrapper<ContractReviewCustomer>().eq(ContractReviewCustomer::getContractReviewMainId, contractId)
                        .eq(ContractReviewCustomer::getDelFlag, false)));

        CompletableFuture<List<ContractReviewNoticeArrive>> contractReviewNoticeArriveCompletableFuture = FlowThreadExecutor.supplyAsync(() ->
                contractReviewNoticeArriveService.list(new LambdaQueryWrapper<ContractReviewNoticeArrive>()
                        .eq(ContractReviewNoticeArrive::getContractReviewMainId, contractId).eq(ContractReviewNoticeArrive::getDelFlag, false)));

        CompletableFuture<ContractReviewSignContract> contractReviewSignContract = FlowThreadExecutor.supplyAsync(() ->
                contractReviewSignContractService.getOne(new LambdaQueryWrapper<ContractReviewSignContract>()
                        .eq(ContractReviewSignContract::getContractReviewMainId, contractId).eq(ContractReviewSignContract::getDelFlag, 0)));
        baseInfoDTO.setBaseInfoDTO(HyperBeanUtils.copyProperties(contractReviewMain, ContractReviewMainBaseInfoDTO::new));
        initBaseInfoDTO(baseInfoDTO.getBaseInfoDTO(), contractReviewMain);
        // 进度
        if (contractReviewMain.getProcessInstanceId() != null && needProcessInfo) {
            Map<String, Set<ApproveNode>> nodesById = tfsNodeClient.queryNodeByProcessInstanceIdList(Collections.singletonList(contractReviewMain.getProcessInstanceId())).getObjEntity();
            baseInfoDTO.getBaseInfoDTO().setProcessState(nodesById.get(contractReviewMain.getProcessInstanceId()));
        }

        baseInfoDTO.setContractReviewNoticeArriveDTO(HyperBeanUtils.copyListProperties(contractReviewNoticeArriveCompletableFuture.join(), ContractReviewNoticeArriveDTO::new));
        baseInfoDTO.setContractReviewSignContractDTO(HyperBeanUtils.copyProperties(contractReviewSignContract.join(), () -> ContractReviewSignContractDTO.builder().build()));
        baseInfoDTO.setContractReviewCustomerDTO(HyperBeanUtils.copyProperties(contractReviewCustomerCompletableFuture.join(), () -> ContractReviewCustomerDTO.builder().build()));
        return baseInfoDTO;
    }

    // 串行取值，一键代下单需要使用
    private ContractBasicInfoDTO contractBasicInfoSerial(String contractId) {
        ContractBasicInfoDTO baseInfoDTO = new ContractBasicInfoDTO();
        // 合同基本信息
        ContractReviewMain contractReviewMain = getOne(new LambdaQueryWrapper<ContractReviewMain>().eq(ContractReviewMain::getId, contractId).eq(ContractReviewMain::getDelFlag, false));

        ContractReviewCustomer contractReviewCustomer = contractReviewCustomerService.getOne(new LambdaQueryWrapper<ContractReviewCustomer>()
                .eq(ContractReviewCustomer::getContractReviewMainId, contractId)
                .eq(ContractReviewCustomer::getDelFlag, false));

        List<ContractReviewNoticeArrive> contractReviewNoticeArrive = contractReviewNoticeArriveService.list(new LambdaQueryWrapper<ContractReviewNoticeArrive>()
                .eq(ContractReviewNoticeArrive::getContractReviewMainId, contractId).eq(ContractReviewNoticeArrive::getDelFlag, false));

        ContractReviewSignContract contractReviewSignContract = contractReviewSignContractService.getOne(new LambdaQueryWrapper<ContractReviewSignContract>()
                .eq(ContractReviewSignContract::getContractReviewMainId, contractId).eq(ContractReviewSignContract::getDelFlag, 0));
        baseInfoDTO.setBaseInfoDTO(HyperBeanUtils.copyProperties(contractReviewMain, ContractReviewMainBaseInfoDTO::new));
        baseInfoDTO.getBaseInfoDTO().setSigningCompanyName(getSigningCompanyName(contractReviewMain.getSigningCompany()));
        baseInfoDTO.getBaseInfoDTO().setContractTypeName(getContractTypeName(contractReviewMain.getContractType()));

        // 排序 把甲方排前面
        if (!CollectionUtils.isEmpty(contractReviewNoticeArrive)) {
            contractReviewNoticeArrive.sort(Comparator.comparing(ContractReviewNoticeArrive::getNoticeType));
        }
        baseInfoDTO.setContractReviewNoticeArriveDTO(HyperBeanUtils.copyListProperties(contractReviewNoticeArrive, ContractReviewNoticeArriveDTO::new));
        baseInfoDTO.setContractReviewSignContractDTO(HyperBeanUtils.copyProperties(contractReviewSignContract, () -> ContractReviewSignContractDTO.builder().build()));
        baseInfoDTO.setContractReviewCustomerDTO(HyperBeanUtils.copyProperties(contractReviewCustomer, () -> ContractReviewCustomerDTO.builder().build()));
        return baseInfoDTO;
    }

    @Override
    public ContractReviewMainBaseInfoDTO getByContractId(String contractId) {
        ContractReviewMain contractReviewMain = getOne(new LambdaQueryWrapper<ContractReviewMain>()
                .eq(ContractReviewMain::getId, contractId)
                .eq(ContractReviewMain::getDelFlag, 0)
                .last("limit 1"));
        ContractReviewMainBaseInfoDTO contractReviewMainBaseInfoDTO = HyperBeanUtils.copyProperties(contractReviewMain, ContractReviewMainBaseInfoDTO::new);
        initBaseInfoDTO(contractReviewMainBaseInfoDTO, contractReviewMain);
        return contractReviewMainBaseInfoDTO;
    }

    @Override
    public ContractReviewMain getByProcessInstanceId(String processInstanceId) {
        return getOne(new LambdaQueryWrapper<ContractReviewMain>()
                .eq(ContractReviewMain::getProcessInstanceId, processInstanceId)
                .eq(ContractReviewMain::getDelFlag, 0)
                .last("limit 1"));
    }

    @Override
    public List<ContractReviewMain> getByProcessInstanceIdBatch(Set<String> processInstanceIds) {
        if (CollectionUtils.isEmpty(processInstanceIds)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<ContractReviewMain>()
                .in(ContractReviewMain::getProcessInstanceId, processInstanceIds)
                .eq(ContractReviewMain::getDelFlag, 0));
    }

    @Override
    public List<ContractReviewMain> getByPerformanceReportId(String performanceReportId, String contractProcessNumber) {
        return list(new LambdaQueryWrapper<ContractReviewMain>()
                .eq(ContractReviewMain::getPerformanceReportId, performanceReportId)
                .eq(StringUtils.hasText(contractProcessNumber), ContractReviewMain::getProcessNumber, contractProcessNumber)
                .eq(ContractReviewMain::getDelFlag, 0));
    }

    @Override
    public List<ContractReviewMain> getByProcessInstanceIds(List<String> processInstanceIds) {
        if (CollectionUtils.isEmpty(processInstanceIds)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<ContractReviewMain>()
                .in(ContractReviewMain::getProcessInstanceId, processInstanceIds)
                .eq(ContractReviewMain::getDelFlag, 0));
    }

    @Override
    public List<ContractReviewMain> getByPerformanceReportId(String performanceReportId) {
        return list(new LambdaQueryWrapper<ContractReviewMain>()
                .in(ContractReviewMain::getPerformanceReportId, performanceReportId)
                .eq(ContractReviewMain::getDelFlag, 0));
    }


    private void formPredicateInit() {
        // 1.视同销售当选择“是”时，合同形式必须选择“无纸制合同”
        formPredicates.add(new ContractPredicate("视同销售当选择“是”时，合同形式必须选择“电子合同”", 1, Collections.singletonList(ContractPredicate.CONTRACT_INFO), (contractReviewMainFlowLaunchDTO, projectInfoDTO) -> {
            if (contractReviewMainFlowLaunchDTO.getBaseInfoDTO().getRegardSale()) {
                return contractReviewMainFlowLaunchDTO.getBaseInfoDTO().getContractForm() == 2;
            }
            return true;
        }));

        // 2.项目中有业绩协商，这里销售为业绩协商发起人（不可修改） 无业绩协商，可以在项目成员中选择必填，单选
        formPredicates.add(new ContractPredicate("项目中有业绩协商，销售为业绩协商发起人,无业绩协商，可以在项目成员中选择", 2, Collections.singletonList(ContractPredicate.CONTRACT_INFO), (contractReviewMainFlowLaunchDTO, projectInfoDTO) -> {
            // 取业绩协商的接口 是否有业绩协商，业绩协商发起人、 项目成员
            String projectId = contractReviewMainFlowLaunchDTO.getBaseInfoDTO().getProjectId();
            String saleId = contractReviewMainFlowLaunchDTO.getBaseInfoDTO().getSaleId();
            JsonObject<CrmProjectPerformanceNegotiationVo> result = remoteProjectPerformanceNegotiationClient.getCrmProjectPerformanceNegotiationByProjectId(projectId);
            if (!result.isSuccess() || result.getObjEntity() == null) {
                // 无业绩协商 必须是项目成员中的
                JsonObject<List<String>> memberIdByProjectId = remoteProjectMemberClient.getMemberIdByProjectId(projectId);
                return Optional.ofNullable(memberIdByProjectId.getObjEntity()).orElse(Collections.emptyList()).contains(saleId);
            }
            return Optional.ofNullable(result.getObjEntity().getNegotiationPersonId()).orElse(Strings.EMPTY).equals(saleId);
        }));

        // 3.当选择出货的产品关联了重点行业序列号时，默认取重点行业序列号对应的单位为签订公司且不可修改；若没有关联重点行业的序列号，则判断来源是否是字典下面的签订公司信息
        formPredicates.add(new ContractPredicate("当选择出货的产品关联了重点行业序列号时，默认取重点行业序列号对应的单位为签订公司且不可修改", 3, Collections.singletonList(ContractPredicate.CONTRACT_INFO), (contractReviewMainFlowLaunchDTO, projectInfoDTO) -> {
            // todo: 重点行业序列号 判断逻辑


            // 没有重点行业序列号的 判断来源是否是字典下面的签订公司
            String signingCompany = contractReviewMainFlowLaunchDTO.getBaseInfoDTO().getSigningCompany();
            JsonObject<ContractSignCompanyVO> result = remoteContractReviewConfigService.getBySignCompanyId(signingCompany);
            return result.isSuccess();
        }));

        // 4.无纸质合同只能选择5和6的代码无纸质合同不需要上传合同
        formPredicates.add(new ContractPredicate("无纸质合同只能选择物料代码为5和6开头的代码,且不需要上传合同，不需要填写付款条款", 4, Collections.singletonList(ContractPredicate.PRODUCT_INFO), (contractReviewMainFlowLaunchDTO, projectInfoDTO) -> {
            if (contractReviewMainFlowLaunchDTO.getBaseInfoDTO().getContractForm() == 2 && !contractReviewMainFlowLaunchDTO.getBaseInfoDTO().getRegardSale()) {
                return contractReviewMainFlowLaunchDTO.getContractProductOwnDTOS().
                        stream().allMatch(contractProductOwnDTO ->
                                Optional.ofNullable(contractProductOwnDTO.getStuffCode()).orElse(Strings.EMPTY).startsWith("5") ||
                                        Optional.ofNullable(contractProductOwnDTO.getStuffCode()).orElse(Strings.EMPTY).startsWith("6")
                        ) &&
                        contractReviewMainFlowLaunchDTO.getContractProductOwnDTOS().
                                stream().map(ContractProductOwnDTO::getContractProductOwnComponentDTOS).flatMap(List::stream).allMatch(contractProductOwnComponentDTO ->
                                        Optional.ofNullable(contractProductOwnComponentDTO.getStuffCode()).orElse(Strings.EMPTY).startsWith("5") ||
                                                Optional.ofNullable(contractProductOwnComponentDTO.getStuffCode()).orElse(Strings.EMPTY).startsWith("6")
                                ) && CollectionUtils.isEmpty(contractReviewMainFlowLaunchDTO.getContractProductThirdDTOS());
            }
            return true;
        }));

        // 5.最终用户、一级行业、二级行业 是否和项目中的一致
        formPredicates.add(new ContractPredicate("最终用户、一级行业、二级行业和项目中的不一致", 5, Collections.singletonList(ContractPredicate.CONTRACT_INFO), (contractReviewMainFlowLaunchDTO, projectInfoDTO) -> {
            // 根据项目Id取项目的信息
            CrmCustomerVo crmCustomerVo = projectInfoDTO.getRight();
            ContractReviewCustomerDTO contractReviewCustomerDTO = contractReviewMainFlowLaunchDTO.getContractReviewCustomerDTO();
            return Optional.ofNullable(crmCustomerVo).map(CrmCustomerVo::getName).orElse(Strings.EMPTY).equals(contractReviewCustomerDTO.getCustomerName()) &&
                    Optional.ofNullable(crmCustomerVo).map(CrmCustomerVo::getIndustryId).orElse(Strings.EMPTY).equals(contractReviewCustomerDTO.getFirstIndustry()) &&
                    Optional.ofNullable(crmCustomerVo).map(CrmCustomerVo::getIndustryIdTwo).orElse(Strings.EMPTY).equals(contractReviewCustomerDTO.getSecondIndustry());
        }));

        // 6. 用户联系人数据来源于最终用户下的联系人进行选择，只能选择申请人录入的联系人
        formPredicates.add(new ContractPredicate("用户联系人数据来源于最终用户下的联系人进行选择，只能选择申请人录入的联系人", 6, Collections.singletonList(ContractPredicate.CONTRACT_INFO), (contractReviewMainFlowLaunchDTO, projectInfoDTO) -> {
            // 判断联系人是否来源于最终用户下的联系人，并且是这个人录入的
            CrmCustomerVo crmCustomerVo = projectInfoDTO.getRight();
            return Optional.ofNullable(crmCustomerVo).map(CrmCustomerVo::getLinkmans).orElse(Collections.emptyList()).stream().anyMatch(crmLinkmanVo ->
                    Optional.ofNullable(crmLinkmanVo.getId()).orElse(Strings.EMPTY).equals(contractReviewMainFlowLaunchDTO.getContractReviewCustomerDTO().getCustomerContactId())
            );
        }));

        // 7.签约单位相关信息 统一信用代码判断
        formPredicates.add(new ContractPredicate("签约单位信息有误", 7, Collections.singletonList(ContractPredicate.CONTRACT_INFO), (contractReviewMainFlowLaunchDTO, projectInfoDTO) -> {
            CrmCustomerVo crmCustomerVo = projectInfoDTO.getRight();
            // 签约模式是支持渠道
            if (Optional.ofNullable(projectInfoDTO.getLeft()).map(CrmProjectDirectlyVo::getSigningType).orElse(-1) == 0) {
                CrmAgentDetailForAgentVo agentNode = getAgent(contractReviewMainFlowLaunchDTO.getBaseInfoDTO().getProjectId());
                // 统一信用代码取渠道的判断
                JsonObject<CrmAgentVo> agentInfo = remoteAgentService.getAgentInfo(agentNode.getId());
                if (!agentInfo.isSuccess()) {
                    return false;
                }
                if (contractReviewMainFlowLaunchDTO.getContractReviewSignContractDTO().getContractCreditCode() == null) {
                    return true;
                }
                return Optional.of(agentNode).map(CrmAgentDetailForAgentVo::getAgentName).orElse(Strings.EMPTY).equals(contractReviewMainFlowLaunchDTO.getContractReviewSignContractDTO().getContractCompanyName())
                        && Optional.ofNullable(agentInfo.getObjEntity()).map(CrmAgentVo::getCrmAgentBusinessInfoVo).map(obj -> obj.getString("creditCode")).orElse(Strings.EMPTY).equals(contractReviewMainFlowLaunchDTO.getContractReviewSignContractDTO().getContractCreditCode());
            }

            // 签约模式 是 我们直签
            if (Optional.ofNullable(projectInfoDTO.getLeft()).map(CrmProjectDirectlyVo::getSigningType).orElse(-1) == 1) {
                if (contractReviewMainFlowLaunchDTO.getContractReviewSignContractDTO().getContractCreditCode() == null) {
                    return true;
                }
                if (crmCustomerVo.getBusinessInfo() != null) {
                    return Optional.ofNullable(crmCustomerVo.getBusinessInfo().getString("creditCode")).orElse(Strings.EMPTY).equals(contractReviewMainFlowLaunchDTO.getContractReviewSignContractDTO().getContractCreditCode());
                }
                // 判断是否和客户信息的一致
                return Optional.ofNullable(crmCustomerVo.getName()).orElse(Strings.EMPTY).equals(contractReviewMainFlowLaunchDTO.getContractReviewSignContractDTO().getContractCompanyName());
            }
            return true;
        }));

        // 8.合同分类、关键字来源判断
        formPredicates.add(new ContractPredicate("合同分类、关键字来源有误", 8, Collections.singletonList(ContractPredicate.CONTRACT_INFO), (contractReviewMainFlowLaunchDTO, projectInfoDTO) -> {
            JsonObject<ContractTypeVO> resultContractType = remoteContractReviewConfigService.getByContractType(contractReviewMainFlowLaunchDTO.getBaseInfoDTO().getContractType());
            boolean keyWordSource = ListUtils.emptyIfNull(contractReviewMainFlowLaunchDTO.getBaseInfoDTO().getKeyWord()).stream().allMatch(keyword -> {
                JsonObject<ContractKeywordVO> resultContractKeyword = remoteContractReviewConfigService.getByKeywordId(keyword);
                return resultContractKeyword.isSuccess();
            });
            return resultContractType.isSuccess() && keyWordSource;
        }));

        // 9. 当选择的“合同币种”为非人民币时，显示原币金额输入框；选择为人民币时，隐藏原币金额输入框
        formPredicates.add(new ContractPredicate("合同币种非人民币时，原币金额必填", 9, Collections.singletonList(ContractPredicate.CONTRACT_INFO), (contractReviewMainFlowLaunchDTO, projectInfoDTO) -> {
            if (contractReviewMainFlowLaunchDTO.getContractReviewCustomerDTO().getContractCurrency() != 0) {
                return contractReviewMainFlowLaunchDTO.getContractReviewCustomerDTO().getPrepaymentOther() != null;
            }
            return true;
        }));

        // 10.产品的税率和项目中的判断，如果不一致则需要提示
//        formPredicates.add(new ContractPredicate("产品税率和项目中的不一致", 10, Collections.singletonList(ContractPredicate.CONTRACT_INFO), (contractReviewMainFlowLaunchDTO, projectInfoDTO) -> {
//            // 产品税率和项目中的是否一致
//            return contractReviewMainFlowLaunchDTO.getContractProductOwnDTOS()
//                    .stream().allMatch(contractProductOwnDTO ->
//                            contractProductOwnDTO.getTaxRate().compareTo(contractProductOwnDTO.getTaxRateProject()) == 0
//                    ) &&
//                    contractReviewMainFlowLaunchDTO.getContractProductOwnDTOS()
//                            .stream().map(ContractProductOwnDTO::getContractProductOwnComponentDTOS).flatMap(List::stream).allMatch(contractProductOwnComponentDTO ->
//                                    contractProductOwnComponentDTO.getTaxRate().compareTo(contractProductOwnComponentDTO.getTaxRateProject()) == 0
//                            ) &&
//                    contractReviewMainFlowLaunchDTO.getContractProductThirdDTOS().stream().allMatch(contractProductThirdDTO -> contractProductThirdDTO.getTaxRate().compareTo(contractProductThirdDTO.getTaxRateProject()) == 0);
//        }));

        // 11.保修时间校验
        formPredicates.add(new ContractPredicate("保修时间校验不符", 11, Collections.singletonList(ContractPredicate.CONTRACT_INFO), (contractReviewMainFlowLaunchDTO, projectInfoDTO) -> {
            // 保修时间校验
            BiPredicate<Integer, Integer> inTimePredicate = (diff, total) -> Math.abs(diff - total) <= 3;
            return contractReviewMainFlowLaunchDTO.getContractProductOwnDTOS().stream().allMatch(contractProductOwnDTO -> {
                if (contractProductOwnDTO.getProductPeriodStart() == null || contractProductOwnDTO.getProductPeriodEnd() == null) {
                    throw new CrmException("保修时间不能为空");
                }
                int diff = (int) ChronoUnit.DAYS.between(contractProductOwnDTO.getProductPeriodStart(), contractProductOwnDTO.getProductPeriodEnd());
                BigDecimal productPeriod = contractProductOwnDTO.getProductPeriod();
                productPeriod = productPeriod == null ? BigDecimal.ZERO : productPeriod;
                BigDecimal periodDay = productPeriod.multiply(new BigDecimal(ONE_MONTH)).setScale(0, RoundingMode.HALF_UP);
                int total = periodDay.intValue();
                return inTimePredicate.test(diff, total);
            }) && contractReviewMainFlowLaunchDTO.getContractProductOwnDTOS().stream()
                    .map(ContractProductOwnDTO::getContractProductOwnComponentDTOS).flatMap(List::stream).allMatch(contractProductOwnComponentDTO -> {
                        if (contractProductOwnComponentDTO.getProductPeriodStart() == null || contractProductOwnComponentDTO.getProductPeriodEnd() == null) {
                            throw new CrmException("保修时间不能为空");
                        }
                        int diff = (int) ChronoUnit.DAYS.between(contractProductOwnComponentDTO.getProductPeriodStart(), contractProductOwnComponentDTO.getProductPeriodEnd());
                        BigDecimal productPeriod = contractProductOwnComponentDTO.getProductPeriod();
                        productPeriod = productPeriod == null ? BigDecimal.ZERO : productPeriod;
                        BigDecimal periodDay = productPeriod.multiply(new BigDecimal(ONE_MONTH)).setScale(0, RoundingMode.HALF_UP);
                        int total = periodDay.intValue();
                        return inTimePredicate.test(diff, total);
                    }) &&
                    contractReviewMainFlowLaunchDTO.getContractProductThirdDTOS().stream().allMatch(contractProductThirdDTO -> {
                        if (contractProductThirdDTO.getProductPeriodStart() == null || contractProductThirdDTO.getProductPeriodEnd() == null) {
                            throw new CrmException("保修时间不能为空");
                        }
                        int diff = (int) ChronoUnit.DAYS.between(contractProductThirdDTO.getProductPeriodStart(), contractProductThirdDTO.getProductPeriodEnd());
                        BigDecimal productPeriod = contractProductThirdDTO.getProductPeriod();
                        productPeriod = productPeriod == null ? BigDecimal.ZERO : productPeriod;
                        BigDecimal periodDay = productPeriod.multiply(new BigDecimal(ONE_MONTH)).setScale(0, RoundingMode.HALF_UP);
                        int total = periodDay.intValue();
                        return inTimePredicate.test(diff, total);
                    });
        }));

    }

    /**
     * 获取项目中的客户信息
     *
     * @param projectId 项目id
     * @return 项目中的信息
     */
    private Pair<CrmProjectDirectlyVo, CrmCustomerVo> getCustomerInfo(String projectId) {
        JsonObject<CrmProjectDirectlyVo> result = remoteProjectDirectlyClient.getProjectInfo(projectId);
        if (!result.isSuccess()) {
            throw new CrmException("项目信息为空！");
        }
        String customerId = Optional.ofNullable(result.getObjEntity().getCrmProjectSigningInfo()).
                map(CrmProjectSigningInfoVo::getCustomerId).orElse(Strings.EMPTY);
        if (Strings.isEmpty(customerId)) {
            return Pair.of(result.getObjEntity(), new CrmCustomerVo());
        }
        RequestContextHolder.setRequestAttributes(RequestContextHolder.getRequestAttributes(), true);
        String currentPersonId;
        try {
            currentPersonId = UserInfoHolder.getCurrentPersonId();
        } catch (Exception e) {
            currentPersonId = "";
        }
        JsonObject<CrmCustomerVo> customerInfo = remoteCustomerService.getCustomerInfo(customerId, currentPersonId, false);
        if (!customerInfo.isSuccess()) {
            throw new CrmException("客户信息为空！");
        }
        return Pair.of(result.getObjEntity(), customerInfo.getObjEntity());
    }

    /**
     * 保存关联表
     *
     * @param contractReviewMainFlowLaunchDTO dto
     * @param mainId                          主表id
     */
    private void saveRelateTable(ContractReviewMainFlowLaunchDTO contractReviewMainFlowLaunchDTO, String mainId) {
        ContractReviewCustomer contractReviewCustomer = HyperBeanUtils.copyProperties(contractReviewMainFlowLaunchDTO.getContractReviewCustomerDTO(), ContractReviewCustomer::new);
        contractReviewCustomer.setContractReviewMainId(mainId);
        contractReviewCustomerService.save(contractReviewCustomer);

        List<ContractReviewNoticeArrive> contractReviewNoticeArrive = HyperBeanUtils.copyListProperties(contractReviewMainFlowLaunchDTO.getContractReviewNoticeArriveDTO(), ContractReviewNoticeArrive::new);
        contractReviewNoticeArrive.forEach(notice -> {
            notice.setContractReviewMainId(mainId);
        });
        contractReviewNoticeArriveService.saveBatch(contractReviewNoticeArrive);

        ContractReviewSignContract contractReviewSignContract = HyperBeanUtils.copyProperties(contractReviewMainFlowLaunchDTO.getContractReviewSignContractDTO(), ContractReviewSignContract::new);
        contractReviewSignContract.setContractReviewMainId(mainId);
        // 写入催款分类
        // 判断最终用户和签约单位是否一致
        setRemindClassification(contractReviewMainFlowLaunchDTO, contractReviewSignContract);

        contractReviewSignContractService.save(contractReviewSignContract);
    }

    private void updateRelateTable(ContractReviewMainFlowLaunchDTO contractReviewMainFlowLaunchDTO) {
        String contractId = contractReviewMainFlowLaunchDTO.getBaseInfoDTO().getId();
        // 根据合同id 修改
        ContractReviewCustomer contractReviewCustomer = HyperBeanUtils.copyProperties(contractReviewMainFlowLaunchDTO.getContractReviewCustomerDTO(), ContractReviewCustomer::new);
        contractReviewCustomerService.update(contractReviewCustomer, new LambdaUpdateWrapper<ContractReviewCustomer>()
                .eq(ContractReviewCustomer::getContractReviewMainId, contractId)
                .eq(ContractReviewCustomer::getDelFlag, 0));

        List<ContractReviewNoticeArrive> contractReviewNoticeArrive = HyperBeanUtils.copyListProperties(contractReviewMainFlowLaunchDTO.getContractReviewNoticeArriveDTO(), ContractReviewNoticeArrive::new);
        contractReviewNoticeArrive.forEach(notice -> {
            notice.setContractReviewMainId(contractId);
        });
        contractReviewNoticeArriveService.updateBatchById(contractReviewNoticeArrive);

        ContractReviewSignContract contractReviewSignContract = HyperBeanUtils.copyProperties(contractReviewMainFlowLaunchDTO.getContractReviewSignContractDTO(), ContractReviewSignContract::new);
        // 写入催款分类
        // 判断最终用户和签约单位是否一致
        setRemindClassification(contractReviewMainFlowLaunchDTO, contractReviewSignContract);

        contractReviewSignContractService.update(contractReviewSignContract, new LambdaUpdateWrapper<ContractReviewSignContract>()
                .eq(ContractReviewSignContract::getContractReviewMainId, contractId)
                .eq(ContractReviewSignContract::getDelFlag, 0));
    }

    private void setRemindClassification(ContractReviewMainFlowLaunchDTO contractReviewMainFlowLaunchDTO, ContractReviewSignContract contractReviewSignContract) {
        if (contractReviewMainFlowLaunchDTO.getContractReviewCustomerDTO().getCustomerName() != null) {
            if (contractReviewMainFlowLaunchDTO.getContractReviewCustomerDTO().getCustomerName().equals(contractReviewMainFlowLaunchDTO.getContractReviewSignContractDTO().getContractCompanyName())) {
                contractReviewSignContract.setRemindClassification(ContractReviewSignContractDTO.DIRECT);
            } else {
                // 取系统集成商里面的级别 如果是一级二级 就是系统集成商，否则是普通经销商
                JsonObject<SystemIntegratorVO> byAgentName = remoteSystemIntegratorService.getByAgentName(contractReviewMainFlowLaunchDTO.getContractReviewSignContractDTO().getContractCompanyName());
                if (byAgentName.isSuccess() && (SystemIntegratorVO.CERTIFICATE_LEVEL_ONE.equals(byAgentName.getObjEntity().getCertification()) || SystemIntegratorVO.CERTIFICATE_LEVEL_TWO.equals(byAgentName.getObjEntity().getCertification()))) {
                    contractReviewSignContract.setRemindClassification(ContractReviewSignContractDTO.SYSTEM_INTEGRATOR);
                } else {
                    contractReviewSignContract.setRemindClassification(ContractReviewSignContractDTO.ORDINARY_DEALER);
                }
            }
        }
    }

    private boolean hasImportantSerial(List<ContractProductOwnDTO> contractProductOwnDTOS) {
        Predicate<ContractProductOwnDTO> importantPredicate = productOwn -> {
            List<CrmProjectProductSnVO> crmProjectProductSn = productOwn.getCrmProjectProductSn();
            return crmProjectProductSn.stream().anyMatch(sn -> ContractProductSnVO.IMPORTANT_CONTRACT.equals(sn.getType()));
        };
        // 判断是否有重点行业的产品
        return ListUtils.emptyIfNull(contractProductOwnDTOS).stream().anyMatch(importantPredicate) ||
                ListUtils.emptyIfNull(contractProductOwnDTOS).stream().flatMap(contractProductOwn -> contractProductOwn.getContractProductOwnComponentDTOS().stream())
                        .anyMatch(importantPredicate);
    }

    public static String checkJobNoAndConcat(String name, String jobNo) {
        if (!name.contains(jobNo)) {  // 如果名字中不包含工号
            return name + jobNo;      // 则把工号加到名字后面
        } else {
            return name;           // 否则直接返回原来的名称

        }
    }

    @Override
    public DraftsTypeEnum getDraftsTypeEnum() {
        return DraftsTypeEnum.CONTRACT_DRAFTS;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DraftsAble getDTO(String businessId) {
        return this.contractInfo(businessId, false, true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteDTO(String businessId) {
        ContractReviewFlowService flowService = SpringUtil.getBean(ContractReviewFlowService.class);
        return flowService.deleteContract(businessId);
    }

    @Data
    @AllArgsConstructor
    public static class ContractPredicate {

        // 合同相关信息
        public static int CONTRACT_INFO = 1;
        // 产品相关信息
        public static int PRODUCT_INFO = 2;
        // 保修期
        public static int PERIOD_TIME = 3;

        /**
         * 校验不通过提示
         */
        private String message;

        /**
         * 判断优先级
         */
        private int ruleOrder;

        /**
         * 校验哪些信息
         */
        private List<Integer> checkRule;

        /**
         * 校验规则
         */
        private BiPredicate<ContractReviewMainFlowLaunchDTO, Pair<CrmProjectDirectlyVo, CrmCustomerVo>> predicate;
    }
}
