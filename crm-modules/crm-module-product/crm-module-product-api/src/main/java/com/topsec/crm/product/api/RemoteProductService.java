package com.topsec.crm.product.api;


import com.topsec.crm.framework.common.constant.ServiceNameConstants;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.product.api.dto.CrmProductLineQuery;
import com.topsec.crm.product.api.entity.*;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * Product RPC
 */
@FeignClient(contextId = "remoteProductService", value = ServiceNameConstants.PRODUCT_SERVICE, url = ServiceNameConstants.PRODUCT_SERVICE_URL)
public interface    RemoteProductService {

    @PostMapping("/product/page")
    @Operation(summary = "分页查询产品")
    JsonObject<PageUtils<CrmProductVo>> page(@RequestBody CrmProductVo crmProductVo);

    @GetMapping("/product/{id}")
    @Operation(summary = "获取【产品】详细信息")
    JsonObject<CrmProductVo> getProduct(@PathVariable String id);

    @PostMapping("/hidden/product/batchGetInfo")
    @Operation(summary = "批量获取【产品成本】详细信息")
    JsonObject<List<CrmProductVo>> batchGetInfo(@RequestBody List<String> ids);

    @GetMapping("/hidden/product/getInfoByMaterialCode")
    @Operation(summary = "根据物料代码获取【产品】详细信息")
    JsonObject<CrmProductVo> getProductByMaterialCode(@RequestParam String materialCode);

    @PostMapping("/hidden/product/groupProductIds")
    @Operation(summary = "对【产品】分组")
    JsonObject<CrmProductGroupVO> groupProductIds(@RequestBody List<String> productIds);

    @PostMapping("/hidden/product/batchGetInfoByMaterialCode")
    @Operation(summary = "根据物料批量获取【产品】详细信息")
    JsonObject<List<CrmProductVo>> batchGetInfoByMaterialCode(@RequestBody List<String> materialCodes);

    @GetMapping("/hidden/product/checkProductLackOfCost")
    @Operation(summary = "产品是否缺成本")
    JsonObject<Boolean> checkProductLackOfCost(@RequestParam String productId);

    @GetMapping("/hidden/product/findHasChild")
    @Operation(summary = "根据产品ID查询是否有子服务或者子配件")
    JsonObject<Boolean> findHasChild(@RequestParam String productId);

    @PostMapping("/hidden/product/batchFindHasChild")
    @Operation(summary = "根据产品ID查询是否有子服务或者子配件")
    JsonObject<Map<String, Boolean>> batchFindHasChild(@RequestBody List<String> productIds);

    @PostMapping("/hidden/product/getNotExistSns")
    @Operation(summary = "根据产品序列号列表，返回不在系统中的产品序列号列表")
    JsonObject<List<String>> getNotExistSns(@RequestBody List<String> materialCodes);

    @PostMapping("/product/selectBySns")
    @Operation(summary = "根据产品序列号列表，返回不在系统中的产品序列号列表")
    JsonObject<List<CrmProductVo>> selectBySns(@RequestBody List<String> materialCodes);

    @PostMapping("/hidden/product/batchFindHasGKCP")
    @Operation(summary = "根据产品IDs 批量查询是否含有工控产品（产品分类为工业互联网安全）")
    JsonObject<Boolean> batchFindHasGKCP(@RequestBody List<String> productIds);

    @PostMapping("/hidden/product/checkProductsRowTypeLack")
    @Operation(summary = "检查产品是否缺少行类型")
    JsonObject<List<CrmProductRowTypeVO>> checkProductsRowTypeLack(@RequestBody List<CrmProductSoftHardwareIdentificationVO> CrmProductSoftHardwareIdentificationVOList);


    @PostMapping("/hidden/product/selectProductsRowType")
    @Operation(summary = "查询产品行类型")
    JsonObject<List<CrmProductRowTypeVO>> selectProductsRowType(@RequestBody List<CrmProductSoftHardwareIdentificationVO> CrmProductSoftHardwareIdentificationVOList);

    @PostMapping(value = "/hidden/product/getProductSoftHardwareIdentifications")
    @Operation(summary = "获取产品软硬件标识名称")
    JsonObject<List<CrmProductSoftHardwareIdentificationVO>> getProductSoftHardwareIdentifications(@RequestBody List<CrmProductSoftHardwareIdentificationVO> crmProductSoftHardwareIdentificationVOList);

    @PostMapping("/hidden/product/crmProductForSelect")
    @Operation(summary = "自有产品选择页面接口")
    JsonObject<PageUtils<CrmProductVo>> crmProductForSelect(@RequestParam Integer pageNum,@RequestParam Integer pageSize,@RequestParam String currentPersonId,@RequestBody CrmProductVo crmProductVo);

    /**
     * 产品配件选择接口
     */
    @PostMapping("/hidden/product/moduleForSelect")
    JsonObject<PageUtils<CrmProductVo>> moduleForSelect(@RequestParam Integer pageNum,@RequestParam Integer pageSize,@RequestBody CrmProductVo crmProductVo);


    @PostMapping("/hidden/product/selectCrmProductListByProductLineQuery")
    JsonObject<List<CrmProductVo>> selectCrmProductListByProductLineQuery(@RequestBody CrmProductLineQuery crmProductLineQuery);

    @GetMapping("/hidden/product/getProductLineById/{id}")
    @Operation(summary = "根据id获取产品线分类")
    JsonObject<CrmProductLineVo> selectProductLineById(@PathVariable String id);

    @PostMapping("/hidden/product/getProductLineByIds")
    @Operation(summary = "根据批量id获取产品线分类")
    JsonObject<List<CrmProductLineVo>> selectProductLineByIds(@RequestBody List<String> ids);


    @PostMapping("/hidden/codeProportion/listByMaterialCodes")
    @Operation(summary="根据物料代码列表查询")
    JsonObject<List<CrmCodeProportionVO>> listByMaterialCodes(@RequestBody List<String> materialCodes);

    @PostMapping("/hidden/product/refreshProductComponentRel")
    @Operation(summary="刷新产品与配件的关系")
    JsonObject<Boolean> refreshProductComponentRel();

    @PostMapping("/hidden/product/getProductsByClassificationExcCode")
    @Operation(summary="根据物料代码返回产品分类例外代码")
    JsonObject<List<String>> getProductsByClassificationExcCode(@RequestBody List<String> materialCodes);

    @PostMapping("/hidden/product/getNonDistributionCodesByMaterialCodes")
    @Operation(summary="根据物料代码返回nonDistributionCode物料代码")
    JsonObject<List<String>> getNonDistributionCodesByMaterialCodes(@RequestBody List<String> materialCodes);

    @PostMapping("/hidden/product/calculateRetailDealPrice")
    @Operation(summary="根据产品表retailPrice字段计算零售成交单价")
    JsonObject<Map<String, BigDecimal>> calculateRetailDealPrice(@RequestBody List<String> materialCodes);

}
