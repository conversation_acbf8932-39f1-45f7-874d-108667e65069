package com.topsec.crm.product.api.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 产品对象 crm_product
 *
 * @date 2024-04-26
 */
@Data
public class CrmProductVo
{
    private static final long serialVersionUID = 1L;

    /** id */
    @Schema(description = "id")
    private String id;

    @Schema(description = "idList")
    private List<String> idList;

    /** 物料代码 */
    @Schema(description = "物料代码")
    private String materialCode;
    /** 产品名称 */
    @Schema(description = "产品名称")
    private String name;
    /** PN码 */
    @Schema(description = "PN码")
    private String pn;
    /** 产品性质 1-自研、2-OEMIn、3-OEMOut、4-软硬分离 */
    @Schema(description = "产品性质 1-自研、2-OEMIn、3-OEMOut、4-软硬分离")
    private Integer quality;
    /** 产品属性 1-非分、2-分销 */
    @Schema(description = "产品属性 1-非分、2-分销")
    private Integer attr;
    /** 产品形态 1-硬件、2-软件、3-配件、4-服务、5-授权 */
    @Schema(description = "产品形态 1-硬件、2-软件、3-配件、4-服务、5-授权")
    private Integer form;
    @Schema(description = "产品线分类1")
    private String productLine1;
    @Schema(description = "产品线分类2")
    private String productLine2;
    @Schema(description = "产品线分类3")
    private String productLine3;
    /** 产品分类1（披露分类） */
    @Schema(description = "产品分类1（披露分类）")
    private String categoryId1;
    /** 产品分类2（所属领域） */
    @Schema(description = "产品分类2（所属领域）")
    private String categoryId2;
    /** 产品分类3（产品分类） */
    @Schema(description = "产品分类3（产品分类）")
    private String categoryId3;
    /** 产品分类4（产品简称/服务简称） */
    @Schema(description = "产品分类4（产品简称/服务简称）")
    private String categoryId4;

    /** 产品分类1（披露分类） */
    @Schema(description = "产品分类1（披露分类）")
    private String categoryName1;
    /** 产品分类2（所属领域） */
    @Schema(description = "产品分类2（所属领域）")
    private String categoryName2;
    /** 产品分类3（产品分类） */
    @Schema(description = "产品分类3（产品分类）")
    private String categoryName3;
    /** 产品分类4（产品简称/服务简称） */
    @Schema(description = "产品分类4（产品简称/服务简称）")
    private String categoryName4;

    /** 天智分类1 */
    @Schema(description = "天智分类1")
    private String tqmCategory1;
    /** 天智分类2 */
    @Schema(description = "天智分类2")
    private String tqmCategory2;
    @Schema(description = "商务部分类")
    private String businessCategoryId;
    @Schema(description = "是否委外")
    private Boolean outSource;
    /** 产品型号id */
    @Schema(description = "产品型号id")
    private String specificationId;
    /** 产品系列id */
    @Schema(description = "产品系列id")
    private String seriesId;
    /** 产品保修期（月） */
    @Schema(description = "产品保修期（月）")
    private Integer guaranteePeriod;
    /** 生产类型 DZFH、Tos、Ta-l、Ta-w、Half、Oem */
    @Schema(description = "生产类型 DZFH、Tos、Ta-l、Ta-w、Half、Oem")
    private String productionType;
    /** 单位（套、块、台、组、根、个、条） */
    @Schema(description = "单位（套、块、台、组、根、个、条）")
    private String unit;
    /** 报价期数id */
    @Schema(description = "报价期数id")
    private String quotationPhaseId;
    /** 产品报价 */
    @Schema(description = "产品报价")
    private BigDecimal price;
    /** sellin价格 */
    @Schema(description = "sellin价格")
    private BigDecimal sellinPrice;
    /** 零售价格 */
    @Schema(description = "零售价格")
    private BigDecimal retailPrice;
    /** 产品状态：1-正常 2-退市 3-预发布 4-预退市 */
    @Schema(description = "产品状态：1-正常 2-退市 3-预发布 4-预退市")
    private Integer status;
    /** 图片url */
    @Schema(description = "图片url")
    private String imageUrl;
    /** 产品说明 */
    @Schema(description = "产品说明")
    private String profile;
    /** 标记删除 0-未删除 1-已删除 */
    @Schema(description = "标记删除 0-未删除 1-已删除")
    private Integer delFlag;
    /** lic数标记 0-否 1-是 */
    @Schema(description = "lic数标记 0-否 1-是")
    private Integer licFlag;
    /** 返点额 */
    @Schema(description = "返点额")
    private Integer rebate;
    /** 创建人 */
    @Schema(description = "创建人")
    private String createUser;
    /** 更新人 */
    @Schema(description = "更新人")
    private String updateUser;
    /** 备注 */
    private String remark;
    /** 产品控制 */
    @Schema(description = "产品控制")
    private String control;

    //业务字段

    // sourceType
    @Schema(description = "业务场景（1：项目下单，2：销售协议，3：产品借试用，4：专项备货）")
    private Integer sourceType;
    //项目ID
    @Schema(description = "项目ID")
    private String projectId;
    //关键词  产品名称/PN
    @Schema(description = "关键词  产品名称/PN")
    private String keywords;
    // 三级产品分类列表（所属领域）
    @Schema(description = "三级产品分类列表（所属领域）")
    private List<String> categoryId3s;
    // 产品是否有  配件或子服务 标识
    @Schema(description = "产品是否有 配件或子服务 标识")
    private Boolean hasChildFlag;
    //产品定制化类型
    @Schema(description = "产品定制化类型")
    private List<Integer> customizedType;
    //序列号
    @Schema(description = "序列号")
    private String sn;
    //产品类型列表
    @Schema(description = "产品类型列表")
    private List<Integer> forms;;

    /** 是否来源于渠道 */
    @Schema(description = "是否来源于渠道")
    private Boolean agent;

    /** 分销通路物料代码 */
    @Schema(description = "分销通路物料代码（使用或条件查询）")
    private List<String> orMaterialCodeList;

    /** 销售通路 */
    @Schema(description = "销售通路")
    private List<Integer> saleAccess;

    // 产品折扣
    @Schema(description = "产品折扣")
    private Double taxRate;

    //是否有分销对应非分销代码标识
    @TableField(exist = false)
    Boolean hasDistributionRel;

    //报价单信息
    @Schema(description = "产品折扣")
    List<String> qpvs;

    // 是否代码禁用
    @Schema(description = "是否代码禁用")
    private Boolean codeDisable;

    // 是否只查询4和5开头的物料代码
    @Schema(description = "是否只查询4和5开头的物料代码")
    private Boolean onlyFF;

    // 是否需要显示控制代码
    @Schema(description = "是否需要显示控制代码")
    private Boolean showControlFlag;

    /**
     * ZD项目既能选择一体机代码，也可以选择基础型代码（软硬分离后的硬件代码），
     * 公司项目和QD项目只能选择一体机代码
     */
    // 是否只能选择一体机代码标识
    @Schema(description = "是否只能选择一体机代码标识")
    private Boolean onlySelectWholeFlag;

    @Schema(description = "一级行业ID")
    private String industryOne;
    @Schema(description = "二级行业ID")
    private String industryTwo;

    @Schema(description = "进货单价（QD项目和ZD项目专用）")
    private BigDecimal purchasePrice;

    @Schema(description = "是否配置sellIn或批量提货价")
    private Boolean sellInOrBatch;

    @Schema(description = "销售协议选择产品过滤5998开头的服务类产品")
    private Boolean xsxySelectFlag;

    @Schema(description = "是否是渠道退换货")
    private Boolean qdBack;

    @Schema(description = "是否有关联的配件标识")
    private Integer relTag;
}
