package com.topsec.crm.product.core.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.topsec.crm.framework.common.web.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 产品对象 crm_product
 *
 * @date 2024-04-26
 */
@Data
public class CrmProduct extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private String id;

    /**
     * 物料代码
     */
    private String materialCode;

    /**
     * 产品名称
     */
    private String name;

    /**
     * PN码
     */
    private String pn;

    /**
     * 产品性质 1-自研、2-OEMIn、3-OEMOut、4-软硬分离
     */
    private Integer quality;

    /**
     * 产品属性 1-非分、2-分销
     */
    private Integer attr;

    /**
     * 产品形态 1-硬件、2-软件、3-配件、4-服务、5-授权
     */
    private Integer form;

    /**
     * 产品线分类1
     */
    private String productLine1;

    /**
     * 产品线分类2
     */
    private String productLine2;

    /**
     * 产品线分类3
     */
    private String productLine3;

    /**
     * 产品分类1（披露分类）
     */
    private String categoryId1;

    /**
     * 产品分类2（所属领域）
     */
    private String categoryId2;

    /**
     * 产品分类3（产品分类）
     */
    private String categoryId3;

    /**
     * 产品分类4（产品简称/服务简称）
     */
    private String categoryId4;

    /**
     * 天智分类1
     */
    private String tqmCategory1;

    /**
     * 天智分类2
     */
    private String tqmCategory2;

    /**
     * 商务部分类
     */
    private String businessCategoryId;

    /**
     * 是否委外 0-否 1-是
     */
    private Boolean outSource;

    /**
     * 分销产品分类3
     */
    private String distributionId3;

    /**
     * 产品型号id
     */
    private String specificationId;

    /**
     * 产品系列id
     */
    private String seriesId;

    /**
     * 产品保修期（月）
     */
    private Integer guaranteePeriod;

    /**
     * 生产类型 DZFH、Tos、Ta-l、Ta-w、Half、Oem
     */
    private String productionType;

    /**
     * 单位（套、块、台、组、根、个、条）
     */
    private String unit;

    /**
     * 报价期数id
     */
    private String quotationPhaseId;

    /**
     * 产品报价
     */
    private BigDecimal price;

    /**
     * sellin价格
     */
    private BigDecimal sellinPrice;

    /**
     * 零售价格
     */
    private BigDecimal retailPrice;

    /**
     * 产品状态：1-正常 2-退市 3-预发布 4-预退市
     */
    private Integer status;

    /**
     * 图片url
     */
    private String imageUrl;

    /**
     * 产品说明
     */
    private String profile;

    /**
     * 标记删除 0-未删除 1-已删除
     */
    private Integer delFlag;

    /**
     * lic数标记 0-否 1-是
     */
    private Integer licFlag;

    /**
     * 返点额
     */
    private Integer rebate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 产品控制
     */
    private String control;

    //业务字段
    //是否查询产品价格标识
    @TableField(exist = false)
    private Boolean searchPriceFlag;
    //关键词  产品名称/PN
    @TableField(exist = false)
    private String keywords;
    // 三级产品分类列表（所属领域）
    @TableField(exist = false)
    private List<String> categoryId3s;
    // 产品是否有  配件或子服务 标识
    @TableField(exist = false)
    private Boolean hasChildFlag;
    //产品定制化类型
    @TableField(exist = false)
    private List<Integer> customizedType;
    //产品类型列表
    @TableField(exist = false)
    private List<Integer> forms;
    // 是否来源于渠道
    @TableField(exist = false)
    private Boolean agent;
    // 分销通路物料代码
    @TableField(exist = false)
    private List<String> orMaterialCodeList;
    //产品折扣
    @TableField(exist = false)
    private Double taxRate;

    @TableField(exist = false)
    private List<String> idList;
    //是否有分销对应非分销代码标识
    @TableField(exist = false)
    Boolean hasDistributionRel;
    //报价单信息
    @TableField(exist = false)
    List<String> qpvs;
    //产品控制设置的需要排除掉的产品IDs
    @TableField(exist = false)
    private List<String> effectsIds;
    // 是否只查询4和5开头的物料代码
    @TableField(exist = false)
    private Boolean onlyFF;
    // 是否只能选择一体机代码标识
    @TableField(exist = false)
    private Boolean onlySelectWholeFlag;
    // 销售协议选择产品过滤5998开头的服务类产品
    @TableField(exist = false)
    private Boolean xsxySelectFlag;
    //关联的配件id
    @TableField(exist = false)
    private List<String> rels;
    //是否有关联的配件标识
    @TableField(exist = false)
    private Integer relTag;
}
