package com.topsec.crm.product.core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.topsec.crm.product.api.dto.CrmProductLineQuery;
import com.topsec.crm.product.api.dto.CrmProductQuery;
import com.topsec.crm.product.api.entity.CrmProductVo;
import com.topsec.crm.product.core.entity.CrmProduct;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 产品Service接口
 *
 * @date 2024-04-26
 */
public interface ICrmProductService extends IService<CrmProduct>
{
    /**
     * 查询产品
     * 
     * @param id 产品ID
     * @return 产品
     */
    CrmProduct selectCrmProductById(String id);

    List<CrmProduct> searchCrmProductListByMaterialCode(String materialCode);

    /**
     * 查询产品列表
     * 
     * @param crmProduct 产品
     * @return 产品集合
     */
    List<CrmProduct> selectCrmProductList(CrmProduct crmProduct);

    /**
     * 新增产品
     * 
     * @param crmProduct 产品
     * @return 结果
     */
    int insertCrmProduct(CrmProduct crmProduct);

    /**
     * 修改产品
     * 
     * @param crmProduct 产品
     * @return 结果
     */
    int updateCrmProduct(CrmProduct crmProduct);

    /**
     * 批量删除产品
     * 
     * @param ids 需要删除的产品ID
     * @return 结果
     */
    int deleteCrmProductByIds(String[] ids);

    /**
     * 删除产品信息
     * 
     * @param id 产品ID
     * @return 结果
     */
    int deleteCrmProductById(String id);

    CrmProduct getInfoByMaterialCode(String materialCode);


    /**
     * 新增自有产品查询
     *
     * @param crmProductQuery 查询条件
     * @return 结果
     */
    List<CrmProductVo> selectCrmProductQuery(CrmProductQuery crmProductQuery);

    /**
     * 产品是否缺成本
     * @param productId
     * @return
     */
    public Boolean checkProductLackOfCost(String productId);

    /**
     * 自有产品选择页面接口
     */
    List<CrmProduct> crmProductForSelect(CrmProduct crmProduct);

    /**
     * 产品配件选择接口
     */
    List<CrmProduct> moduleForSelect(CrmProduct crmProduct);

    /**
     * 根据产品ID查询是否有子服务或者子配件
     */
    Boolean findHasChild(String productId);

    /**
     * 根据产品IDs 批量查询是否有子服务或者子配件
     */
    Map<String,Boolean> batchFindHasChild(List<String> productIds);

    /**
     * 根据设备序列号列表，返回不在系统中的设备序列号列表
     */
    List<String> getNotExistSns(List<String> sns);

    /**
     * 根据产品IDs 批量查询是否含有工控产品（产品分类为工业互联网安全）
     */
    Boolean batchFindHasGKCP(List<String> productIds);

    /**
     * 批量获取【产品】详细信息
     */
    List<CrmProduct> selectCrmProductByIds(List<String> ids);

    /**
     * 根据物料代码批量获取【产品】详细信息
     */
    List<CrmProduct> batchGetInfoByMaterialCode(List<String> materialCodes);

    List<String> getProductLine1List();

    List<CrmProduct> selectCrmProductListByProductLineQuery(CrmProductLineQuery crmProductLineQuery);

    /**
     * 代码权限申请 获取【产品】详细信息
     */
    CrmProduct getInfoForDMSQ(String id);

    /**
     * 刷新产品配件关系
     */
    Boolean refreshProductComponentRel();

    /**
     * 根据ProductClassificationExcCode返回产品分类例外代码
     */
    List<String> getProductsByClassificationExcCode(List<String> materialCodes);

    /**
     * 根据CrmDistributionCodeConversion返回nonDistributionCode物料代码
     */
    List<String> getNonDistributionCodesByMaterialCodes(List<String> materialCodes);

    /**
     * 根据产品表retailPrice字段计算零售成交单价
     */
    Map<String, BigDecimal> calculateRetailDealPrice(List<String> materialCodes);
}
