package com.topsec.crm.product.core.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.topsec.crm.framework.common.bean.ProductComponent;
import com.topsec.crm.framework.common.enums.ProductTypeEnum;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.product.api.dto.CrmProductLineQuery;
import com.topsec.crm.product.api.dto.CrmProductQuery;
import com.topsec.crm.product.api.entity.CrmProductVo;
import com.topsec.crm.product.core.entity.*;
import com.topsec.crm.product.core.mapper.CrmProductCostMapper;
import com.topsec.crm.product.core.mapper.CrmProductMapper;
import com.topsec.crm.product.core.service.*;
import com.topsec.crm.project.api.client.TqmServiceClient;
import com.topsec.crm.project.api.client.RemoteAccessConfigClient;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 产品Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-26
 */
@Service
public class CrmProductServiceImpl extends ServiceImpl<CrmProductMapper, CrmProduct> implements ICrmProductService {
    @Autowired
    private CrmProductMapper crmProductMapper;
    @Autowired
    private CrmProductCostMapper crmProductCostMapper;
    @Autowired
    private ICrmSubServeService crmSubServeService;
    @Autowired
    private ICrmCustomizedProductService crmCustomizedProductService;
    @Autowired
    private ICrmServeCategoryService crmServeCategoryService;
    @Autowired
    private ICrmQuotationPhaseService crmQuotationPhaseService;
    @Autowired
    private ICrmProductDistributionRelService crmProductDistributionRelService;
    @Autowired
    private ICrmMaterialControlService materialControlService;
    @Resource
    private TqmServiceClient tqmServiceClient;
    @Resource
    private ICrmProductModuleRelService crmProductModuleRelService;
    @Autowired
    private ICrmProductCategoryService crmProductCategoryService;
    @Resource
    private RemoteAccessConfigClient remoteAccessConfigClient;

    /**
     * 查询产品
     *
     * @param id 产品ID
     * @return 产品
     */
    @Override
    public CrmProduct selectCrmProductById(String id) {
        return crmProductMapper.selectCrmProductById(id);
    }

    /**
     * 代码权限申请 获取【产品】详细信息
     */
    @Override
    public CrmProduct getInfoForDMSQ(String id) {
        CrmProduct crmProduct = crmProductMapper.selectCrmProductById(id);
        //1.补充报价单信息
        List<String> productIds = Collections.singletonList(id);
        List<CrmQuotationPhase> phases = crmQuotationPhaseService.batchGetQuotation(productIds);
        if(CollectionUtil.isNotEmpty(phases)){
            List<String> list = phases.stream().map(q -> q.getPhaseName() + "(" + q.getPrice() + ")").toList();
            crmProduct.setQpvs(list);
        }
        //2.补充分销对应非分销代码信息
        CrmProductDistributionRel distributionRel = crmProductDistributionRelService.getDistributionRel(id);
        crmProduct.setHasDistributionRel(distributionRel != null ? true : false);

        return crmProduct;
    }


    @Override
    public List<CrmProduct> searchCrmProductListByMaterialCode(String materialCode) {
        QueryWrapper<CrmProduct> wrapper = new QueryWrapper<>();
        wrapper.likeRight(StringUtils.isNotEmpty(materialCode), "material_code", materialCode);
        wrapper.orderByAsc("material_code");
        return crmProductMapper.selectList(wrapper);
    }

    /**
     * 查询产品列表
     *
     * @param crmProduct 产品
     * @return 产品
     */
    @Override
    public List<CrmProduct> selectCrmProductList(CrmProduct crmProduct) {
        return crmProductMapper.selectCrmProductList(crmProduct);
    }

    /**
     * 新增产品
     *
     * @param crmProduct 产品
     * @return 结果
     */
    @Override
    public int insertCrmProduct(CrmProduct crmProduct) {
        crmProduct.setCreateTime(LocalDateTime.now());
        return crmProductMapper.insertCrmProduct(crmProduct);
    }

    /**
     * 修改产品
     *
     * @param crmProduct 产品
     * @return 结果
     */
    @Override
    public int updateCrmProduct(CrmProduct crmProduct) {
        crmProduct.setUpdateTime(LocalDateTime.now());
        return crmProductMapper.updateCrmProduct(crmProduct);
    }

    /**
     * 批量删除产品
     *
     * @param ids 需要删除的产品ID
     * @return 结果
     */
    @Override
    public int deleteCrmProductByIds(String[] ids) {
        return crmProductMapper.deleteCrmProductByIds(ids);
    }

    /**
     * 删除产品信息
     *
     * @param id 产品ID
     * @return 结果
     */
    @Override
    public int deleteCrmProductById(String id) {
        return crmProductMapper.deleteCrmProductById(id);
    }

    @Override
    public CrmProduct getInfoByMaterialCode(String materialCode) {
        QueryWrapper<CrmProduct> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotEmpty(materialCode), "material_code", materialCode);
        return baseMapper.selectOne(wrapper);
    }

    @Override
    public List<CrmProductVo> selectCrmProductQuery(CrmProductQuery crmProductQuery) {

//        List<CrmProduct> crmProducts = crmProductMapper.selectCrmProductByCrmProductQuery(crmProductQuery);
        List<CrmProduct> crmProducts = crmProductMapper.selectList(new QueryWrapper<CrmProduct>().lambda()
                .and(StringUtils.isNoneEmpty(crmProductQuery.getNameOrPn()),
                        entity->{entity.eq(CrmProduct::getName,crmProductQuery.getNameOrPn());
                        entity.or().eq(CrmProduct::getPn,crmProductQuery.getNameOrPn());
                })
                .eq(StringUtils.isNoneEmpty(crmProductQuery.getMaterialCode()),CrmProduct::getMaterialCode,crmProductQuery.getMaterialCode())
                .eq(Objects.nonNull(crmProductQuery.getLevel()) && crmProductQuery.getLevel().equals(1), CrmProduct::getCategoryId1,crmProductQuery.getCategoryId())
                .eq(Objects.nonNull(crmProductQuery.getLevel()) && crmProductQuery.getLevel().equals(2), CrmProduct::getCategoryId2,crmProductQuery.getCategoryId())
                .eq(Objects.nonNull(crmProductQuery.getLevel()) && crmProductQuery.getLevel().equals(3), CrmProduct::getCategoryId3,crmProductQuery.getCategoryId())
                .eq(Objects.nonNull(crmProductQuery.getLevel()) && crmProductQuery.getLevel().equals(4), CrmProduct::getCategoryId4,crmProductQuery.getCategoryId())
                .eq(Objects.nonNull(crmProductQuery.getForm()), CrmProduct::getForm,crmProductQuery.getForm())
                .eq(CrmProduct::getDelFlag,0)
                );

        List<CrmProductVo> productVos = HyperBeanUtils.copyListProperties(crmProducts,CrmProductVo::new);
        return productVos;
    }

    /**
     * 产品是否缺成本
     * @param productId 产品ID
     * @return
     */

    @Override
    public Boolean checkProductLackOfCost(String productId) {
        CrmProductCost crmProductCost = crmProductCostMapper.selectOne(new QueryWrapper<CrmProductCost>().eq("product_id", productId));
        if (crmProductCost == null){
            return true;
        }else {
            List<BigDecimal> collect = Stream.of(crmProductCost.getFinanceCost(), crmProductCost.getPurchaseCost(), crmProductCost.getBomCost(), crmProductCost.getExamineCost()).collect(Collectors.toList());
            return collect.stream().allMatch(Objects::isNull);
        }
    }

    /**
     * 自有产品选择页面接口
     */
    @Override
    public List<CrmProduct> crmProductForSelect(CrmProduct crmProduct) {
        List<CrmProduct> crmProducts = crmProductMapper.crmProductForSelect(crmProduct);

        if(CollectionUtils.isNotEmpty(crmProducts)) {
            List<String> productIds = crmProducts.stream().map(CrmProduct::getId).toList();
            //批量查询自定义类型
            Map<String, List<Integer>> stringListMap = crmCustomizedProductService.batchGetCustomizedType(productIds);
            //批量查询子服务列表
            List<CrmSubServe> subServes = crmSubServeService.batchGetSunServe(productIds);
            //批量查询产品自定义类型
            List<CrmServeCategory> categorys = crmServeCategoryService.query().in("product_id", productIds).list();

            //查询该产品下有没有配件或者服务
            for (CrmProduct product : crmProducts) {
                //查询自定义服务类型
                List<Integer> customizedType = stringListMap.get(product.getId());
                product.setCustomizedType(customizedType);

                //如果是硬件/软件，查询有没有对应配件
                if (product.getForm() == Integer.parseInt(ProductTypeEnum.ProductTypeProductEnum.HARDWARE.getCode())
                        || product.getForm() == Integer.parseInt(ProductTypeEnum.ProductTypeProductEnum.SOFTWARE.getCode())) {
                    //配件取消对应关系
                    //                Integer counts = crmProductMapper.moduleForSelectCount(product);
                    //                product.setHasChildFlag(counts > 0 ? true : false);
                    product.setHasChildFlag(true);
                } else if (product.getForm() == Integer.parseInt(ProductTypeEnum.ProductTypeProductEnum.SERVICE.getCode())) {
                    //服务保持现有关系
                    List<CrmSubServe> list = subServes.stream().filter(e -> e.getProductId().equals(product.getId())).toList();
                    product.setHasChildFlag(list.size() > 0 ? true : false);
                    product.setPrice(list.size() > 0 ? null : product.getPrice());

                } else {
                    product.setHasChildFlag(false);
                }

                //设置产品折扣
                setTaxRate(product,categorys);
            }
        }

        return crmProducts;
    }

    /**
         根据物料代码设置产品折扣

         1、2、4、8开头代码:13%税率；

         5开头代码:
         5998开头代码(5998010000101、5998010000201)，6%税率；
         其他，13%税率

         6开头代码:
         维修、设备租赁、资产租赁，13%税率；
         房屋租赁，9%税率；
         其他，6%税率

         7开头代码:
         销售自己选择，选择范围: 0%税率，6%税率、9%税率、13%税率
     */
    private void setTaxRate(CrmProduct product,List<CrmServeCategory> categorys){
        String materialCode = product.getMaterialCode();
        if(materialCode.startsWith("1") || materialCode.startsWith("2") || materialCode.startsWith("4") || materialCode.startsWith("8")){
            product.setTaxRate(0.13);
        }else if(materialCode.startsWith("5")){
            if(materialCode.startsWith("5998")){
                product.setTaxRate(0.06);
            }else{
                product.setTaxRate(0.13);
            }
        }else if(materialCode.startsWith("6")){
            //查询产品自定义类型
            CrmServeCategory category = categorys.stream().filter(e -> e.getProductId().equals(product.getId())).findFirst().orElse(null);
            if(category==null){
                product.setTaxRate(0.06);
            }else{
                String categoryS = category.getServeCategorySecond();
                if(categoryS.equals("维修") || categoryS.equals("设备租赁") || categoryS.equals("资产租赁")){
                    product.setTaxRate(0.13);
                }else if(categoryS.equals("房屋租赁")){
                    product.setTaxRate(0.09);
                }else{
                    product.setTaxRate(0.06);
                }
            }
        }else{

        }
    }

    /**
     * 产品配件选择接口
     */
    @Override
    public List<CrmProduct> moduleForSelect(CrmProduct crmProduct) {
        List<CrmProduct> crmProducts = crmProductMapper.moduleForSelect(crmProduct);

        if(CollectionUtils.isNotEmpty(crmProducts)) {
            List<String> productIds = crmProducts.stream().map(CrmProduct::getId).toList();
            //批量查询产品自定义内型
            List<CrmServeCategory> categorys = crmServeCategoryService.query().in("product_id", productIds).list();

            for (CrmProduct product : crmProducts) {
                //设置产品折扣
                setTaxRate(product,categorys);
            }
        }

        return crmProducts;
    }

    /**
     * 根据产品ID查询是否有子服务或者子配件
     */
    @Override
    public Boolean findHasChild(String productId) {
        //查询产品信息
        CrmProduct product = crmProductMapper.selectCrmProductById(productId);
        //如果是硬件/软件，查询有没有对应配件
        if(product.getForm() == Integer.parseInt(ProductTypeEnum.ProductTypeProductEnum.HARDWARE.getCode())
                || product.getForm() == Integer.parseInt(ProductTypeEnum.ProductTypeProductEnum.SOFTWARE.getCode())){
            //取消对应关系
//            Integer counts = crmProductMapper.moduleForSelectCount(product);
//            return counts > 0 ? true : false;
            return true;
        }else if (product.getForm() == Integer.parseInt(ProductTypeEnum.ProductTypeProductEnum.SERVICE.getCode())){
            //取消对应关系
            Integer counts = crmSubServeService.serveForSelectCount(product);
            return counts > 0 ? true : false;
        }else {
            return false;
        }
    }

    /**
     * 根据产品IDs 批量查询是否有子服务或者子配件
     */
    @Override
    public Map<String,Boolean> batchFindHasChild(List<String> productIds) {
        Map<String,Boolean> result = new HashMap<>();
        //查询产品信息
        List<CrmProduct> products = crmProductMapper.selectCrmProductByIds(productIds);
        for (CrmProduct product : products) {
            //如果是硬件/软件，查询有没有对应配件
            if(product.getForm() == Integer.parseInt(ProductTypeEnum.ProductTypeProductEnum.HARDWARE.getCode())
                    || product.getForm() == Integer.parseInt(ProductTypeEnum.ProductTypeProductEnum.SOFTWARE.getCode())){
                //取消对应关系
//                Integer counts = crmProductMapper.moduleForSelectCount(product);
//                result.put(product.getId(), counts > 0 ? true : false);
                result.put(product.getId(),true);
            }else if (product.getForm() == Integer.parseInt(ProductTypeEnum.ProductTypeProductEnum.SERVICE.getCode())){
                //服务恢复对应关系
                Integer counts = crmSubServeService.serveForSelectCount(product);
                result.put(product.getId(), counts > 0 ? true : false);
            }else {
                result.put(product.getId(), false);
            }
        }
        return result;
    }

    /**
     * 根据产品IDs 批量查询是否含有工控产品（产品分类为工业互联网安全）
     */
    @Override
    public Boolean batchFindHasGKCP(List<String> productIds) {
        Map<String,Boolean> result = new HashMap<>();
        //根据产品IDs 批量查询是否含有工控产品（产品分类为工业互联网安全）
        List<CrmProduct> products = crmProductMapper.batchFindHadGKCP(productIds);
        return products.size() > 0 ? true : false;
    }

    /**
     * 根据设备序列号列表，返回不在系统中的设备序列号列表
     */
    @Override
    public List<String> getNotExistSns(List<String> sns) {
        List<String> result = new ArrayList<>();

        List<String> crmProducts = crmProductMapper.getExistSns(sns);
        //查询不存在的序列号
        if(crmProducts != null && crmProducts.size() > 0){
            for (String mc : sns) {
                if(!crmProducts.contains(mc)){
                    result.add(mc);
                }
            }

            List<CrmProduct> products = crmProductMapper.selectCrmProductByIds(crmProducts);
            List<String> notStartFourProIds = new ArrayList<>();
            for (CrmProduct pro : products) {
                if(!pro.getMaterialCode().startsWith("4")){
                    notStartFourProIds.add(pro.getId());
                }
            }

            if(CollectionUtils.isNotEmpty(notStartFourProIds)){
                //反查SN
                List<String> notStartForSns = crmProductMapper.getNotStartForSns(notStartFourProIds);
                result.addAll(notStartForSns);
            }
        }

        return result;
    }

    /**
     * 批量获取【产品】详细信息
     */
    @Override
    public List<CrmProduct> selectCrmProductByIds(List<String> ids) {
        return crmProductMapper.selectCrmProductByIds(ids);
    }

    /**
     * 根据物料代码批量获取【产品】详细信息
     */
    @Override
    public List<CrmProduct> batchGetInfoByMaterialCode(List<String> materialCodes) {
        return crmProductMapper.selectCrmProductByMaterialCodes(materialCodes);
    }

    @Override
    public List<String> getProductLine1List() {
        return crmProductMapper.getProductLine1List();
    }

    @Override
    public List<CrmProduct> selectCrmProductListByProductLineQuery(CrmProductLineQuery crmProductLineQuery) {
        List<String> productIds = crmProductLineQuery.getProductIds();
        List<CrmProduct> crmProducts = CollectionUtils.isNotEmpty(productIds) ? crmProductMapper.selectCrmProductByIds(productIds) : new ArrayList<CrmProduct>();
        List<CrmProductLineQuery.ProductLine> productLines = crmProductLineQuery.getProductLines();
        if (CollectionUtils.isNotEmpty(productLines)) {
            List<CrmProduct> result = productLines.stream().map(item -> {
                List<CrmProduct> crmProductsTemp = crmProducts;
                String productLine1 = item.getProductLineId1();
                String productLine2 = item.getProductLineId2();
                String productLine3 = item.getProductLineId3();
                if (StringUtils.isEmpty(productLine1) && StringUtils.isEmpty(productLine2) && StringUtils.isEmpty(productLine3)) {
                    crmProductsTemp = new ArrayList<CrmProduct>();
                } else if (StringUtils.isNotEmpty(productLine1) && StringUtils.isEmpty(productLine2) && StringUtils.isEmpty(productLine3)) {
                    crmProductsTemp=crmProductsTemp.stream().filter(item1 -> productLine1.equals(item1.getProductLine1())).toList();
                } else if (StringUtils.isNotEmpty(productLine1) && StringUtils.isNotEmpty(productLine2) && StringUtils.isEmpty(productLine3)) {
                    crmProductsTemp=crmProductsTemp.stream().filter(item1 -> productLine1.equals(item1.getProductLine1()) && productLine2.equals(item1.getProductLine2())).toList();
                } else if (StringUtils.isNotEmpty(productLine1) && StringUtils.isNotEmpty(productLine2) && StringUtils.isNotEmpty(productLine3)) {
                    crmProductsTemp=crmProductsTemp.stream().filter(item1-> productLine1.equals(item1.getProductLine1()) && productLine2.equals(item1.getProductLine2()) && productLine3.equals(item1.getProductLine3())).toList();
                }
                return crmProductsTemp;
            }).toList().stream().flatMap(List::stream).distinct().toList();
            return result;
        }else{
            return crmProducts;
        }
    }

    /**
     * 刷新产品配件关系
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean refreshProductComponentRel() {
        JsonObject<List<ProductComponent>> listJsonObject = tqmServiceClient.prodCompListAll();
        if(listJsonObject.isSuccess() && CollectionUtils.isNotEmpty(listJsonObject.getObjEntity())){
            List<ProductComponent> objEntity = listJsonObject.getObjEntity();
            Set<String> stuffCodes = objEntity.stream().map(ProductComponent::getPid).collect(Collectors.toSet());
            stuffCodes.addAll(objEntity.stream().map(ProductComponent::getCid).collect(Collectors.toSet()));
            if(CollectionUtils.isNotEmpty(stuffCodes)){
                List<CrmProduct> crmProducts = crmProductMapper.selectList(new QueryWrapper<CrmProduct>().lambda().in(CrmProduct::getMaterialCode, stuffCodes));
                List<CrmProductModuleRel> list = new ArrayList<>();
                objEntity.forEach(item -> {
                    CrmProduct parent = crmProducts.stream().filter(product -> product.getMaterialCode().equals(item.getPid())).findFirst().orElse(null);
                    CrmProduct child = crmProducts.stream().filter(product -> product.getMaterialCode().equals(item.getCid())).findFirst().orElse(null);
                    if(parent != null && child != null){
                        CrmProductModuleRel crmProductModuleRel = new CrmProductModuleRel();
                        crmProductModuleRel.setId(item.getPcid());
                        crmProductModuleRel.setProductId(parent.getId());
                        crmProductModuleRel.setModuleProductId(child.getId());
                        crmProductModuleRel.setDelFlag(0);
                        list.add(crmProductModuleRel);
                    }
                });
                // 差量更新
                List<CrmProductModuleRel> existingList = crmProductModuleRelService.list();
                Set<String> existingIds = existingList.stream().map(CrmProductModuleRel::getId).collect(Collectors.toSet());
                Set<String> newIds = list.stream().map(CrmProductModuleRel::getId).collect(Collectors.toSet());

                List<CrmProductModuleRel> toDelete = existingList.stream()
                        .filter(rel -> !newIds.contains(rel.getId()))
                        .toList();
                List<CrmProductModuleRel> toInsert = list.stream()
                        .filter(rel -> !existingIds.contains(rel.getId()))
                        .toList();
                List<CrmProductModuleRel> toUpdate = list.stream()
                        .filter(rel -> existingIds.contains(rel.getId()))
                        .toList();
                if (CollectionUtils.isNotEmpty(toDelete)) {
                    crmProductModuleRelService.removeByIds(toDelete.stream().map(CrmProductModuleRel::getId).toList());
                }
                if (CollectionUtils.isNotEmpty(toInsert)) {
                    crmProductModuleRelService.saveBatch(toInsert);
                }
                if (CollectionUtils.isNotEmpty(toUpdate)) {
                    crmProductModuleRelService.updateBatchById(toUpdate);
                }
                /*batchUpdateProductModuleRel(toDelete, toInsert, toUpdate);
                crmProductModuleRelService.remove(null);
                if(CollectionUtils.isNotEmpty(list)){
                    crmProductModuleRelService.saveOrUpdateBatch(list);
                }*/
            }
        }
        return true;
    }

    @Override
    public List<String> getProductsByClassificationExcCode(List<String> materialCodes) {
        if (CollectionUtils.isEmpty(materialCodes)) {
            return new ArrayList<>();
        }

        // 根据物料代码查询产品信息
        List<CrmProduct> products = batchGetInfoByMaterialCode(materialCodes);
        if (CollectionUtils.isEmpty(products)) {
            return new ArrayList<>();
        }

        // 这里需要根据ProductClassificationExcCode的具体业务逻辑来筛选
        // 暂时返回所有物料代码，具体逻辑需要根据业务需求实现
        return products.stream()
                .map(CrmProduct::getMaterialCode)
                .collect(Collectors.toList());
    }

    @Override
    public List<String> getNonDistributionCodesByMaterialCodes(List<String> materialCodes) {
        if (CollectionUtils.isEmpty(materialCodes)) {
            return new ArrayList<>();
        }

        // 根据物料代码查询产品信息
        List<CrmProduct> products = batchGetInfoByMaterialCode(materialCodes);
        if (CollectionUtils.isEmpty(products)) {
            return new ArrayList<>();
        }

        // 查询分销对应非分销产品关系
        List<String> productIds = products.stream().map(CrmProduct::getId).collect(Collectors.toList());
        List<String> nonDistributionCodes = new ArrayList<>();

        for (String productId : productIds) {
            CrmProductDistributionRel distributionRel = crmProductDistributionRelService.getDistributionRel(productId);
            if (distributionRel != null) {
                // 获取非分销产品的物料代码
                CrmProduct nonDistributionProduct = selectCrmProductById(distributionRel.getProductId());
                if (nonDistributionProduct != null) {
                    nonDistributionCodes.add(nonDistributionProduct.getMaterialCode());
                }
            }
        }

        return nonDistributionCodes;
    }

    @Override
    public Map<String, BigDecimal> calculateRetailDealPrice(List<String> materialCodes) {
        if (CollectionUtils.isEmpty(materialCodes)) {
            return new HashMap<>();
        }

        // 根据物料代码查询产品信息
        List<CrmProduct> products = batchGetInfoByMaterialCode(materialCodes);
        if (CollectionUtils.isEmpty(products)) {
            return new HashMap<>();
        }

        Map<String, BigDecimal> retailDealPriceMap = new HashMap<>();

        for (CrmProduct product : products) {
            BigDecimal retailDealPrice = BigDecimal.ZERO;
            String materialCode = product.getMaterialCode();

            // 获取产品分类2信息
            String categoryName2 = "";
            if (StringUtils.isNotEmpty(product.getCategoryId2())) {
                CrmProductCategory category = crmProductCategoryService.getById(product.getCategoryId2());
                if (category != null) {
                    categoryName2 = category.getName();
                }
            }

            // 判断是否为分销产品（attr=2表示分销）
            boolean isDistribution = product.getAttr() != null && product.getAttr() == 2;

            if (isDistribution && product.getRetailPrice() != null && product.getRetailPrice().compareTo(BigDecimal.ZERO) > 0) {
                // 分销产品为"是"且物料代码零售价不为空，则零售成交价= 零售价
                retailDealPrice = product.getRetailPrice();
            } else if (isDistribution && "云计算-超融合产品".equals(categoryName2)) {
                // 分销产品为"是"且产品分类2=云计算-超融合产品，则零售成交价=当期报价
                retailDealPrice = product.getPrice() != null ? product.getPrice() : BigDecimal.ZERO;
            } else if (isDistribution && product.getPrice() != null && product.getPrice().compareTo(BigDecimal.ZERO) != 0
                    && product.getSpecification() != null && product.getSpecification().toLowerCase().contains("lic")) {
                // 分销产品为"是"且当期报价 != 0且产品型号包含'lic'，则零售成交价=当期报价*0.2
                retailDealPrice = product.getPrice().multiply(new BigDecimal("0.2"));
            } else if (isDistribution && product.getPrice() != null && product.getPrice().compareTo(BigDecimal.ZERO) != 0) {
                // 分销产品为"是"且当期报价 != 0，则零售成交价=当期报价*0.2*1.2
                retailDealPrice = product.getPrice().multiply(new BigDecimal("0.2")).multiply(new BigDecimal("1.2"));
            } else if (isInDistributionChannelGroup(product.getId()) && product.getAttr() != null && product.getAttr() == 1
                    && product.getPrice() != null && product.getPrice().compareTo(BigDecimal.ZERO) != 0) {
                // 物料代码属于"分销通路产品组"且产品属性为"非分销"且当期报价 != 0，则零售成交价=当期报价*0.22
                retailDealPrice = product.getPrice().multiply(new BigDecimal("0.22"));
            }
            // else 零售成交价=0 (默认值)

            retailDealPriceMap.put(materialCode, retailDealPrice);
        }

        return retailDealPriceMap;
    }

    /**
     * 判断产品是否属于分销通路产品组
     */
    private boolean isInDistributionChannelGroup(String productId) {
        try {
            // 调用RemoteAccessConfigClient#getInfoByGroupName接口获取分销通路产品组的产品列表
            JsonObject<List<String>> result = remoteAccessConfigClient.getInfoByGroupName("分销通路产品组");
            if (result.isSuccess() && result.getObjEntity() != null) {
                return result.getObjEntity().contains(productId);
            }
        } catch (Exception e) {
            // 如果调用失败，记录日志并返回false
            // logger.warn("调用分销通路产品组接口失败", e);
        }
        return false;
    }
}
