package com.topsec.crm.agent.core.mapstruct;

import com.topsec.crm.agent.api.entity.CrmAgentDisableFlowVO;
import com.topsec.crm.agent.core.entity.CrmAgent;
import com.topsec.crm.agent.core.entity.CrmAgentDisableFlow;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface AgentDisableFlowConvertor {
    AgentDisableFlowConvertor INSTANCE = Mappers.getMapper(AgentDisableFlowConvertor.class);

    @Mapping(target = "projectInfoList", ignore = true)
    CrmAgentDisableFlowVO toDisableFlowVO(CrmAgentDisableFlow flow);

    @Mapping(target = "enableScope", ignore = true)
    @Mapping(target = "end", ignore = true)
    @Mapping(target = "begin", ignore = true)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createUser", ignore = true)
    @Mapping(target = "updateUser", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateTime", ignore = true)

    @Mapping(target = "agentId", source = "id")
    CrmAgentDisableFlow agentToDisableFlow(CrmAgent agent);
}
