package com.topsec.crm.agent.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.topsec.crm.framework.common.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 渠道禁止下单
 * @TableName crm_agent_disable_flow
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="crm_agent_disable_flow")
@Data
public class CrmAgentDisableFlow extends BaseEntity {
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 渠道id
     */
    private String agentId;

    /**
     * 渠道名
     */
    private String agentName;

    /**
     * 渠道级别
     */
    private Integer level;

    /**
     * 当前授信级别
     */
    private String creditLevel;

    /**
     * 授信额度
     */
    private BigDecimal creditAmount;

    /**
     * 欠款金额
     */
    private BigDecimal arrearsAmount;

    /**
     * 超期应收
     */
    private BigDecimal overdueReceivable;

    /**
     * 禁止下单范围 1-全部项目 2-部分项目
     */
    private Integer enableScope;

    /**
     * 解禁开始时间
     */
    private LocalDate begin;

    /**
     * 解禁结束时间
     */
    private LocalDate end;

}
