package com.topsec.crm.agent.core.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.topsec.crm.agent.api.entity.*;
import com.topsec.crm.agent.core.entity.CrmAgentDisableFlow;
import com.topsec.crm.agent.core.entity.CrmAgentDisableFlowEnableProject;
import com.topsec.crm.agent.core.mapper.CrmAgentDisableFlowEnableProjectMapper;
import com.topsec.crm.agent.core.service.CrmAgentDisableFlowService;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/agentDisableFlow")
@Tag(name = "渠道禁止下单相关")
public class CrmAgentDisableFlowController {

    @Resource
    private CrmAgentDisableFlowService crmAgentDisableFlowService;


    @Resource
    private CrmAgentDisableFlowEnableProjectMapper crmAgentDisableFlowEnableProjectMapper;

    @Operation(summary = "分页")
    @PostMapping("/page")
    @PreAuthorize(hasPermission = "crm_agent_disable_order")
    public JsonObject<PageUtils<CrmAgentDisableFlowVO>> page(@RequestBody CrmAgentDisableFlowQuery query) {
        return new JsonObject<>(crmAgentDisableFlowService.page(query));
    }
    @Operation(summary = "解禁项目分页-选择解禁用")
    @PostMapping("/pageInfoBySignCustom")
    @PreAuthorize(hasPermission = "crm_agent_disable_order_update")
    public JsonObject<PageUtils<SignCustomProjectInfo>> pageInfoBySignCustom(@RequestBody SignCustomProjectInfoQuery query) {
        return new JsonObject<>(crmAgentDisableFlowService.pageInfoBySignCustom(query));
    }


    @Operation(summary = "修改解禁")
    @PostMapping("/update")
    @PreAuthorize(hasPermission = "crm_agent_disable_order_update")
    public JsonObject<Boolean> update(@RequestBody CrmAgentDisableFlowProjectInput input) {
        Integer enableScope = input.getEnableScope();
        CrmAgentDisableFlow crmAgentDisableFlow=new CrmAgentDisableFlow();
        crmAgentDisableFlow.setId(input.getId());
        crmAgentDisableFlow.setEnableScope(enableScope);
        crmAgentDisableFlow.setBegin(input.getBegin());
        crmAgentDisableFlow.setEnd(input.getEnd());
        if (enableScope == 1){
            crmAgentDisableFlowService.updateById(crmAgentDisableFlow);
            crmAgentDisableFlowEnableProjectMapper.delete(new LambdaQueryWrapper<CrmAgentDisableFlowEnableProject>()
                    .eq(CrmAgentDisableFlowEnableProject::getDisableFlowId,input.getId()));
        }else if(enableScope==2){
            crmAgentDisableFlowService.update(new LambdaUpdateWrapper<CrmAgentDisableFlow>()
                    .eq(CrmAgentDisableFlow::getId,input.getId())
                    .set(CrmAgentDisableFlow::getEnableScope,enableScope)
                    .set(CrmAgentDisableFlow::getBegin,null)
                    .set(CrmAgentDisableFlow::getEnd,null)
            );
            crmAgentDisableFlowEnableProjectMapper.delete(new LambdaQueryWrapper<CrmAgentDisableFlowEnableProject>()
                    .eq(CrmAgentDisableFlowEnableProject::getDisableFlowId,input.getId()));
            crmAgentDisableFlowEnableProjectMapper.insert(input.getProjectInfoList().stream().map(project -> {
                CrmAgentDisableFlowEnableProject item = new CrmAgentDisableFlowEnableProject();
                item.setDisableFlowId(input.getId());
                item.setProjectId(project.getId());
                item.setBegin(project.getBegin());
                item.setEnd(project.getEnd());
                return item;
            }).toList());
        } else if(enableScope==3){
            crmAgentDisableFlowEnableProjectMapper.delete(new LambdaQueryWrapper<CrmAgentDisableFlowEnableProject>()
                    .eq(CrmAgentDisableFlowEnableProject::getDisableFlowId,input.getId()));
            crmAgentDisableFlowService.update(new LambdaUpdateWrapper<CrmAgentDisableFlow>()
                    .eq(CrmAgentDisableFlow::getId,input.getId())
                    .set(CrmAgentDisableFlow::getEnableScope,enableScope)
                    .set(CrmAgentDisableFlow::getBegin,null)
                    .set(CrmAgentDisableFlow::getEnd,null)
            );
        }

        return new JsonObject<>(true);
    }

}
