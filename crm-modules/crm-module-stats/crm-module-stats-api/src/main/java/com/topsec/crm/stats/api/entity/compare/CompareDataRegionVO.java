package com.topsec.crm.stats.api.entity.compare;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Schema(description = "同期对比-大区")
@Data
@EqualsAndHashCode(callSuper = false)
public class CompareDataRegionVO extends CompareDataVO {

    @Schema(description = "大区")
    private String region;

    @Schema(description = "省")
    private String province;

}
