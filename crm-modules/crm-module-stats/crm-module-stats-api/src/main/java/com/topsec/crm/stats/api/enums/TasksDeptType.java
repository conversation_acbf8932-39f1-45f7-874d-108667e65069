package com.topsec.crm.stats.api.enums;

import lombok.Getter;


@Getter
public enum TasksDeptType {
    REGION(0,2),
    REGION_OFFICE(1,4),
    INDUSTRY_DIVISION(2,5);
    private Integer code;
    private Integer value;

    TasksDeptType(Integer code,Integer value){
        this.code=code;
        this.value=value;
    }
    public static TasksDeptType getTypeEnum(Integer code){
        for(TasksDeptType type: TasksDeptType.values()){
            if (type.getCode().equals(code)){
                return type;
            }
        }
        return null;
    }
}
