package com.topsec.crm.stats.api.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 数据分发人员对象
 */
@Data
@Schema(description = "数据分发人员对象")
@NoArgsConstructor
@AllArgsConstructor
public class MocDistributePersonVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** ID */
    @Schema(description = "ID")
    private String id;

    /** 数据分发ID */
    @Schema(description = "数据分发ID")
    private String distributeId;

    /** 人员ID */
    @Schema(description = "人员ID")
    private String personId;

    /** 导出权限（1：允许导出，2：禁止导出） */
    @Schema(description = "导出权限（1：允许导出，2：禁止导出）")
    private Integer isReport;

    //业务字段
    @Schema(description = "人员名称")
    private String personName;

}
