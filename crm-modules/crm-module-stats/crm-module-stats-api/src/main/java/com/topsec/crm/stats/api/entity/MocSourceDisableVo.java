package com.topsec.crm.stats.api.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 属性对象
 * 
 * <AUTHOR>
 * @date 2023-01-09
 */
@Data
@Schema(description = "属性对象")
@NoArgsConstructor
@AllArgsConstructor
public class MocSourceDisableVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** ID */
    @Schema(description = "ID")
    private String id;

    /** 数据源ID */
    @Schema(description = "数据源ID")
    private String sourceId;

    /** 部门ID */
    @Schema(description = "部门ID")
    private String deptId;

    /** 人员ID */
    @Schema(description = "人员ID")
    private String personId;

    /** 人员组ID */
    @Schema(description = "人员组ID")
    private String groupId;

    /** 禁用属性列表 */
    @Schema(description = "禁用属性列表")
    private List<MocSourceFieldVo> fields;

    //业务字段
    @Schema(description = "部门名称")
    private String deptName;
    @Schema(description = "人员姓名")
    private String personName;
    @Schema(description = "人员组名称")
    private String groupName;

}
