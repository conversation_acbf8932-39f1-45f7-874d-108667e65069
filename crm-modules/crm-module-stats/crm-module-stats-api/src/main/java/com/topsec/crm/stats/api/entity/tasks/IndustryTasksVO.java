package com.topsec.crm.stats.api.entity.tasks;

import com.topsec.crm.framework.common.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

@Data
public class IndustryTasksVO implements Serializable {

    @Schema(description = "ID")
    private String id;

    @Excel(name = "年份")
    @Schema(description = "年份")
    private Integer year;

    @Schema(description = "部门ID")
    private String deptId;

    @Excel(name = "一级行业ID")
    @Schema(description = "一级行业ID")
    private List<String> industryLevel1;

    @Excel(name = "二级行业ID")
    @Schema(description = "二级行业ID(JSON)")
    private List<String> industryLevel2;

    @Excel(name = "负责人")
    @Schema(description = "负责人(JSON)")
    private List<String> industryLeaders;

    @Excel(name = "任务金额")
    @Schema(description = "任务金额")
    private BigDecimal tasksMoney;

    @Excel(name = "备注")
    @Schema(description = "备注")
    private String remarks;



    public String level2ToString(){
        if (CollectionUtils.isEmpty(this.industryLevel2)){
            return null;
        }else{
            return this.industryLevel2.stream().map(s -> "\"" + s + "\"").collect(Collectors.toList()).toString();
        }
    }

    public String industryLeadersToString(){
        if (CollectionUtils.isEmpty(this.industryLeaders)){
            return null;
        }else{
            return this.industryLeaders.stream().map(s -> "\"" + s + "\"").collect(Collectors.toList()).toString();
        }
    }
}
