package com.topsec.crm.stats.api.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 属性对象
 * 
 * <AUTHOR>
 * @date 2023-01-09
 */
@Data
@Schema(description = "属性对象")
@NoArgsConstructor
@AllArgsConstructor
public class MocSourceFieldVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 属性 */
    @Schema(description = "属性")
    private String field;

    /** 属性名称 */
    @Schema(description = "属性名称")
    private String fieldName;

}
