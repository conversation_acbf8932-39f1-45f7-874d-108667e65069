package com.topsec.crm.stats.api.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 数据分发基本信息对象
 * 
 * <AUTHOR>
 * @date 2023-01-09
 */
@Data
@Schema(description = "数据分发基本信息对象")
@NoArgsConstructor
@AllArgsConstructor
public class MocDistributeVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** ID */
    @Schema(description = "ID")
    private String id;

    /** 任务号 */
    @Schema(description = "任务号")
    private String taskNumber;

    /** 任务标题 */
    @Schema(description = "任务标题")
    private String title;

    /** 通知方式（1：不通知，2：企业微信，3：邮箱，4：企业微信和邮箱） */
    @Schema(description = "通知方式（1：不通知，2：企业微信，3：邮箱，4：企业微信和邮箱）")
    private Integer notifyType;

    /** 通知消息 */
    @Schema(description = "通知消息")
    private String content;

    /** 任务事项 */
    @Schema(description = "任务事项")
    private String taskMatters;

    /** 任务状态（1：执行中，2：已完成，3:已撤销） */
    @Schema(description = "任务状态（1：执行中，2：已完成，3:已撤销）")
    private Integer status;

    @Schema(description = "创建人")
    private String createUser;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /** 创建人姓名 */
    @Schema(description = "创建人姓名")
    private String createUserName;

    //业务字段
    @Schema(description = "分发人员列表")
    private List<MocDistributePersonVo> distributePersons;
    @Schema(description = "分发配置")
    private MocDistributeConfigVo configVo;
    @Schema(description = "分发配置-规则配置")
    private List<MocDistributeConfigRuleVo> configRules;

}
