package com.topsec.crm.stats.api.entity.compare;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Schema(description = "同期对比-销售人员")
@Data
@EqualsAndHashCode(callSuper = false)
public class CompareDataSalesVO extends CompareDataVO {

    @Schema(description = "销售人员id")
    private String personId;

    @Schema(description = "销售人员名称")
    private String personName;

}
