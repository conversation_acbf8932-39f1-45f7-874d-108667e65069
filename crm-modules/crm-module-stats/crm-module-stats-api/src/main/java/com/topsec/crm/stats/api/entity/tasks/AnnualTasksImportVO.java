package com.topsec.crm.stats.api.entity.tasks;

import com.topsec.crm.framework.common.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class AnnualTasksImportVO {

    @Excel(name = "年度")
    @Schema(description = "年度")
    private Integer year;

    @Excel(name = "业务部门")
    @Schema(description = "业务部门")
    private String deptName;

    @Excel(name = "部门类别")
    @Schema(description = "部门类别 区域事业部、区域办事处、行业事业部")
    private String deptType;

    @Excel(name = "一级行业")
    @Schema(description = "一级行业")
    private String industryLevel1;

    @Excel(name = "二级行业")
    @Schema(description = "二级行业")
    private String industryLevel2;

    @Excel(name = "销售团队")
    @Schema(description = "销售团队")
    private String saleTeam;

    @Excel(name = "工号")
    @Schema(description = "工号")
    private String jobNo;

    @Excel(name = "销售人员")
    @Schema(description = "销售人员")
    private String personName;

    @Excel(name = "新增毛利任务")
    @Schema(description = "新增毛利任务")
    private BigDecimal additionProfitTasks;

    @Excel(name = "回款毛利任务")
    @Schema(description = "回款毛利任务")
    private BigDecimal returnedProfitTasks;

    @Excel(name = "si安全产品")
    @Schema(description = "si安全产品")
    private BigDecimal siSafeProduct;

    @Excel(name = "si无线产品")
    @Schema(description = "si无线产品")
    private BigDecimal siWirelessProduct;

    @Excel(name = "so安全产品")
    @Schema(description = "so安全产品")
    private BigDecimal soSafeProduct;

    @Excel(name = "so无线产品")
    @Schema(description = "so无线产品")
    private BigDecimal soWirelessProduct;

    @Excel(name = "工业互联网安全产品新增合同")
    @Schema(description = "工业互联网安全产品新增合同")
    private BigDecimal industrialSafeProduct;

    @Schema(description = "工业互联网安全产品项目个数")
    private BigDecimal industrialSafeNumber;

    @Excel(name = "安全集成战略项目个数")
    @Schema(description = "安全集成战略项目个数")
    private BigDecimal safeMixNumber;

    @Schema(description = "安全集成战略任务")
    private BigDecimal safeMixTasks;

    @Schema(description = "XC集成项目个数")
    private BigDecimal xcProjectNumber;

    @Schema(description = "XC集成新增合同")
    private BigDecimal xcContract;

    @Excel(name = "云计算新增合同")
    @Schema(description = "云计算新增合同")
    private BigDecimal cloudComputingContract;

    @Excel(name = "有效授权合同伙伴激活任务数")
    @Schema(description = "有效授权合同伙伴激活任务数")
    private BigDecimal effectiveAuthorizePartnerActivation;

    @Excel(name = "授权合同伙伴激活任务数")
    @Schema(description = "授权合同伙伴激活任务数")
    private BigDecimal authorizePartnerActivation;

    @Excel(name = "渠道总监售前初级认证工程师任务数")
    @Schema(description = "渠道总监售前初级认证工程师任务数")
    private BigDecimal channelPreSalesPrimary;

    @Excel(name = "渠道总监售前中级认证工程师任务数")
    @Schema(description = "渠道总监售前中级认证工程师任务数")
    private BigDecimal channelPreSalesIntermediate;

    @Excel(name = "渠道总监售后初级认证工程师任务数")
    @Schema(description = "渠道总监售后初级认证工程师任务数")
    private BigDecimal channelAfterSalesPrimary;

    @Excel(name = "渠道总监售后中级认证工程师任务数")
    @Schema(description = "渠道总监售后中级认证工程师任务数")
    private BigDecimal channelAfterSalesIntermediate;

    @Schema(description = "部门ID" , hidden = true)
    private String deptId;

    @Schema(description = "任务分类" , hidden = true)
    private Integer taskType;

    @Schema(description = "销售团队ID" , hidden = true)
    private String saleTeamId;

    @Schema(description = "错误原因" , hidden = true)
    private String errorReason;
}
