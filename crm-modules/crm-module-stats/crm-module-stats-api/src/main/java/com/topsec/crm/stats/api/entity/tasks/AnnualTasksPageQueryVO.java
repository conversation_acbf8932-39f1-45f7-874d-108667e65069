package com.topsec.crm.stats.api.entity.tasks;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class AnnualTasksPageQueryVO {

    @Schema(description = "部门ID")
    private List<String> deptId;

    @Schema(description = "年份")
    private Integer year;

    @Schema(description = "部门类别 0-区域事业部 1-区域办事处 2-行业事业部")
    private Integer deptType;

}
