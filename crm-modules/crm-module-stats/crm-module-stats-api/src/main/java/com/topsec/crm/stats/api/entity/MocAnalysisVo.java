package com.topsec.crm.stats.api.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * 数据分析基本信息对象
 * 
 * <AUTHOR>
 * @date 2023-01-09
 */
@Data
@Schema(description = "数据分析基本信息对象")
@NoArgsConstructor
@AllArgsConstructor
public class MocAnalysisVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    @Schema(description = "ID")
    private String id;

    @Schema(description = "报表名称")
    private String name;

    @Schema(description = "数据源ID")
    private String sourceId;

    @Schema(description = "失效类型（1：手动失效，2：自动失效）")
    private Integer loseEfficacy;

    @Schema(description = "失效时间")
    private LocalDate loseDate;

    @Schema(description = "模板文件ID")
    private String docId;

    @Schema(description = "状态（1：正常，2：失效）")
    private Integer status;

    @Schema(description = "报表说明")
    private String remark;

    @Schema(description = "创建人")
    private String createUser;

    @Schema(description = "创建人姓名")
    private String createUserName;

    //业务字段
    @Schema(description = "开始时间")
    private LocalDate startDate;
    @Schema(description = "创建人姓名")
    private LocalDate endDate;
    @Schema(description = "报表成员列表")
    private List<MocAnalysisPermissionsVo> permissionsVos;
}
