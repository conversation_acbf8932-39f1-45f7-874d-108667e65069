package com.topsec.crm.stats.api.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 数据分发配置
 * 
 * <AUTHOR>
 * @date 2023-01-09
 */
@Data
@Schema(description = "数据分发配置")
@NoArgsConstructor
@AllArgsConstructor
public class MocDistributeConfigVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** ID */
    @Schema(description = "ID")
    private String id;

    /** 数据分发ID */
    @Schema(description = "数据分发ID")
    private String distributeId;

    /** 数据分析表ID */
    @Schema(description = "数据分析表ID")
    private String analysisId;

    /** 表头行 */
    @Schema(description = "表头行")
    private String rowTitle;

    /** 表头行 */
    @Schema(description = "表头行")
    private String columnRange;

    /** 列范围对应属性 */
    @Schema(description = "列范围对应属性")
    private List<MocSourceFieldVo> columns;

    /** 可编辑字段 */
    @Schema(description = "可编辑字段")
    private List<MocSourceFieldVo> editColumn;

    /** 行规则多规则匹配逻辑（1：且条件，2：或条件） */
    @Schema(description = "行规则多规则匹配逻辑（1：且条件，2：或条件）")
    private Integer logicType;

}
