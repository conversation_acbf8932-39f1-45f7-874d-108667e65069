package com.topsec.crm.stats.api.enums;
import lombok.Getter;

@Getter
public enum TasksSheetType {
    DETP_VALYER(1,"部门任务"),
    TEAM_VALYER(2,"团队任务"),
    SALE_VALYER(3,"个人任务");
    private Integer code;
    private String value;

    TasksSheetType(Integer code,String value){
        this.code=code;
        this.value=value;
    }

    public static TasksSheetType valueOf(Integer code){
        for (TasksSheetType tasksSheetType : TasksSheetType.values()){
            if (tasksSheetType.getCode().equals(code)){
                return tasksSheetType;
            }
        }
        return null;
    }
}
