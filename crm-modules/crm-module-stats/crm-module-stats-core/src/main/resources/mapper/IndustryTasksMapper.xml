<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.stats.core.mapper.IndustryTasksMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.stats.core.entity.task.IndustryTasks">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="industryLevel1" column="industry_level1" jdbcType="OTHER" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler" />
            <result property="industryLevel2" column="industry_level2" jdbcType="OTHER" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler" />
            <result property="industryLeaders" column="industry_leaders" jdbcType="OTHER" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
            <result property="tasksMoney" column="tasks_money" jdbcType="DECIMAL"/>
            <result property="remarks" column="remarks" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,industry_level1,industry_level2,
        industry_leaders,tasks_money,remarks,
        del_flag,create_user,update_user,
        create_time,update_time
    </sql>
</mapper>
