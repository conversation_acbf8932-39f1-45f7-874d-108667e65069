package com.topsec.crm.stats.core.controller.moc;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.topsec.crm.file.api.RemoteFileService;
import com.topsec.crm.framework.common.bean.CrmFsmDoc;
import com.topsec.crm.framework.common.name.NameUtils;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.project.api.client.RemoteApproveConfigClient;
import com.topsec.crm.project.api.entity.CrmPersonnelGroupVO;
import com.topsec.crm.stats.api.entity.MocAnalysisPermissionsVo;
import com.topsec.crm.stats.api.entity.MocAnalysisVo;
import com.topsec.crm.stats.api.entity.MocSourceDisableVo;
import com.topsec.crm.stats.core.entity.moc.MocAnalysis;
import com.topsec.crm.stats.core.entity.moc.MocAnalysisPermissions;
import com.topsec.crm.stats.core.entity.moc.MocSourceDisable;
import com.topsec.crm.stats.core.service.IMocAnalysisPermissionsService;
import com.topsec.crm.stats.core.service.IMocAnalysisService;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.api.client.TosDepartmentClient;
import com.topsec.tos.api.client.TosEmployeeClient;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.tos.common.query.SimpleDeptQuery;
import com.topsec.tos.common.vo.EmployeeVO;
import com.topsec.tos.common.vo.TosDepartmentVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/moc/analysis")
@Tag(name = "【数据分析基本信息管理】", description = "【数据分析基本信息管理】")
public class MocAnalysisController extends BaseController {

    @Resource
    private IMocAnalysisService mocAnalysisService;
    @Resource
    private IMocAnalysisPermissionsService mocAnalysisPermissionsService;
    @Resource
    private RemoteFileService remoteFileService;
    @Resource
    private TosEmployeeClient tosEmployeeClient;
    @Resource
    private TosDepartmentClient tosDepartmentClient;
    @Autowired
    private RemoteApproveConfigClient remoteApproveConfigClient;

    @PostMapping(value = "/page")
    @Operation(summary = "数据分析基本信息分页")
    public JsonObject<PageUtils<MocAnalysisVo>> page(@RequestBody MocAnalysisVo mocAnalysisVo) {
        //查询有查看权限的分析表id
        String personId = UserInfoHolder.getCurrentPersonId();
        //查询当前用户所属部门
        JsonObject<List<TosDepartmentVO>> byPersonId = tosDepartmentClient.findByPersonId(personId);
        Assert.isFalse(!byPersonId.isSuccess() || CollectionUtils.isEmpty(byPersonId.getObjEntity()), "未查询到当前员工所在部门，请联系管理员。");
        //查询用户所在人员组
        JsonObject<List<CrmPersonnelGroupVO>> listJsonObject = remoteApproveConfigClient.personnelGroupDetails(personId);
        Assert.isFalse(!listJsonObject.isSuccess() , "用户成员组获取失败，请联系管理员。");
        List<String> groupIds = listJsonObject.getObjEntity().stream().map(CrmPersonnelGroupVO::getId).toList();

        List<String> deptIds = byPersonId.getObjEntity().stream().map(TosDepartmentVO::getUuid).toList();
        List<MocAnalysisPermissions> list = mocAnalysisPermissionsService.query()
                .eq("person_id", personId)
                .or().in("dept_id", deptIds)
                .or().in(CollectionUtils.isNotEmpty(groupIds), "group_id", groupIds)
                .list();
        Set<String> orInPartAnas = list.stream().map(MocAnalysisPermissions::getAnalysisId).collect(Collectors.toSet());

        startPage();
        List<MocAnalysis> analyses = mocAnalysisService.query()
                .like(StringUtils.isNotBlank(mocAnalysisVo.getName()), "name", mocAnalysisVo.getName())
                .eq(StringUtils.isNotBlank(mocAnalysisVo.getCreateUser()), "create_user", mocAnalysisVo.getCreateUser())
                .eq(StringUtils.isNotNull(mocAnalysisVo.getLoseEfficacy()), "lose_efficacy", mocAnalysisVo.getLoseEfficacy())
                .eq(StringUtils.isNotNull(mocAnalysisVo.getStatus()), "status", mocAnalysisVo.getStatus())
                .between(StringUtils.isNotNull(mocAnalysisVo.getStartDate()) && StringUtils.isNotNull(mocAnalysisVo.getEndDate()), "create_time",  mocAnalysisVo.getStartDate(), mocAnalysisVo.getEndDate())
                .and(wrapper -> {
                     wrapper.eq("create_user", personId)
                             .or().
                             in(CollectionUtils.isNotEmpty(orInPartAnas),"id", orInPartAnas);
                })
                .orderByDesc("create_time")
                .list();

        List<MocAnalysisVo> listVo = HyperBeanUtils.copyListPropertiesByJackson(analyses, MocAnalysisVo.class);
        //list对象为分页后的代理对象，如果转成VO对象，代理对象会失效，即total获取不到，需要重新封装
        PageUtils dataTable = getDataTable(list,listVo);
        return new JsonObject<>(dataTable);
    }

    @GetMapping(value = "/get")
    @Operation(summary = "获取数据分析表基本信息")
    public JsonObject<MocAnalysisVo> get(@RequestParam("id") String id) throws Exception {
        Assert.isFalse(checkHasAuth(id).getObjEntity() == 0, "无当前分析表查看权限，请联系管理员。");

        //1.查询基本信息
        MocAnalysis mocSource = mocAnalysisService.getById(id);
        MocAnalysisVo mocAnalysisVo = HyperBeanUtils.copyPropertiesByJackson(mocSource, MocAnalysisVo.class);

        //2.查询分析表权限列表
        List<MocAnalysisPermissions> permissionss = mocAnalysisPermissionsService.query().eq("analysis_id", id).list();
        List<MocAnalysisPermissionsVo> listVo = HyperBeanUtils.copyListPropertiesByJackson(permissionss, MocAnalysisPermissionsVo.class);
        if(CollectionUtils.isNotEmpty(listVo)){
            //2.1批量获取用户信息
            List<String> personIds = listVo.stream().map(MocAnalysisPermissionsVo::getPersonId).toList();
            List<EmployeeVO> employeeVOS = new ArrayList<EmployeeVO>();
            if(CollectionUtil.isNotEmpty(personIds)){
                JsonObject<List<EmployeeVO>> byIds = tosEmployeeClient.findByIds(personIds);
                if (byIds.isSuccess() && byIds.getObjEntity() != null)
                    employeeVOS = byIds.getObjEntity();
            }
            //2.1批量获取部门信息
            List<String> deptIds = listVo.stream().map(MocAnalysisPermissionsVo::getDeptId).toList();
            List<TosDepartmentVO> departmentVOS = new ArrayList<TosDepartmentVO>();
            if(CollectionUtil.isNotEmpty(deptIds)){
                SimpleDeptQuery departmentQuery = new SimpleDeptQuery();
                departmentQuery.setPageNo(1);
                departmentQuery.setPageSize(1000);
                departmentQuery.setDeptIdList(deptIds);
                JsonObject<Page<TosDepartmentVO>> byIds = tosDepartmentClient.pageSimpleDept(departmentQuery);
                if (byIds.isSuccess() && byIds.getObjEntity() != null)
                    departmentVOS = byIds.getObjEntity().getRecords();
            }

            for (MocAnalysisPermissionsVo permissionsVo : listVo) {
                //补充部门名称
                if(StringUtils.isNotBlank(permissionsVo.getDeptId())){
                    TosDepartmentVO departmentVo = departmentVOS.stream().filter(e -> e.getUuid().equals(permissionsVo.getDeptId())).findFirst().orElse(null);
                    permissionsVo.setDeptName(departmentVo != null ? departmentVo.getName() : null);
                }
                //补充人员名称
                if(StringUtils.isNotBlank(permissionsVo.getPersonId())){
                    EmployeeVO employeeVO = employeeVOS.stream().filter(e -> e.getUuid().equals(permissionsVo.getPersonId())).findFirst().orElse(null);
                    permissionsVo.setPersonName(NameUtils.getNameByEmployeeVO(employeeVO));
                }
                //TODO minggang补充人员组名称
            }

        }
        mocAnalysisVo.setPermissionsVos(listVo);
        

        return new JsonObject<>(mocAnalysisVo);
    }

    @PostMapping(value = "/saveBasicInfo")
    @Operation(summary = "保存 数据分析基本信息")
    public JsonObject<Boolean> saveBasicInfo(@RequestBody MocAnalysisVo mocAnalysisVo) throws Exception {
        //1.重名数据源校验
        List<MocAnalysis> exist = mocAnalysisService.query().eq("name", mocAnalysisVo.getName()).eq("del_flag", 0).list();
        Assert.isFalse(CollectionUtil.isNotEmpty(exist), "存在重名数据分析表，请重新编辑");

        //2.保存基本信息
        String analysisId = UUID.randomUUID().toString();
        MocAnalysis mocAnalysis = HyperBeanUtils.copyPropertiesByJackson(mocAnalysisVo, MocAnalysis.class);
        mocAnalysis.setId(analysisId);
        mocAnalysis.setStatus(1);

        //3.查询创建人姓名
        JsonObject<EmployeeVO> byId = tosEmployeeClient.findById(UserInfoHolder.getCurrentPersonId());
        if(byId.isSuccess() && byId.getObjEntity() != null){
            mocAnalysis.setCreateUserName(byId.getObjEntity().getName());
        }

        return new JsonObject<>(mocAnalysisService.save(mocAnalysis));
    }

    @PostMapping(value = "/updateBasicInfo")
    @Operation(summary = "修改数据分析基本信息")
    public JsonObject<Boolean> updateBasicInfo(@RequestBody MocAnalysisVo mocAnalysisVo) throws Exception {
        Assert.isFalse(checkHasAuth(mocAnalysisVo.getId()).getObjEntity() != 2, "无当前分析表编辑权限，请联系管理员。");

        //重名校验
        List<MocAnalysis> exist = mocAnalysisService.query()
                .eq("name", mocAnalysisVo.getName())
                .ne("id",mocAnalysisVo.getId())
                .eq("del_flag", 0)
                .list();
        Assert.isFalse(CollectionUtil.isNotEmpty(exist), "存在重名数据分析表，请重新编辑");

        MocAnalysis mocAnalysis = HyperBeanUtils.copyPropertiesByJackson(mocAnalysisVo, MocAnalysis.class);
        return new JsonObject<>(mocAnalysisService.updateById(mocAnalysis));
    }

    @PostMapping(value = "/saveOrUpdateTemplate")
    @Operation(summary = "保存/更新 数据分析模板")
    public JsonObject<Boolean> saveOrUpdateTemplate(@RequestBody MocAnalysisVo mocAnalysisVo) throws Exception {
        Assert.isFalse(checkHasAuth(mocAnalysisVo.getId()).getObjEntity() != 2, "无当前分析表编辑权限，请联系管理员。");


        mocAnalysisService.updateById(HyperBeanUtils.copyPropertiesByJackson(mocAnalysisVo, MocAnalysis.class));
        return new JsonObject<>(true);
    }

    @PostMapping(value = "/saveOrUpdatePermissions")
    @Operation(summary = "保存/更新 数据分析权限")
    public JsonObject<Boolean> saveOrUpdatePermissions(@RequestBody MocAnalysisVo mocAnalysisVo) throws Exception {
        Assert.isFalse(checkHasAuth(mocAnalysisVo.getId()).getObjEntity() != 2, "无当前分析表编辑权限，请联系管理员。");

        //删除历史的权限数据
        mocAnalysisPermissionsService.remove(new QueryWrapper<MocAnalysisPermissions>()
                .eq("analysis_id", mocAnalysisVo.getId()));

        //3.保存分析表权限
        if(CollectionUtil.isNotEmpty(mocAnalysisVo.getPermissionsVos())){
            List<MocAnalysisPermissionsVo> permissionss = mocAnalysisVo.getPermissionsVos();

            for (MocAnalysisPermissionsVo permissions : permissionss) {
                permissions.setId(UUID.randomUUID().toString());
                permissions.setAnalysisId(mocAnalysisVo.getId());
            }
            mocAnalysisPermissionsService.saveOrUpdateBatch(HyperBeanUtils.copyListPropertiesByJackson(permissionss, MocAnalysisPermissions.class));
        }
        return new JsonObject<>(true);
    }

    @PostMapping(value = "/setStatus")
    @Operation(summary = "报表失效/正常")
    public JsonObject<Boolean> setStatus(@RequestBody MocAnalysisVo mocAnalysisVo) throws Exception {
        Assert.isFalse(checkHasAuth(mocAnalysisVo.getId()).getObjEntity() != 2, "无当前分析表编辑权限，请联系管理员。");

        MocAnalysis mocAnalysis = new MocAnalysis();
        mocAnalysis.setId(mocAnalysisVo.getId());
        mocAnalysis.setStatus(mocAnalysisVo.getStatus());
        mocAnalysisService.updateById(mocAnalysis);
        return new JsonObject<>(true);
    }

    @PostMapping(value = "/delete")
    @Operation(summary = "删除报表")
    public JsonObject<Boolean> delete(@RequestBody MocAnalysisVo mocAnalysisVo) throws Exception {
        Assert.isFalse(checkHasAuth(mocAnalysisVo.getId()).getObjEntity() != 2, "无当前分析表编辑权限，请联系管理员。");

        mocAnalysisService.update(new UpdateWrapper<MocAnalysis>()
                .set("del_flag",1)
                .eq("id", mocAnalysisVo.getId()));
        return new JsonObject<>(true);
    }

    @PostMapping(value = "/checkHasAuth")
    @Operation(summary = "检测当前用户有什么权限，取最大的权限(0：无权限，1：仅查看，2：可编辑)")
    public JsonObject<Integer> checkHasAuth(@RequestParam(value = "analysisId") String analysisId) throws Exception {
        String personId = UserInfoHolder.getCurrentPersonId();
        //查询当前用户所属部门
        JsonObject<List<TosDepartmentVO>> byPersonId = tosDepartmentClient.findByPersonId(personId);
        Assert.isFalse(!byPersonId.isSuccess() || CollectionUtils.isEmpty(byPersonId.getObjEntity()), "未查询到当前员工所在部门，请联系管理员。");
        List<String> deptIds = byPersonId.getObjEntity().stream().map(TosDepartmentVO::getUuid).toList();

        MocAnalysis mocA = mocAnalysisService.query().eq("id", analysisId).one();
        if(personId.equals(mocA.getCreateUser())){
            return new JsonObject<>(2);//创建人默认拥有编辑权限
        }

        //查询用户所在人员组
        JsonObject<List<CrmPersonnelGroupVO>> listJsonObject = remoteApproveConfigClient.personnelGroupDetails(UserInfoHolder.getCurrentPersonId());
        Assert.isFalse(!listJsonObject.isSuccess() , "用户成员组获取失败，请联系管理员。");
        List<String> groupIds = listJsonObject.getObjEntity().stream().map(CrmPersonnelGroupVO::getId).toList();

        //查询用户所在人员组
        List<MocAnalysisPermissions> list = mocAnalysisPermissionsService.query()
                .eq("analysis_id", analysisId)
                .and(wrapper -> {
                    wrapper.eq("person_id", personId)
                            .or().in("dept_id", deptIds)
                            .or().in(CollectionUtils.isNotEmpty(groupIds), "group_id", groupIds);
                })
                .list();

        if(CollectionUtil.isNotEmpty(list)){
            Integer per = list.stream().mapToInt(MocAnalysisPermissions::getVisitPermissions).max().orElse(0);
            return new JsonObject<>(per);
        }else{
            return new JsonObject<>(0);
        }
    }

}
