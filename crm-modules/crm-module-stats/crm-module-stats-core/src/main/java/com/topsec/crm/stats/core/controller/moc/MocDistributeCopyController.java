package com.topsec.crm.stats.core.controller.moc;

import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 对生成好的Excel结果集做分发
 * 填报后将填报数据组合到Excel中
 *
 * 涉及：
 * 1.Excel结果集文件存储
 * 2.Excel结果集存储到MongoDB
 * 3.数据填报  excel列与值对应关系
 * 4.数据回显  将填报的值，渲染到对应的单元格中
 */
@RestController
@RequestMapping("/moc/distribute/copy")
@Tag(name = "【数据分发管理】", description = "【数据分发管理】")
public class MocDistributeCopyController extends BaseController {

    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private MongoBulkInserter mongoBulkInserter;

    /**
     * 1.Excel结果集文件存储
     * 2.Excel结果集存储到MongoDB
     */
    @CrossOrigin
    @PostMapping(value = "/save", headers = ("content-type=multipart/form-data"))
    @Operation(summary = "数据分发基础数据保存")
    public JsonObject<Boolean> save(@RequestParam("headerColumn") Integer headerColumn,
                @RequestParam("distributeFile") MultipartFile distributeFile,
                @RequestParam String editClms) throws Exception{
        //1.Excel结果集存储到MongoDB
        ExcelReader reader = ExcelUtil.getReader(distributeFile.getInputStream());
        // 通过名称获取 Sheet
        ExcelReader namedSheet = reader.setSheet("Sheet1");
        // 将第一行作为标题，第二行开始为数据
        List<Map<String, Object>> rows = namedSheet.read(headerColumn, headerColumn+1,namedSheet.getRowCount());
        // 转换为 JSON 数组
        String json = JSONObject.toJSONString(rows, SerializerFeature.WriteMapNullValue);
        Set<Object> ins = JSONArray.parseArray(json).stream().collect(Collectors.toSet());

        //批量插入数据，（会删掉历史的集合，新建一个集合）
        mongoBulkInserter.insertAll(ins,"这是一个新的Sheet");

        return new JsonObject<>(true);
    }

    /**
     * 数据填报
     */
    @CrossOrigin
    @PostMapping(value = "/update")
    @Operation(summary = "数据填报  excel列与值对应关系")
    public JsonObject<Boolean> update(@RequestParam("headerColumn") Integer headerColumn,
                                    @RequestBody List<JSONObject> content) throws Exception{
        //批量修改
        for (JSONObject item : content) {
            Query query = new Query(Criteria.where("_id").is(item.getString("_id")));
            Update update = new Update(); // 设置要更新的字段和值
            for (String s : item.keySet()) {
                if(!s.equals("_id")) {
                    update.set(s, item.get(s));
                }
            }
            mongoTemplate.upsert(query, update, "这是一个新的Sheet");
        }

        return new JsonObject<>(true);
    }

    /**
     * 查询分发数据
     *
     * TODO 需要结合分发权限表
     */
    @CrossOrigin
    @PostMapping(value = "/getDistributeInfo")
    @Operation(summary = "查询分发数据")
    public JsonObject<List<JSONObject>> getDistributeInfo(@RequestParam("headerColumn") Integer headerColumn){
        //结合权限表，查询对应数据
        List<JSONObject> all = mongoTemplate.findAll(JSONObject.class,"这是一个新的Sheet");
        List<JSONObject> collect = all.stream().map(doc -> {
            doc.put("_id", doc.getString("_id").toString());
            return doc;
        }).collect(Collectors.toList());
        return new JsonObject<>(collect);
    }

}
