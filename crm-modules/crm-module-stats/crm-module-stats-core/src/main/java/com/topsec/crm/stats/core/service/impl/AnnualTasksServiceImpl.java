package com.topsec.crm.stats.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.topsec.crm.framework.common.bean.FlowPerson;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.AccountAccquireUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.util.poi.ExcelUtil;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.stats.api.entity.tasks.*;
import com.topsec.crm.stats.api.enums.TasksDeptType;
import com.topsec.crm.stats.api.enums.TasksSheetType;
import com.topsec.crm.stats.core.entity.task.AnnualTasks;
import com.topsec.crm.stats.core.mapper.AnnualTasksMapper;
import com.topsec.crm.stats.core.service.AnnualTasksService;
import com.topsec.crm.stats.core.service.SaleTeamService;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.api.client.TosDepartmentClient;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.tos.common.enums.RelationTypeEnum;
import com.topsec.tos.common.query.DepartmentQuery;
import com.topsec.tos.common.vo.TosDepartmentTreeVO;
import com.topsec.tos.common.vo.TosDepartmentVO;
import io.seata.common.util.CollectionUtils;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【annual_tasks(年度任务)】的数据库操作Service实现
* @createDate 2025-07-18 16:13:19
*/
@Service
public class AnnualTasksServiceImpl extends ServiceImpl<AnnualTasksMapper, AnnualTasks>
    implements AnnualTasksService{

    @Resource
    private TosDepartmentClient tosDepartmentClient;
    @Resource
    private SaleTeamService saleTeamService;


    @Override
    public TableDataInfo annualTTasksPage(AnnualTasksPageQueryVO queryVO,Integer tasksType) {
        List<AnnualTasks> annualTasksList = baseMapper.selectList(buildQueryWrapper(queryVO)
                .eq(AnnualTasks::getTasksType,tasksType));
        return getPageInf(annualTasksList,tasksType);
    }

    @Override
    public Boolean saveOrUpAnnualTasks(AnnualTasksVO annualTasksVO) {
        AnnualTasks annualTasks = HyperBeanUtils.copyProperties(annualTasksVO,AnnualTasks::new);
        annualTasks.setType(getType(annualTasks.getTasksType(),annualTasks.getTasksType()));
        return saveOrUpdate(annualTasks);
    }

    @Override
    public Boolean deleteAnnualTasks(String annualTaskId) {
        Optional<AnnualTasks> optional = Optional.ofNullable(getById(annualTaskId));
        if (optional.isPresent()){
            AnnualTasks annualTasks = optional.get();
            annualTasks.setDelFlag(1);
            return updateById(annualTasks);
        }else{
            throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
        }
    }

    @Override
    public AnnualTasksVO getAnnualTasksById(String annualTaskId) {
        AnnualTasks annualTasks = getById(annualTaskId);
        return HyperBeanUtils.copyPropertiesByJackson(annualTasks, AnnualTasksVO.class);
    }

    @Override
    public List<AnnualTasksVO> exportAnnualTasks(AnnualTasksVO annualTasksVOQuery) {
        List<AnnualTasks> annualTasks = list(new LambdaQueryWrapper<AnnualTasks>().eq(AnnualTasks::getDelFlag,0)
                .eq(!StringUtils.isEmpty(annualTasksVOQuery.getDeptId()),AnnualTasks::getDeptId,annualTasksVOQuery.getDeptId())
                .eq(AnnualTasks::getTasksType,annualTasksVOQuery.getTasksType())
                .like(!Objects.isNull(annualTasksVOQuery.getYear()),AnnualTasks::getYear,annualTasksVOQuery.getYear())
                .eq(!Objects.isNull(annualTasksVOQuery.getDeptType()),AnnualTasks::getDeptType,annualTasksVOQuery.getDeptType()));
        return HyperBeanUtils.copyListProperties(annualTasks,AnnualTasksVO::new);
    }

    @Override
    public AnnualTaskImportResultVO importPlatformTasks(MultipartFile file) {
        ExcelUtil<AnnualTasksImportVO> excelUtil = new ExcelUtil<>(AnnualTasksImportVO.class);
        try {
            List<AnnualTasksImportVO> platformTasks = excelUtil.importExcel(file.getInputStream());
            if (CollectionUtils.isEmpty(platformTasks)) {
                return AnnualTaskImportResultVO.fail(Arrays.asList("导入文件为空或格式不正确"), 0);
            }
            Set<String> deptNames = platformTasks.stream().filter(q->StringUtils.isNotEmpty(q.getDeptName())).map(AnnualTasksImportVO::getDeptName).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(deptNames)) return AnnualTaskImportResultVO.fail(Arrays.asList("部门信息不存在"), 0);
            JsonObject<List<TosDepartmentVO>> deptObject = tosDepartmentClient.queryByNameList(deptNames.stream().toList());
            if (deptObject.isSuccess() && CollectionUtils.isNotEmpty(deptObject.getObjEntity())){
                Map<String,TosDepartmentVO> deptMap = deptObject.getObjEntity().stream().collect(Collectors.toMap(TosDepartmentVO::getName,(o->o)));
                List<String> errorMessages = new ArrayList<>();
                Set<String> deptName = new HashSet<>();
                for (int i=0;i<platformTasks.size();i++){
                    AnnualTasksImportVO annualTasksImportVO = platformTasks.get(i);
                    Integer row = i+1;
                    if (!Objects.isNull(annualTasksImportVO.getYear())){
                        errorMessages.add("第" +row+ "行：年度不能为空");
                    }
                    if (StringUtils.isEmpty(annualTasksImportVO.getDeptName())){
                        errorMessages.add("第" +row+ "行：业务部门不能为空");
                    }
                    if (deptName.contains(annualTasksImportVO.getDeptName())){
                        errorMessages.add("第" +row+ "行："+annualTasksImportVO.getDeptName()+"部门重复");
                    }
                    if (deptMap.get(annualTasksImportVO.getDeptName())==null){
                        errorMessages.add("第" +row+ "行："+annualTasksImportVO.getDeptName()+"部门信息不存在");
                    }
                    deptName.add(annualTasksImportVO.getDeptName());
                    annualTasksImportVO.setDeptId(deptMap.get(annualTasksImportVO.getDeptName()).getUuid());
                    annualTasksImportVO.setTaskType(0);
                }
                Map<Integer,List<AnnualTasksImportVO>> map= platformTasks.parallelStream().collect(Collectors.groupingBy(entity->(entity .getYear()),Collectors.toList()));
                if (map.size()>1){
                    errorMessages.add("年度信息必须一致");
                }
                if (CollectionUtils.isNotEmpty(errorMessages)) {
                    return AnnualTaskImportResultVO.fail(errorMessages, errorMessages.size());
                }
                return importAnnualTaskstVO(platformTasks);
            }else{
                return AnnualTaskImportResultVO.fail(Arrays.asList("部门信息不存在"), 0);
            }
        }catch (Exception e){
            throw new CrmException("执行导入失败：" + e.getMessage());
        }
    }

    @Override
    public AnnualTaskImportResultVO importAnnualTasks(MultipartFile file) {
        ExcelUtil<AnnualTasksImportVO> excelUtil = new ExcelUtil<>(AnnualTasksImportVO.class);
        try {
            List<AnnualTasksImportVO> deprTasks = excelUtil.importExcel("部门任务",file.getInputStream());
            List<AnnualTasksImportVO> teamTasks = excelUtil.importExcel("团队任务",file.getInputStream());
            List<AnnualTasksImportVO> saleTasks = excelUtil.importExcel("个人任务",file.getInputStream());
            if (CollectionUtils.isEmpty(deprTasks) && CollectionUtils.isEmpty(teamTasks) && CollectionUtils.isEmpty(saleTasks)) {
                return AnnualTaskImportResultVO.fail(Arrays.asList("导入文件为空或格式不正确"), 0);
            }
            Set<String> deptNameSet = new HashSet<>();
            Set<String> deptNames = deprTasks.stream().filter(q->StringUtils.isNotEmpty(q.getDeptName()))
                    .map(AnnualTasksImportVO::getDeptName).collect(Collectors.toSet());
            Set<String> teamDeptNames = teamTasks.stream().filter(q->StringUtils.isNotEmpty(q.getDeptName()))
                    .map(AnnualTasksImportVO::getDeptName).collect(Collectors.toSet());
            Set<String> saleDepttNames = saleTasks.stream().filter(q->StringUtils.isNotEmpty(q.getDeptName()))
                    .map(AnnualTasksImportVO::getDeptName).collect(Collectors.toSet());
            deptNameSet.addAll(deptNames);
            deptNameSet.addAll(teamDeptNames);
            deptNameSet.addAll(saleDepttNames);
            if (CollectionUtils.isEmpty(deptNameSet)) return AnnualTaskImportResultVO.fail(Arrays.asList("部门信息不存在"), 0);
            JsonObject<List<TosDepartmentVO>> deptObject = tosDepartmentClient.queryByNameList(deptNames.stream().toList());
            if (deptObject.isSuccess() && CollectionUtils.isNotEmpty(deptObject.getObjEntity())){
                List<TosDepartmentVO> departmentVO = deptObject.getObjEntity();
                Map<String,Object> deprMap =getImportInfo(deprTasks,departmentVO,1);
                Map<String,Object> teamMap =getImportInfo(teamTasks,departmentVO,2);
                Map<String,Object> saleMap =getImportInfo(saleTasks,departmentVO,3);
                List<String> errorMessages = new ArrayList<>();
                errorMessages.addAll((List<String>) deprMap.get("errorMessages"));
                errorMessages.addAll((List<String>) teamMap.get("errorMessages"));
                errorMessages.addAll((List<String>) saleMap.get("errorMessages"));
                if (CollectionUtils.isNotEmpty(errorMessages)) {
                    return AnnualTaskImportResultVO.fail(errorMessages, errorMessages.size());
                }
                List<AnnualTasksImportVO> tasksImportVOS = new ArrayList<>();
                tasksImportVOS.addAll((List<AnnualTasksImportVO>)deprMap.get("annualTasksImportVOS"));
                tasksImportVOS.addAll((List<AnnualTasksImportVO>)teamMap.get("annualTasksImportVOS"));
                tasksImportVOS.addAll((List<AnnualTasksImportVO>)saleMap.get("annualTasksImportVOS"));
                return importAnnualTaskstVO(tasksImportVOS);
            }else{
                return AnnualTaskImportResultVO.fail(Arrays.asList("部门信息不存在"), 0);
            }
        }catch (Exception e){
            throw new CrmException("执行导入失败：" + e.getMessage());
        }
    }

    @Override
    public TableDataInfo getSubtaskByDeptId(String deptId,Integer year,Integer type) {
        TableDataInfo dataInfo =new TableDataInfo();
        DepartmentQuery departmentQuery = new DepartmentQuery();
        departmentQuery.setDeptId(deptId);
        departmentQuery.setFetchChild(true);
        departmentQuery.setPrincipalRelFilterQuery(List.of(RelationTypeEnum.LEADER));
        departmentQuery.setPageSize(Integer.MAX_VALUE);
        JsonObject<Page<TosDepartmentTreeVO>> deptObject = tosDepartmentClient.pageChildrenDept(departmentQuery);
        if (deptObject.isSuccess() && CollectionUtils.isNotEmpty(deptObject.getObjEntity().getRecords())){
            Set<String> deptIds = deptObject.getObjEntity().getRecords().stream().map(TosDepartmentTreeVO::getUuid).collect(Collectors.toSet());
            AnnualTasksPageQueryVO queryVO = new AnnualTasksPageQueryVO();
            queryVO.setYear(year);
            List<AnnualTasks> annualTasksList = baseMapper.selectList(buildQueryWrapper(queryVO)
                    .in(AnnualTasks::getDeptId,deptIds).eq(AnnualTasks::getTasksType,type));
            return getPageInf(annualTasksList,type);
        }
        return dataInfo;
    }

    private TableDataInfo getPageInf(List<AnnualTasks> annualTasksList,Integer type){
        TableDataInfo dataInfo =new TableDataInfo();
        if (CollectionUtils.isEmpty(annualTasksList)) return dataInfo;
        if (type==3){
            List<String> persongIds = annualTasksList.stream().map(AnnualTasks::getPersonId).toList();
            List<FlowPerson> flowPersonList = AccountAccquireUtils.convertGetAccountByPersonId(persongIds);
            Map<String,FlowPerson> flowPersonMap = flowPersonList.stream().collect(Collectors.toMap(FlowPerson::getPersonId,(o->o)));
            List<AnnualTasksVO>  annualTasksVOS = annualTasksList.stream().map(annualTasks -> {
                FlowPerson flowPerson = flowPersonMap.get(annualTasks.getPersonId());
                AnnualTasksVO annualTasksVO = HyperBeanUtils.copyPropertiesByJackson(annualTasks, AnnualTasksVO.class);
                if (flowPerson!=null) annualTasksVO.setPersonName(flowPerson.getPersonName()+flowPerson.getJobNo());
                return annualTasksVO;
            }).toList();
            dataInfo.setTotalCount(new PageInfo<>(annualTasksList).getTotal());
            dataInfo.setList(annualTasksVOS);
        }else{
            List<AnnualTasksVO>  annualTasksVOS = HyperBeanUtils.copyListProperties(annualTasksList,AnnualTasksVO::new);
            dataInfo.setTotalCount(new PageInfo<>(annualTasksList).getTotal());
            dataInfo.setList(annualTasksVOS);
        }
        return dataInfo;
    }

    private LambdaQueryWrapper<AnnualTasks> buildQueryWrapper(AnnualTasksPageQueryVO queryVO) {
        return new LambdaQueryWrapper<AnnualTasks>()
                .eq(AnnualTasks::getDelFlag, false)
                .in(!StringUtils.isEmpty(queryVO.getDeptId()),AnnualTasks::getDeptId,queryVO.getDeptId())
                .like(!Objects.isNull(queryVO.getYear()),AnnualTasks::getYear,queryVO.getYear())
                .eq(!Objects.isNull(queryVO.getDeptType()),AnnualTasks::getDeptType,queryVO.getDeptType());
    }

    private Map<String,Object> getImportInfo(List<AnnualTasksImportVO> annualTasksImportVOS,List<TosDepartmentVO> departmentVOS,Integer type){
        Map<String,Object> returnMap = new HashMap<>();
        Map<String,TosDepartmentVO> deptMap = departmentVOS.stream().collect(Collectors.toMap(TosDepartmentVO::getName,(o->o)));
        List<String> errorMessages = new ArrayList<>();
        Set<String> deptName = new HashSet<>();
        Set<String> jobNo = new HashSet<>();
        String sheetType= TasksSheetType.valueOf(type).getValue();

        Map<Integer,List<AnnualTasksImportVO>> map= annualTasksImportVOS.parallelStream().collect(Collectors.groupingBy(entity->(entity .getYear()),Collectors.toList()));
        if (map.size()>1){
            errorMessages.add("年度信息必须一致");
            returnMap.put("errorMessages",errorMessages);
            returnMap.put("annualTasksImportVOS",annualTasksImportVOS);
            return returnMap;
        }
        Integer year = annualTasksImportVOS.get(0).getYear();
        Map<String,SaleTeamVO> teamVOMap = type==2? new HashMap<>():saleTeamService.listAll(year).stream().collect(Collectors.toMap(SaleTeamVO::getTeamName,(o->o)));
        for (int i=0;i<annualTasksImportVOS.size();i++){
            AnnualTasksImportVO annualTasksImportVO = annualTasksImportVOS.get(i);
            Integer row = i+1;
            if (!Objects.isNull(annualTasksImportVO.getYear())){
                errorMessages.add("Sheet"+sheetType+"第" +row+ "行：年度不能为空");
            }
            if (StringUtils.isEmpty(annualTasksImportVO.getDeptName())){
                errorMessages.add("Sheet"+sheetType+"第" +row+ "行：业务部门不能为空");
            }
            if (deptName.contains(annualTasksImportVO.getDeptName())){
                errorMessages.add("Sheet"+sheetType+"第" +row+ "行："+annualTasksImportVO.getDeptName()+"部门重复");
            }
            if (deptMap.get(annualTasksImportVO.getDeptName())==null){
                errorMessages.add("Sheet"+sheetType+"第" +row+ "行："+annualTasksImportVO.getDeptName()+"部门信息不存在");
            }else{
                annualTasksImportVO.setDeptId(deptMap.get(annualTasksImportVO.getDeptName()).getUuid());
            }
            if (StringUtils.isEmpty(annualTasksImportVO.getDeptType())){
                errorMessages.add("Sheet"+sheetType+"第" +row+ "行：部门类别不能为空");
            }
            if (StringUtils.isEmpty(annualTasksImportVO.getIndustryLevel1())){
                errorMessages.add("Sheet"+sheetType+"第" +row+ "行：一级行业不能为空");
            }
            if (StringUtils.isNotEmpty(annualTasksImportVO.getIndustryLevel2()) && deptMap.get(annualTasksImportVO.getIndustryLevel2())==null){
                errorMessages.add("Sheet"+sheetType+"第" +row+ "行：二级行业不存在");
            }
            if (StringUtils.isEmpty(annualTasksImportVO.getSaleTeam()) && type==2 ){
                errorMessages.add("Sheet"+sheetType+"第" +row+ "行：销售团队不能为空");
            }else if(type==2 && teamVOMap.get(annualTasksImportVO.getSaleTeam())==null){
                errorMessages.add("Sheet"+sheetType+"第" +row+ "行：销售团队不存在");
            }else{
                annualTasksImportVO.setSaleTeamId(teamVOMap.get(annualTasksImportVO.getSaleTeam()).getId());
            }
            if (StringUtils.isEmpty(annualTasksImportVO.getJobNo()) && type==3){
                errorMessages.add("Sheet"+sheetType+"第" +row+ "行：工号不能为空");
            }
            if (jobNo.contains(annualTasksImportVO.getJobNo()) && type==3){
                errorMessages.add("Sheet"+sheetType+"第" +row+ "行："+annualTasksImportVO.getJobNo()+"工号重复");
            }
            deptName.add(annualTasksImportVO.getDeptName());
            jobNo.add(annualTasksImportVO.getJobNo());
            annualTasksImportVO.setTaskType(type);
        }

        returnMap.put("errorMessages",errorMessages);
        returnMap.put("annualTasksImportVOS",annualTasksImportVOS);
        return returnMap;
    }

    private AnnualTaskImportResultVO importAnnualTaskstVO(List<AnnualTasksImportVO> annualTasksImportVOS){
        try {
            //获取重复数据
            List<AnnualTasks> annualTasksList = list(new LambdaQueryWrapper<AnnualTasks>().eq(AnnualTasks::getDelFlag, 0).like(AnnualTasks::getYear, annualTasksImportVOS.get(0).getYear())
                    .in(AnnualTasks::getDeptId, annualTasksImportVOS.stream().map(AnnualTasksImportVO::getDeptId).toList()));

            if (CollectionUtils.isNotEmpty(annualTasksList)){
                //删除重复数据
                LambdaQueryWrapper<AnnualTasks> delQueryWrapper = new LambdaQueryWrapper<>();
                delQueryWrapper.in(AnnualTasks::getDeptId, annualTasksList.stream().map(AnnualTasks::getDeptId).toList());
                delQueryWrapper.eq(AnnualTasks::getDelFlag, 0);
                AnnualTasks annualTasks = new AnnualTasks();
                annualTasks.setDelFlag(1);
                this.update(annualTasks, delQueryWrapper);
            }


            List<AnnualTasks> annualTasksEntity = annualTasksImportVOS.stream().map(annualTasksImportVO -> {
                AnnualTasks tasks = HyperBeanUtils.copyProperties(annualTasksImportVO, AnnualTasks::new);
                return tasks;
            }).toList();

            if (saveBatch(annualTasksEntity)) {
                return AnnualTaskImportResultVO.success(annualTasksImportVOS.size(), annualTasksImportVOS.size());
            } else {
                return AnnualTaskImportResultVO.fail(Arrays.asList("保存数据失败"), annualTasksImportVOS.size());
            }
        } catch (Exception e) {
            throw new CrmException("执行导入失败：" + e.getMessage());
        }

    }

    /**
     * 计算分类
     * @param tasksType
     * @param deptType
     * @return
     */
    private Integer getType(Integer tasksType,Integer deptType){
        if (tasksType!=1) return tasksType;
        return TasksDeptType.getTypeEnum(deptType).getValue();
    }


}




