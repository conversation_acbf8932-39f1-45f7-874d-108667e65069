package com.topsec.crm.stats.core.entity.task;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.topsec.crm.framework.common.web.domain.BaseEntity;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 行业任务
 * @TableName industry_tasks
 */
@TableName(value ="industry_tasks",autoResultMap=true)
@Data
public class IndustryTasks extends BaseEntity implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 年份
     */
    private Integer year;

    /**
     * 部门ID
     */
    private String deptId;

    /**
     * 一级行业ID
     */
    @TableField(value = "industry_level2",typeHandler = JacksonTypeHandler.class,updateStrategy = FieldStrategy.IGNORED)
    private List<String>  industryLevel1;

    /**
     * 二级行业ID(JSON)
     */
    @TableField(value = "industry_level2",typeHandler = JacksonTypeHandler.class,updateStrategy = FieldStrategy.IGNORED)
    private List<String> industryLevel2;

    /**
     * 负责人(JSON)
     */
    @TableField(value = "industry_leaders",typeHandler = JacksonTypeHandler.class,updateStrategy = FieldStrategy.IGNORED)
    private List<String> industryLeaders;

    /**
     * 任务金额
     */
    private BigDecimal tasksMoney;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 删除标记 0-未删除 1-已删除
     */
    private Integer delFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}