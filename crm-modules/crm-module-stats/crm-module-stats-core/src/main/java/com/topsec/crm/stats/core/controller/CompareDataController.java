package com.topsec.crm.stats.core.controller;

import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.stats.core.service.CompareDataService;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/compareData")
@Tag(name = "【报表中心-同期对比】", description = "compareData")
public class CompareDataController extends BaseController {

    @Resource
    private CompareDataService compareDataService;

}
