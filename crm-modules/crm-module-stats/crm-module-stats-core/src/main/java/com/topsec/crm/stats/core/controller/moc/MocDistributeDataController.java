package com.topsec.crm.stats.core.controller.moc;

import cn.hutool.core.lang.Assert;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.starter.redis.util.DistributedNumberGenerator;
import com.topsec.crm.stats.api.entity.*;
import com.topsec.crm.stats.core.entity.moc.*;
import com.topsec.crm.stats.core.service.*;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * 分发数据预览
 */
@RestController
@RequestMapping("/moc/distribute/preview")
@Tag(name = "【分发数据预览】", description = "【分发数据预览】")
public class MocDistributeDataController extends BaseController {
    @Autowired
    private IMocAnalysisService mocAnalysisService;
    @Autowired
    private IMocSourceService mocSourceService;
    @Autowired
    private IMocDistributeService mocDistributeService;
    @Autowired
    private IMocDistributePersonService mocDistributePersonService;
    @Autowired
    private IMocDistributeConfigService mocDistributeConfigService;
    @Autowired
    private IMocDistributeConfigRuleService mocDistributeConfigRuleService;
    @Autowired
    private DistributedNumberGenerator distributedNumberGenerator;

    private static final String FFRW_PREFIX = "FFRW"+ DateTimeFormatter.ofPattern("yyyyMMdd").format(LocalDate.now());

    @PostMapping(value = "/personal")
    @Operation(summary = "数据分发数据预览")
    public JsonObject<List<Map<String,Object>>> personal(@RequestBody MocDistributeVo mocDistributeVo) {
//        MocDistributeConfigVo configVo = mocDistributeVo.getConfigVo();
//        Assert.isFalse(configVo == null || StringUtils.isNotBlank(configVo.getAnalysisId()), "请先选择分析表.");
//
//        MocAnalysis exist = mocAnalysisService.getById(configVo.getAnalysisId());
//        Assert.isFalse(exist == null || exist.getStatus() == 2, "当前分析表不存在或已失效，请重新选择。");
//
//        MocSource source = mocSourceService.getById(exist.getSourceId());
//        String getUrl = source.getGetUrl();


        String url = "http://localhost:7140/stats/customer/list";
        // 1. 创建 RestTemplate 实例
        RestTemplate restTemplate = new RestTemplate();

        // 2. 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("JWT",request.getHeader("JWT"));  // 设置 Bearer Token

        // 3. 创建包含请求头和请求体的 HttpEntity
        MocSourceVo mocSourceVo = new MocSourceVo();
        mocSourceVo.setId("32ad8f2f-b633-406a-9431-f25b108ee86f");
        HttpEntity<Object> requestEntity = new HttpEntity<>(mocSourceVo, headers);

        // 4. 发送 POST 请求
        ResponseEntity<Map> listResponseEntity = restTemplate.postForEntity(url, requestEntity, Map.class);

        // 4. 从 Map 中提取数据
        if (listResponseEntity.getStatusCode().value() == 200) {
            Map<String, Object> responseBody = listResponseEntity.getBody();
            List<Map<String,Object>> data = (List<Map<String,Object>>) responseBody.get("objEntity"); // 假设返回的 JSON 中有 "data" 字段
            // 处理数据
            for (Map<String, Object> datum : data) {
                datum.remove("name");
            }
            return new JsonObject<>(data);
        }

        return new JsonObject<>(null);
    }

}
