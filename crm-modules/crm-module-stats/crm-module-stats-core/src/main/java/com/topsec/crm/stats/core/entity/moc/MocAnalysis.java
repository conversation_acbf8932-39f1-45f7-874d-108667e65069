package com.topsec.crm.stats.core.entity.moc;

import com.baomidou.mybatisplus.annotation.TableName;
import com.topsec.crm.framework.common.web.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 数据分析基本信息对象
 * 
 * <AUTHOR>
 * @date 2023-01-09
 */
@Data
@TableName("moc_analysis")
@Schema(description = "数据分析基本信息对象")
@NoArgsConstructor
@AllArgsConstructor
public class MocAnalysis extends BaseEntity implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private String id;

    /** 报表名称 */
    private String name;

    /** 数据源ID */
    private String sourceId;

    /** 失效类型（1：手动失效，2：自动失效） */
    private Integer loseEfficacy;

    /** 失效时间 */
    private LocalDate loseDate;

    /** 模板文件ID */
    private String docId;

    /** 状态（1：正常，2：失效） */
    private Integer status;

    /** 报表说明 */
    private String remark;

    /** 创建人姓名 */
    private String createUserName;

}
