package com.topsec.crm.stats.core.controller.tasks;

import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.util.poi.ExcelUtil;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.stats.api.entity.tasks.AnnualTaskImportResultVO;
import com.topsec.crm.stats.api.entity.tasks.AnnualTasksPageQueryVO;
import com.topsec.crm.stats.api.entity.tasks.AnnualTasksVO;
import com.topsec.crm.stats.core.service.AnnualTasksService;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

@RestController
@RequestMapping("/deptAnnualTasks")
@Tag(name = "【办事处、事业部任务】", description = "deptAnnualTasks")
public class DeptAnnualTasksController extends BaseController {

    @Resource
    private AnnualTasksService annualTasksService;
    @Resource
    protected HttpServletResponse response;

    @PostMapping("/annualTasksPage")
    @Operation(summary = "办事处事业部列表")
    @PreAuthorize(inner = "true")
    public JsonObject<TableDataInfo> industryTasksPage(@RequestBody AnnualTasksPageQueryVO queryVO) {
        startPage();
        return new JsonObject<>(annualTasksService.annualTTasksPage(queryVO,1));
    }
    @PostMapping("/annualTeamTasksPage")
    @Operation(summary = "团队列表")
    @PreAuthorize(inner = "true")
    public JsonObject<TableDataInfo> annualTTeamTasksPage(@RequestBody AnnualTasksPageQueryVO queryVO) {
        return new JsonObject<>(annualTasksService.annualTTasksPage(queryVO,2));
    }

    @PostMapping("/annualSaleTasksPage")
    @Operation(summary = "销售人员列表")
    @PreAuthorize(inner = "true")
    public JsonObject<TableDataInfo> annualSaleTasksPage(@RequestBody AnnualTasksPageQueryVO queryVO) {
        return new JsonObject<>(annualTasksService.annualTTasksPage(queryVO,3));
    }

    @PostMapping("/saveOrUpAnnualTasks")
    @Operation(summary = "新增/修改办事处任务")
    @PreAuthorize(inner = "true")
    public JsonObject<Boolean> saveOrUpAnnualTasks(@RequestBody AnnualTasksVO annualTasksVO) {
        return new JsonObject<>(annualTasksService.saveOrUpAnnualTasks(annualTasksVO));
    }

    @GetMapping("/deleteAnnualTasks")
    @Operation(summary = "删除办事处任务")
    @PreAuthorize(inner = "true")
    public JsonObject<Boolean> deleteAnnualTasks(@RequestParam String industryTasksId) {
        CrmAssert.hasText(industryTasksId, "主表ID不能为空");
        return new JsonObject<>(annualTasksService.deleteAnnualTasks(industryTasksId));
    }

    @PostMapping("/exportAnnualTasks")
    @Operation(summary = "导出办事处任务")
    @PreAuthorize(inner = "true")
    public void exportIndustryTasks(@RequestBody AnnualTasksVO annualTasksVO) throws IOException {
        List<AnnualTasksVO> list = annualTasksService.exportAnnualTasks(annualTasksVO);
        ExcelUtil<AnnualTasksVO> excelUtil = new ExcelUtil<>(AnnualTasksVO.class);
        excelUtil.exportExcel(response,list,"办事处、事业部任务数据");
    }

    @GetMapping("/getAnnualTasksById")
    @Operation(summary = "查询办事处任务详情")
    @PreAuthorize(inner = "true")
    public JsonObject<AnnualTasksVO> getAnnualTasksById(@RequestParam String industryTasksId) {
        CrmAssert.hasText(industryTasksId, "主表ID不能为空");
        return new JsonObject<>(annualTasksService.getAnnualTasksById(industryTasksId));
    }
    @PostMapping("/importAnnualTask")
    @Operation(summary = "导入办事处、事业部任务")
    @PreAuthorize(inner = "true")
    public JsonObject<AnnualTaskImportResultVO> importCodeProportion(@RequestParam("file") MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return new JsonObject<>(AnnualTaskImportResultVO.fail(List.of("上传文件不能为空"), 0));
        }
        return new JsonObject<>(annualTasksService.importAnnualTasks(file));
    }

    @GetMapping("/downloadTemplate")
    @Operation(summary = "导入办事处事业部任务模板")
    @PreAuthorize(inner = "true")
    public ResponseEntity<org.springframework.core.io.Resource> downloadTemplate(){
        try {
            // 从resources目录中读取Excel文件
            org.springframework.core.io.Resource resource = new ClassPathResource("excel/DeptAnnualTasksImport.xls");

            // 设置文件名（包含中文）
            String fileName = "票据关联合同核销模板.xlsx";
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + encodedFileName + "\"");

            // 返回文件下载响应
            return ResponseEntity.ok()
                    .headers(headers)
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(resource);

        } catch (Exception e) {
            throw new CrmException("下载模板失败");
        }
    }
}
