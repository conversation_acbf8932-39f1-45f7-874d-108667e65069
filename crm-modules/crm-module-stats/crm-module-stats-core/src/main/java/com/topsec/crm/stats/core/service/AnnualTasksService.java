package com.topsec.crm.stats.core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.stats.api.entity.tasks.AnnualTaskImportResultVO;
import com.topsec.crm.stats.api.entity.tasks.AnnualTasksPageQueryVO;
import com.topsec.crm.stats.api.entity.tasks.AnnualTasksVO;
import com.topsec.crm.stats.core.entity.task.AnnualTasks;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【annual_tasks(年度任务)】的数据库操作Service
* @createDate 2025-07-18 16:13:19
*/
public interface AnnualTasksService extends IService<AnnualTasks> {


    /**
     * 列表页查询
     * @param queryVO
     * @return
     */
    TableDataInfo annualTTasksPage(AnnualTasksPageQueryVO queryVO,Integer tasksType);

    /**
     * 新增/修改任务
     * @param annualTasksVO
     * @return
     */
    Boolean saveOrUpAnnualTasks(AnnualTasksVO annualTasksVO);

    /**
     * 删除任务
     * @param annualTaskId
     * @return
     */
    Boolean deleteAnnualTasks(String annualTaskId);

    /**
     * 获取任务详情
     * @param annualTaskId
     * @return
     */
    AnnualTasksVO getAnnualTasksById(String annualTaskId);

    /**
     * 导出任务数据
     * @param annualTasksVO
     * @return
     */
    List<AnnualTasksVO> exportAnnualTasks(AnnualTasksVO annualTasksVO);

    /**
     * 导入平台任务
     * @param file
     * @return
     */
    AnnualTaskImportResultVO importPlatformTasks(MultipartFile file);

    /**
     * 导入办事处事业部任务
     * @param file
     * @return
     */
    AnnualTaskImportResultVO importAnnualTasks(MultipartFile file);

    /**
     * 查询下级部门子任务
     * @param deptId
     * @return
     */
    TableDataInfo getSubtaskByDeptId(String deptId,Integer year,Integer type);

}
