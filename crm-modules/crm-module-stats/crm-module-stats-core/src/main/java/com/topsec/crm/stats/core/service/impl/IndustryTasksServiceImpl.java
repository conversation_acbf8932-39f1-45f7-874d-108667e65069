package com.topsec.crm.stats.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.topsec.crm.framework.common.bean.FlowPerson;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.AccountAccquireUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.operation.api.RemoteIndustryService;
import com.topsec.crm.operation.api.entity.CrmIndustryVO;
import com.topsec.crm.stats.api.entity.tasks.IndustryTasksVO;
import com.topsec.crm.stats.core.entity.task.IndustryTasks;
import com.topsec.crm.stats.core.mapper.IndustryTasksMapper;
import com.topsec.crm.stats.core.service.IndustryTasksService;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【industry_tasks(行业任务)】的数据库操作Service实现
* @createDate 2025-07-16 17:42:29
*/
@Service
public class IndustryTasksServiceImpl extends ServiceImpl<IndustryTasksMapper, IndustryTasks>
    implements IndustryTasksService{

    @Resource
    private RemoteIndustryService remoteIndustryService;

    @Override
    public TableDataInfo industryTasksPage(IndustryTasksVO industryTasksVOQuery) {
        TableDataInfo tableDataInfo = new TableDataInfo();
        List<IndustryTasks> industryTasksList = baseMapper.selectList(buildQueryWrapper(industryTasksVOQuery));
        if (CollectionUtils.isEmpty(industryTasksList)) return tableDataInfo;
        List<String> industryIds = new ArrayList<>();
        industryIds.addAll(industryTasksList.stream().map(IndustryTasks::getIndustryLevel1).toList().stream().flatMap(Collection::stream).toList());
        List<String> industryLevel2Ids = industryTasksList.stream().filter(e->CollectionUtils.isNotEmpty(e.getIndustryLevel2())).map(IndustryTasks::getIndustryLevel2).toList().stream().flatMap(Collection::stream).toList();
        if (CollectionUtils.isNotEmpty(industryLevel2Ids)){
            industryIds.addAll(industryLevel2Ids);
        }
        JsonObject<List<CrmIndustryVO>> industyObject = remoteIndustryService.industyByUuids(industryIds);
        List<String> personIds = industryTasksList.stream().filter(e->CollectionUtils.isNotEmpty(e.getIndustryLeaders())).map(IndustryTasks::getIndustryLeaders).toList().stream().flatMap(Collection::stream).toList();
        List<FlowPerson> flowPersonList = AccountAccquireUtils.convertGetAccountByPersonId(personIds);
        if (industyObject.isSuccess() && CollectionUtils.isNotEmpty(industyObject.getObjEntity())){
            List<CrmIndustryVO> crmIndustryVOS = industyObject.getObjEntity();
            Map<String,String> crmIndustryMap= crmIndustryVOS.stream().collect(Collectors.toMap(CrmIndustryVO::getUuid,CrmIndustryVO::getName));
            List<IndustryTasksVO> industryTasksVOS = industryTasksList.stream().map(industryTasks -> {
                IndustryTasksVO industryTasksVO = HyperBeanUtils.copyProperties(industryTasks,IndustryTasksVO::new);
                List<String> industryLevel1= crmIndustryVOS.stream().filter(entity-> industryTasks.getIndustryLevel1().contains(entity.getUuid()))
                        .map(CrmIndustryVO::getName).toList();
                industryTasksVO.setIndustryLevel1(industryLevel1);
                List<String> industryLevel2= crmIndustryVOS.stream().filter(entity-> industryTasks.getIndustryLevel2().contains(entity.getUuid()))
                        .map(CrmIndustryVO::getName).toList();
                industryTasksVO.setIndustryLevel2(industryLevel2);
                List<String> person = flowPersonList.stream().filter(e-> industryTasks.getIndustryLeaders().contains(e.getPersonId()))
                        .map(FlowPerson::getPersonName).toList();
                industryTasksVO.setIndustryLeaders(person);
                return industryTasksVO;
            }).toList();
            tableDataInfo.setTotalCount(new PageInfo(industryTasksList).getTotal());
            tableDataInfo.setList(industryTasksVOS);
        }
        return tableDataInfo;
    }

    @Override
    @Transactional
    public Boolean saveOrUpIndustryTasks(IndustryTasksVO industryTasksVO) {
        IndustryTasks industryTasks = HyperBeanUtils.copyPropertiesByJackson(industryTasksVO,IndustryTasks.class);
        return saveOrUpdate(industryTasks);
    }

    @Override
    @Transactional
    public Boolean deleteIndustryTasks(String industryTasksId) {
        Optional<IndustryTasks> optional = Optional.ofNullable(getById(industryTasksId));
        if (optional.isPresent()){
            IndustryTasks industryTasks = optional.get();
            industryTasks.setDelFlag(1);
            return updateById(industryTasks);
        }else{
            throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
        }
    }

    @Override
    public IndustryTasksVO getIndustryTasks(String industryTasksId) {
        Optional<IndustryTasks> optional = Optional.ofNullable( getOne(new LambdaQueryWrapper<IndustryTasks>().eq(IndustryTasks::getDelFlag,0)
                .eq(IndustryTasks::getId,industryTasksId)));
        if (optional.isPresent()){
            IndustryTasks industryTasks = optional.get();
            List<String> industryIds = new ArrayList<>();
            industryIds.addAll(industryTasks.getIndustryLevel1());
            if (CollectionUtils.isNotEmpty(industryTasks.getIndustryLevel2())){
                industryIds.addAll(industryTasks.getIndustryLevel2());
            }
            JsonObject<List<CrmIndustryVO>> industyObject = remoteIndustryService.industyByUuids(industryIds);
            List<FlowPerson> flowPersonList = AccountAccquireUtils.convertGetAccountByPersonId(industryTasks.getIndustryLeaders());
            if (industyObject.isSuccess() && CollectionUtils.isNotEmpty(industyObject.getObjEntity())){
                List<CrmIndustryVO> crmIndustryVOS = industyObject.getObjEntity();
                Map<String,String> crmIndustryMap= crmIndustryVOS.stream().collect(Collectors.toMap(CrmIndustryVO::getUuid,CrmIndustryVO::getName));
                IndustryTasksVO industryTasksVO = HyperBeanUtils.copyProperties(industryTasks,IndustryTasksVO::new);
                List<String> industryLevel1= crmIndustryVOS.stream().filter(entity-> industryTasks.getIndustryLevel1().contains(entity.getUuid()))
                        .map(CrmIndustryVO::getName).toList();
                industryTasksVO.setIndustryLevel1(industryLevel1);
                List<String> industryLevel2= crmIndustryVOS.stream().filter(entity-> industryTasks.getIndustryLevel2().contains(entity.getUuid()))
                        .map(CrmIndustryVO::getName).toList();
                industryTasksVO.setIndustryLevel2(industryLevel2);
                List<String> person = flowPersonList.stream().filter(e-> industryTasks.getIndustryLeaders().contains(e.getPersonId()))
                        .map(FlowPerson::getPersonName).toList();
                industryTasksVO.setIndustryLeaders(person);
                return industryTasksVO;
            }
            return null;
        }else{
            throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
        }
    }

    @Override
    public List<IndustryTasksVO> exportIndustryTasks(IndustryTasksVO industryTasksVO) {
        List<IndustryTasks> industryTasksList = baseMapper.selectList(buildQueryWrapper(industryTasksVO));
        return HyperBeanUtils.copyListProperties(industryTasksList,IndustryTasksVO::new);
    }

    private LambdaQueryWrapper<IndustryTasks> buildQueryWrapper(IndustryTasksVO industryTasksVO){
        return new LambdaQueryWrapper<IndustryTasks>().eq(IndustryTasks::getDelFlag,0)
                .eq(!Objects.isNull(industryTasksVO.getYear()),IndustryTasks::getYear,industryTasksVO.getYear())
                .apply(CollectionUtils.isNotEmpty(industryTasksVO.getIndustryLevel1()),"JSON_CONTAINS(industry_level1, {0})",industryTasksVO.getIndustryLevel1())
                .apply(CollectionUtils.isNotEmpty(industryTasksVO.getIndustryLevel2()),"JSON_CONTAINS(industry_level2, {0})",industryTasksVO.level2ToString())
                .apply(CollectionUtils.isNotEmpty(industryTasksVO.getIndustryLeaders()),"JSON_CONTAINS(industry_leaders, {0})",industryTasksVO.industryLeadersToString());
    }

    @Override
    public List<CrmIndustryVO> getCrmIndustryVO(String type) {
        String finalType = StringUtils.isEmpty(type)? "1":type;
        JsonObject<List<CrmIndustryVO>> industyObject = remoteIndustryService.industryByLevel(finalType);
        if (industyObject.isSuccess() && CollectionUtils.isNotEmpty(industyObject.getObjEntity())){
            return industyObject.getObjEntity();
        }
        return List.of();
    }
}




