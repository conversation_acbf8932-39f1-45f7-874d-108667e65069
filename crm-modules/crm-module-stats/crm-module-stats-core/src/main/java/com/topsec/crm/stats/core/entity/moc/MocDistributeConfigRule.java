package com.topsec.crm.stats.core.entity.moc;

import com.baomidou.mybatisplus.annotation.TableName;
import com.topsec.crm.framework.common.web.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 数据分发配置-规则配置
 */
@Data
@TableName("moc_distribute_config_rule")
@Schema(description = "数据分发配置-规则配置")
@NoArgsConstructor
@AllArgsConstructor
public class MocDistributeConfigRule implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private String id;

    /** 数据分发ID */
    private String distributeId;

    /** 规则（1：列规则，2：行规则，3：分发规则） */
    private Integer type;

    /** 列属性 */
    private String col;

    /** 列属性名称 */
    private String columnName;

    /** 人员ID */
    private String personId;

    /** 是否可见（1：可见，2：不可见） */
    private Integer isShow;

    /** 匹配规则 */
    private String matchRule;

    /** 值 */
    private String value;

    /** 分发逻辑（1：直接分发，2：列字段等于人员属性，3：列字段包含人员属性） */
    private Integer shareType;

    /** 分发列字段属性 */
    private String shareColumn;

    /** 分发列字段属性名称 */
    private String shareColumnName;

    /** 人员属性（1：工号，2：所属部门，3：管辖部门，4：所属及管辖部门） */
    private Integer personnelAttributes;
}
