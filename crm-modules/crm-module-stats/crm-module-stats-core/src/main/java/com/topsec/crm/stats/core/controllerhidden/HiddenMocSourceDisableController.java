package com.topsec.crm.stats.core.controllerhidden;

import cn.hutool.core.lang.Assert;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.project.api.client.RemoteApproveConfigClient;
import com.topsec.crm.project.api.entity.CrmPersonnelGroupVO;
import com.topsec.crm.stats.api.entity.MocSourceFieldVo;
import com.topsec.crm.stats.core.entity.moc.MocSourceDisable;
import com.topsec.crm.stats.core.service.IMocSourceDisableService;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.api.client.TosDepartmentClient;
import com.topsec.tos.common.vo.TosDepartmentVO;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/hidden/moc/source/disable")
@Tag(name = "【模板管理】", description = "【模板管理】")
public class HiddenMocSourceDisableController extends BaseController {

    @Resource
    private IMocSourceDisableService mocSourceDisableService;
    @Resource
    private TosDepartmentClient tosDepartmentClient;
    @Resource
    private RemoteApproveConfigClient remoteApproveConfigClient;

    //查询当前人员对应的数据源所有属性对应的权限配置，找出需要隐藏的属性
    @GetMapping(value = "/getDisableProps")
    public JsonObject<List<String>> getDisableProps(@RequestParam String sourceId) throws IOException {
        //查询用户所在部门
        JsonObject<List<TosDepartmentVO>> byPersonId = tosDepartmentClient.findByPersonId(UserInfoHolder.getCurrentPersonId());
        Assert.isFalse(!byPersonId.isSuccess() || CollectionUtils.isEmpty(byPersonId.getObjEntity()), "未查询到当前员工所在部门，请联系管理员。");

        //查询用户所在人员组
        JsonObject<List<CrmPersonnelGroupVO>> listJsonObject = remoteApproveConfigClient.personnelGroupDetails(UserInfoHolder.getCurrentPersonId());
        Assert.isFalse(!listJsonObject.isSuccess() , "用户成员组获取失败，请联系管理员。");
        List<String> groupIds = listJsonObject.getObjEntity().stream().map(CrmPersonnelGroupVO::getId).toList();

        List<String> deptIds = byPersonId.getObjEntity().stream().map(TosDepartmentVO::getUuid).toList();
        List<MocSourceDisable> list = mocSourceDisableService.query()
                .eq("source_id", sourceId)
                .and(wrapper -> {
                    wrapper.eq("person_id", UserInfoHolder.getCurrentPersonId())
                            .or().in("dept_id", deptIds)
                            .or().in(CollectionUtils.isNotEmpty(groupIds), "group_id", groupIds);
                })
                .list();

        List<String> disableProps = new ArrayList<String>();
        for (MocSourceDisable disable : list) {
            List<MocSourceFieldVo> fields = disable.getFields();
            for (MocSourceFieldVo field : fields) {
                disableProps.add(field.getField());
            }
        }

        return new JsonObject<>(disableProps.stream().distinct().toList());
    }

    //查询当前用户是否有数据源的权限
    @GetMapping(value = "/checkHasPri")
    JsonObject<Boolean> checkHasPri(@RequestParam String sourceId){
        //查询用户所在部门
        JsonObject<List<TosDepartmentVO>> byPersonId = tosDepartmentClient.findByPersonId(UserInfoHolder.getCurrentPersonId());
        Assert.isFalse(!byPersonId.isSuccess() || CollectionUtils.isEmpty(byPersonId.getObjEntity()), "未查询到当前员工所在部门，请联系管理员。");
        List<String> deptIds = byPersonId.getObjEntity().stream().map(TosDepartmentVO::getUuid).toList();

        //查询用户所在人员组
        JsonObject<List<CrmPersonnelGroupVO>> listJsonObject = remoteApproveConfigClient.personnelGroupDetails(UserInfoHolder.getCurrentPersonId());
        Assert.isFalse(!listJsonObject.isSuccess() , "用户成员组获取失败，请联系管理员。");
        List<String> groupIds = listJsonObject.getObjEntity().stream().map(CrmPersonnelGroupVO::getId).toList();

        List<MocSourceDisable> list = mocSourceDisableService.query()
                .eq("source_id", sourceId)
                .and(wrapper -> {
                    wrapper.eq("person_id", UserInfoHolder.getCurrentPersonId())
                            .or().in("dept_id", deptIds)
                            .or().in(CollectionUtils.isNotEmpty(groupIds), "group_id", groupIds);
                })
                .list();

        return new JsonObject<>(CollectionUtils.isEmpty(list) ? false : true);
    }

}
