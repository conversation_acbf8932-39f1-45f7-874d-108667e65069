package com.topsec.crm.stats.core.entity.moc;

import com.baomidou.mybatisplus.annotation.TableName;
import com.topsec.crm.framework.common.web.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 数据分发基本信息对象
 * 
 * <AUTHOR>
 * @date 2023-01-09
 */
@Data
@TableName("moc_distribute")
@Schema(description = "数据分发基本信息对象")
@NoArgsConstructor
@AllArgsConstructor
public class MocDistribute extends BaseEntity implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private String id;

    /** 任务号 */
    private String taskNumber;

    /** 任务标题 */
    private String title;

    /** 通知方式（1：不通知，2：企业微信，3：邮箱，4：企业微信和邮箱） */
    private Integer notifyType;

    /** 通知消息 */
    private String content;

    /** 任务事项 */
    private String taskMatters;

    /** 任务状态（1：执行中，2：已完成，3:已撤销） */
    private Integer status;

    /** 创建人姓名 */
    private String createUserName;

}
