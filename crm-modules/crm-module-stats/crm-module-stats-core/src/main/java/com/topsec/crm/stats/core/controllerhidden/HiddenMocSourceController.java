package com.topsec.crm.stats.core.controllerhidden;

import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.stats.api.entity.MocSourceVo;
import com.topsec.crm.stats.core.entity.moc.MocSource;
import com.topsec.crm.stats.core.service.IMocSourceService;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;

@RestController
@RequestMapping("/hidden/moc/source")
public class HiddenMocSourceController extends BaseController {

    @Resource
    private IMocSourceService mocSourceService;

    //获取数据源信息
    @GetMapping(value = "/getSource")
    public JsonObject<MocSourceVo> getSource(@RequestParam String sourceId) throws IOException {
        MocSource exist = mocSourceService.query()
                .eq("id", sourceId)
                .one();

        return new JsonObject<>(HyperBeanUtils.copyPropertiesByJackson(exist,MocSourceVo.class));
    }

}
