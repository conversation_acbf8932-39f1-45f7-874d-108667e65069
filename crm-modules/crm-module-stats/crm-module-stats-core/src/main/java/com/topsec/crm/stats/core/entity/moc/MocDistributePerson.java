package com.topsec.crm.stats.core.entity.moc;

import com.baomidou.mybatisplus.annotation.TableName;
import com.topsec.crm.framework.common.web.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 数据分发人员对象
 */
@Data
@TableName("moc_distribute_person")
@Schema(description = "数据分发人员对象")
@NoArgsConstructor
@AllArgsConstructor
public class MocDistributePerson extends BaseEntity implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private String id;

    /** 数据分发ID */
    private String distributeId;

    /** 人员ID */
    private String personId;

    /** 导出权限（1：允许导出，2：禁止导出） */
    private Integer isReport;

}
