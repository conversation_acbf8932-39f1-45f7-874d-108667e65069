package com.topsec.crm.stats.core.entity.moc;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.topsec.crm.framework.common.web.domain.BaseEntity;
import com.topsec.crm.stats.api.entity.MocSourceFieldVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 数据分发配置
 */
@Data
@TableName(value = "moc_distribute_config", autoResultMap = true)
@Schema(description = "数据分发配置")
@NoArgsConstructor
@AllArgsConstructor
public class MocDistributeConfig extends BaseEntity implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private String id;

    /** 数据分发ID */
    private String distributeId;

    /** 数据分析表ID */
    private String analysisId;

    /** 表头行 */
    private String rowTitle;

    /** 表头行 */
    private String columnRange;

    /** 列范围对应属性 */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<MocSourceFieldVo> columns;

    /** 可编辑字段 */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<MocSourceFieldVo> editColumn;

    /** 行规则多规则匹配逻辑（1：且条件，2：或条件） */
    private Integer logicType;
}
