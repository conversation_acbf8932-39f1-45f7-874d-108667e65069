package com.topsec.crm.stats.core.controller.moc;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.topsec.crm.framework.common.name.NameUtils;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.starter.redis.util.DistributedNumberGenerator;
import com.topsec.crm.stats.api.entity.*;
import com.topsec.crm.stats.core.entity.moc.*;
import com.topsec.crm.stats.core.service.IMocDistributeConfigService;
import com.topsec.crm.stats.core.service.IMocDistributeConfigRuleService;
import com.topsec.crm.stats.core.service.IMocDistributePersonService;
import com.topsec.crm.stats.core.service.IMocDistributeService;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.tos.common.vo.EmployeeVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 数据分发基本信息管理
 */
@RestController
@RequestMapping("/moc/distribute")
@Tag(name = "【数据分发基本信息管理】", description = "【数据分发基本信息管理】")
public class MocDistributeController extends BaseController {
    @Autowired
    private IMocDistributeService mocDistributeService;
    @Autowired
    private IMocDistributePersonService mocDistributePersonService;
    @Autowired
    private IMocDistributeConfigService mocDistributeConfigService;
    @Autowired
    private IMocDistributeConfigRuleService mocDistributeConfigRuleService;
    @Autowired
    private DistributedNumberGenerator distributedNumberGenerator;

    private static final String FFRW_PREFIX = "FFRW"+ DateTimeFormatter.ofPattern("yyyyMMdd").format(LocalDate.now());

    @PostMapping(value = "/page")
    @Operation(summary = "数据分发基本信息分页")
    public JsonObject<PageUtils<MocDistributeVo>> page(@RequestBody MocDistributeVo mocDistributeVo) {
        List<MocDistribute> list = mocDistributeService.query()
                .eq(StringUtils.isNotBlank(mocDistributeVo.getTaskNumber()), "task_number", mocDistributeVo.getTaskNumber())
                .eq(StringUtils.isNotBlank(mocDistributeVo.getTitle()), "title", mocDistributeVo.getTitle())
                .eq(StringUtils.isNotBlank(mocDistributeVo.getCreateUser()), "create_user", mocDistributeVo.getCreateUser())
                .eq(StringUtils.isNotNull(mocDistributeVo.getNotifyType()), "notify_type", mocDistributeVo.getNotifyType())
                .eq(StringUtils.isNotNull(mocDistributeVo.getStatus()), "status", mocDistributeVo.getStatus())
                //TODO minggang 分发人员
                .orderByDesc("create_time")
                .list();

        List<MocDistributeVo> listVo = HyperBeanUtils.copyListPropertiesByJackson(list, MocDistributeVo.class);
        //list对象为分页后的代理对象，如果转成VO对象，代理对象会失效，即total获取不到，需要重新封装
        PageUtils dataTable = getDataTable(list,listVo);
        return new JsonObject<>(dataTable);
    }

    @PostMapping(value = "/saveBasicInfo")
    @Operation(summary = "保存 数据分发基本信息")
    public JsonObject<Boolean> saveBasicInfo(@RequestBody MocDistributeVo mocDistributeVo) throws Exception {
        //1.保存基本信息
        String analysisId = UUID.randomUUID().toString();
        MocDistribute mocDistribute = HyperBeanUtils.copyPropertiesByJackson(mocDistributeVo, MocDistribute.class);
        mocDistribute.setId(analysisId);
        mocDistribute.setTaskNumber(distributedNumberGenerator.selfIncrNumberGenerate(FFRW_PREFIX));
        mocDistribute.setStatus(1);

        //2.查询创建人姓名
        JsonObject<EmployeeVO> byId = tosEmployeeClient.findById(UserInfoHolder.getCurrentPersonId());
        if(byId.isSuccess() && byId.getObjEntity() != null){
            mocDistribute.setCreateUserName(NameUtils.getNameByEmployeeVO(byId.getObjEntity()));
        }

        return new JsonObject<>(mocDistributeService.save(mocDistribute));
    }

    @PostMapping(value = "/saveOrUpdatePersons")
    @Operation(summary = "保存/更新 数据分发人员")
    public JsonObject<Boolean> saveOrUpdatePersons(@RequestBody MocDistributeVo mocDistributeVo) throws Exception {
        //1.逻辑删除历史的权限数据
        mocDistributePersonService.update(new UpdateWrapper<MocDistributePerson>()
                .set("del_flag",1)
                .eq("distribute_id", mocDistributeVo.getId()));

        //2.保存数据分发人员
        if(CollectionUtil.isNotEmpty(mocDistributeVo.getDistributePersons())){
            List<MocDistributePersonVo> personVos = mocDistributeVo.getDistributePersons();

            for (MocDistributePersonVo personVo : personVos) {
                personVo.setId(UUID.randomUUID().toString());
                personVo.setDistributeId(mocDistributeVo.getId());
            }
            mocDistributePersonService.saveOrUpdateBatch(HyperBeanUtils.copyListPropertiesByJackson(personVos, MocDistributePerson.class));
        }
        return new JsonObject<>(true);
    }

    @PostMapping(value = "/saveConfig")
    @Operation(summary = "数据配置")
    public JsonObject<Boolean> saveConfig(@RequestBody MocDistributeVo mocDistributeVo) throws Exception {
        //1.保存分发配置
        MocDistributeConfig config = HyperBeanUtils.copyPropertiesByJackson(mocDistributeVo.getConfigVo(), MocDistributeConfig.class);
        config.setId(UUID.randomUUID().toString());
        config.setDistributeId(mocDistributeVo.getId());
        mocDistributeConfigService.save(config);

        //2.保存分发配置-规则配置
        List<MocDistributeConfigRule> inss = new ArrayList<MocDistributeConfigRule>();
        List<MocDistributeConfigRuleVo> configRules = mocDistributeVo.getConfigRules();
        for (MocDistributeConfigRuleVo configRule : configRules) {
            configRule.setId(UUID.randomUUID().toString());
            configRule.setDistributeId(mocDistributeVo.getId());
            inss.add(HyperBeanUtils.copyPropertiesByJackson(configRule, MocDistributeConfigRule.class));
        }
        mocDistributeConfigRuleService.saveBatch(inss);

        return new JsonObject<>(true);
    }


}
