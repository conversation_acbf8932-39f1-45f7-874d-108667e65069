package com.topsec.crm.stats.core.entity.moc;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.topsec.crm.framework.common.web.domain.BaseEntity;
import com.topsec.crm.stats.api.entity.MocSourceFieldVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 属性对象
 * 
 * <AUTHOR>
 * @date 2023-01-09
 */
@Data
@TableName(autoResultMap = true, value = "moc_source_disable")
@Schema(description = "属性对象")
@NoArgsConstructor
@AllArgsConstructor
public class MocSourceDisable extends BaseEntity implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private String id;

    /** 数据源ID */
    private String sourceId;

    /** 部门ID */
    private String deptId;

    /** 人员ID */
    private String personId;

    /** 人员组ID */
    private String groupId;

    /** 禁用属性列表 */
    @TableField(value = "fields", typeHandler = JacksonTypeHandler.class)
    private List<MocSourceFieldVo> fields;

}
