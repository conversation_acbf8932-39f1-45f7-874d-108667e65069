package com.topsec.crm.stats.core.controller.stats;

import com.topsec.crm.customer.api.RemoteCustomerService;
import com.topsec.crm.customer.api.entity.CrmCustomerVo;
import com.topsec.crm.framework.common.util.moc.BeanToMapUtils;
import com.topsec.crm.framework.security.annotation.MocSourceDisableAuthorize;
import com.topsec.crm.framework.common.annotation.MocSourceStructure;
import com.topsec.crm.stats.api.entity.MocSourceVo;
import com.topsec.crm.stats.core.controller.moc.AbstractMocController;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.*;

@RestController
@RequestMapping("/stats/customer")
@Tag(name = "【客户信息查询管理】", description = "【客户信息查询管理】")
public class MocCustomerInfoController extends AbstractMocController<MocSourceVo, CrmCustomerVo> {
    @Resource
    private RemoteCustomerService remoteCustomerService;

    @CrossOrigin
    @PostMapping(value = "/getDataStructure")
    @Operation(summary = "获取数据源结构")
    @MocSourceStructure
    public JsonObject<CrmCustomerVo> getDataStructure(@RequestBody MocSourceVo mocSourceVo) {
        return new JsonObject<>(new CrmCustomerVo());
    }

    @CrossOrigin
    @PostMapping(value = "/list")
    @Operation(summary = "客户信息查询")
    @MocSourceDisableAuthorize
    public JsonObject<List<Map>> list(@RequestBody MocSourceVo mocSourceVo) {
        Set<String> names = new HashSet<String>();
        names.add("深圳市腾讯计算机系统有限公司");
        names.add("伟仕佳杰（重庆）科技有限公司");
        JsonObject<List<CrmCustomerVo>> listJsonObject1 = remoteCustomerService.batchFindByName(names);
        return new JsonObject<>(BeanToMapUtils.beansToMap(listJsonObject1.getObjEntity()));
    }

    @CrossOrigin
    @PostMapping(value = "/getOne")
    @Operation(summary = "单个客户信息查询")
    @MocSourceDisableAuthorize
    public JsonObject<Map> getOne(@RequestBody MocSourceVo mocSourceVo) {
        JsonObject<CrmCustomerVo> listJsonObject1 = remoteCustomerService.getCustomerInfo("004dd1d3-b323-4037", UserInfoHolder.getCurrentPersonId(), false);
        return new JsonObject<>(BeanToMapUtils.beanToMap(listJsonObject1.getObjEntity()));
    }


}
