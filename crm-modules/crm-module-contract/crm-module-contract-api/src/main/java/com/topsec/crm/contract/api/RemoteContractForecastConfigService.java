package com.topsec.crm.contract.api;

import com.topsec.crm.framework.common.constant.ServiceNameConstants;
import com.topsec.tbscommon.JsonObject;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

/**
 * @version V1.0
 * @Description: 合同预测配置
 * @ClassName: com.topsec.crm.contract.api.RemoteContractForecastService.java
 * @Copyright 天融信 - Powered By 企业软件研发中心
 * @author: leo
 * @date: 2025-07-22 16:27
 */
@FeignClient(contextId = "RemoteContractForecastConfigService", value = ServiceNameConstants.CONTRACT_SERVICE, url = ServiceNameConstants.CONTRACT_SERVICE_URL)
public interface RemoteContractForecastConfigService {

    @GetMapping("/contractForecastFeign/createContractForecastPlan")
    JsonObject<Boolean> createContractForecastPlan();

    @GetMapping("/contractForecastFeign/showContractForecastRank")
    JsonObject<Boolean> showContractForecastRank();

    @GetMapping("/contractForecastFeign/getCurrentMonth")
    JsonObject<String> getCurrentMonth();

    @GetMapping("/contractForecastFeign/getAutoFinishMonth01")
    JsonObject<List<String>> getAutoFinishMonth01();

    @GetMapping("/contractForecastFeign/getAutoFinishMonth02")
    JsonObject<List<String>> getAutoFinishMonth02();
}
