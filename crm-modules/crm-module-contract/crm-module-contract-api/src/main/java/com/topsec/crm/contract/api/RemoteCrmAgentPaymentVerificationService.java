package com.topsec.crm.contract.api;

import com.topsec.crm.contract.api.entity.crmagentpayment.CrmAgentPaymentVerificationDTO;
import com.topsec.crm.contract.api.entity.crmagentpayment.agentpaymentverification.*;
import com.topsec.crm.flow.api.dto.performancereport.PerformanceReportPaymentInfoDTO;
import com.topsec.crm.framework.common.bean.AgentPrePaymentVerificationVO;
import com.topsec.crm.framework.common.constant.ServiceNameConstants;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


@FeignClient(contextId = "remoteCrmAgentPaymentVerificationService", value = ServiceNameConstants.CONTRACT_SERVICE,url = ServiceNameConstants.CONTRACT_SERVICE_URL)
public interface RemoteCrmAgentPaymentVerificationService {

    @Operation(summary = "获取业绩上报对应欠款金额")
    @PostMapping("/hidden/crmAgentPaymentVerification/getDebtByNumbers")
    JsonObject<Map<String, BigDecimal>> getDebtByNumbers(@RequestBody(required = false) List<String> numbers);

    @Operation(summary = "付款申请办结-回款核销-保存")
    @PostMapping("/hidden/crmAgentPaymentVerification/savePaymentVerificationByPayment")
    JsonObject<Boolean> savePaymentVerificationByPayment(@RequestBody CrmAgentPaymentVerificationDTO verificationDTO);

    @Operation(summary = "生效业绩上报（写入最终表后调用）-付款信息-回款核销-保存")
    @PostMapping("/hidden/crmAgentPaymentVerification/savePaymentVerificationByPerformance")
    JsonObject<Boolean> savePaymentVerificationByPerformance(@RequestBody List<PerformanceReportPaymentInfoDTO> infoDTOList);

    @Operation(summary = "生效业绩上报（写入最终表后调用）-预付款抵货款-回款核销-保存")
    @PostMapping("/hidden/crmAgentPaymentVerification/savePaymentVerificationByAdvanceUsed")
    JsonObject<Boolean> savePaymentVerificationByAdvanceUsed(@RequestBody List<PerformancePreInfoVO> infoDTOList);

    @GetMapping("/hidden/crmAgentPaymentVerification/verificationCount")
    @Operation(summary = "返回统计的冻结预付款总数， 预付款总数")
    JsonObject<PrepaymentSummaryVO> verificationCount(@RequestParam String payerAgentName, @RequestParam String receivedAgentName);

    @PostMapping("/hidden/crmAgentPaymentVerification/verificationPage")
    @Operation(summary = "回款核销分页列表")
    JsonObject<PageUtils<AgentPaymentVerificationVO>> verificationHiddenPage(@RequestBody AgentPaymentHiddenPageQuery query);

    @PostMapping("/hidden/crmAgentPaymentVerification/verificationPrePage")
    @Operation(summary = "回款核销预付款分页列表")
    JsonObject<PageUtils<AgentPrePaymentVerificationVO>> verificationPrePage(@RequestBody AgentPaymentHiddenPageQuery query);

    @GetMapping("/hidden/crmAgentPaymentVerification/queryPreByPayerAgent")
    @Operation(summary = "生效业绩上报-使用预付款-根据渠道商查询")
    JsonObject<List<PerformancePreInfoVO>> queryPreByPayerAgent(@RequestParam String payerAgentName,@RequestParam String receivedAgentName);

    @PostMapping("/hidden/crmAgentPaymentVerification/batchVerificationCount")
    @Operation(summary = "回款核销分页列表(查询自定渠道商的带有统计的接口)批量")
    JsonObject<List<PrepaymentSummaryVO>> batchVerificationCount(@RequestBody List<Map<String,String>> params);

    @PostMapping("/hidden/crmAgentPaymentVerification/savePreDetailInfo")
    @Operation(summary = "预付款流程办结完——回写核销数据")
    JsonObject<Boolean> savePreDetailInfo(@RequestBody SubmitPreDetailDTO submitDetailDTO);

    @PostMapping("/hidden/crmAgentPaymentVerification/addVerification")
    @Operation(summary = "渠道付款流程办结完——回写核销数据")
    JsonObject<String> addVerification(@RequestBody AgentPaymentProcessSubmitDTO submitDetailDTO);

    @GetMapping("/hidden/crmAgentPaymentVerification/queryInfoByProcessInstanceId")
    @Operation(summary = "生效业绩上报-根据业绩上报流程id查询明细")
    JsonObject<List<PerformanceInfoVO>> queryInfoByProcessInstanceId(@RequestParam String performanceReportNumber);

    @PostMapping("/hidden/crmAgentPaymentVerification/executFreeze")
    @Operation(summary = "每天执行清洗冻结定时器")
    JsonObject<Void> executFreeze(@RequestBody Map<String,Object> param);

    @GetMapping("/hidden/crmAgentPaymentVerification/queryPrePaymentVerificationList")
    @Operation(summary = "查询回款核销预付款信息")
    JsonObject<List<AgentPrePaymentVerificationVO>> queryPrePaymentVerificationList(@RequestParam String payerAgentId, @RequestParam String receivedAgentId);
}
