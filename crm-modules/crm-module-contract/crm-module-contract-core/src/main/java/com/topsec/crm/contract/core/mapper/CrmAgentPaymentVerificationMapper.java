package com.topsec.crm.contract.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.topsec.crm.contract.api.entity.crmagentpayment.CrmAgentAdvancePaymentQuery;
import com.topsec.crm.contract.api.entity.crmagentpayment.CrmAgentAdvancePaymentVO;
import com.topsec.crm.contract.api.entity.crmagentpayment.agentpaymentverification.AgentPaymentVerificationQuery;
import com.topsec.crm.framework.common.bean.AgentPrePaymentVerificationVO;
import com.topsec.crm.contract.api.entity.crmagentpayment.agentpaymentverification.AgentReceivedlVO;
import com.topsec.crm.contract.core.entity.CrmAgentPaymentVerification;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 渠道-回款核销 Mapper 接口
 */
public interface CrmAgentPaymentVerificationMapper extends BaseMapper<CrmAgentPaymentVerification> {

    List<CrmAgentAdvancePaymentVO> selectAdvancePaymentList(CrmAgentAdvancePaymentQuery query);


    IPage<CrmAgentPaymentVerification> selectPages(IPage<CrmAgentPaymentVerification> page, @Param("query") AgentPaymentVerificationQuery query, @Param("depts") Set<String> depts, @Param("allagents") List<String> allagents, @Param("twoagents") List<String> twoagents);

    IPage<CrmAgentPaymentVerification> verificationHiddenPage(IPage<CrmAgentPaymentVerification> page, @Param("query") AgentPaymentVerificationQuery query);


    IPage<AgentPrePaymentVerificationVO> selectPrePages(IPage<AgentPrePaymentVerificationVO> page, @Param("query") AgentPaymentVerificationQuery query, @Param("depts") Set<String> depts, @Param("allagents") List<String> allagents, @Param("twoagents") List<String> twoagents, @Param("verificationType")Integer verificationType);

    IPage<AgentReceivedlVO> selectAdvancesReceivedPages(IPage<AgentReceivedlVO> page, @Param("query") AgentPaymentVerificationQuery query, @Param("depts") Set<String> depts, @Param("allagents") List<String> allagents, @Param("twoagents") List<String> twoagents);

    List<String> selectAccountNumbers();

    List<AgentPrePaymentVerificationVO> selectPrePaymentVerificationList(String payerAgentId, String receivedAgentId);
}
