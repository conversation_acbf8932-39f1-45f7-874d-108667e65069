package com.topsec.crm.contract.core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.topsec.crm.contract.core.entity.CrmContractForecast;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.page.CrmPageQuery;

import java.util.List;

/**
 * <p>
 * 月新增合同预测设置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
public interface ICrmContractForecastService extends IService<CrmContractForecast> {

    /**
     * 分页查询月新增合同预测设置
     * @param crmPageQuery
     * @return
     */
    PageUtils<CrmContractForecast> selectCrmContractForecastPageByParams(CrmPageQuery crmPageQuery);

    /**
     * 修改月新增合同预测设置
     * @param crmContractForecast
     * @return
     */
    Boolean updateCrmContractForecast(CrmContractForecast crmContractForecast);

    /**
     * 创建合同预测计划
     * @return
     */
    Boolean createContractForecastPlan();

    /**
     * 是否显示合同预测未办结排名
     * @return
     */
    Boolean showContractForecastRank();

    /**
     * 获取当前期数
     * @return
     */
    String getCurrentMonth();

    /**
     * 获取01步自动结束的期数
     *
     */
    List<String> getAutoFinishMonth01();

    /**
     * 获取02步自动结束的期数
     *
     */
    List<String> getAutoFinishMonth02();
}
