package com.topsec.crm.contract.core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.topsec.crm.contract.api.entity.crmagentpayment.CrmAgentAdvancePaymentQuery;
import com.topsec.crm.contract.api.entity.crmagentpayment.CrmAgentAdvancePaymentVO;
import com.topsec.crm.contract.api.entity.crmagentpayment.CrmAgentPaymentVerificationDTO;
import com.topsec.crm.contract.api.entity.crmagentpayment.agentpaymentverification.*;
import com.topsec.crm.contract.core.entity.CrmAgentPaymentVerification;
import com.topsec.crm.flow.api.dto.performancereport.PerformanceExecuteQuery;
import com.topsec.crm.flow.api.dto.performancereport.PerformanceExecuteVO;
import com.topsec.crm.flow.api.dto.performancereport.PerformanceReportPaymentInfoDTO;
import com.topsec.crm.framework.common.bean.AgentPrePaymentVerificationVO;
import com.topsec.crm.framework.common.util.PageUtils;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * 渠道-回款核销 服务类
 */
public interface CrmAgentPaymentVerificationService extends IService<CrmAgentPaymentVerification> {

    /**
     * 渠道-预付款汇总列表
     * @param query
     * @return
     */
    PageUtils<CrmAgentAdvancePaymentVO> advancePaymentPage(CrmAgentAdvancePaymentQuery query);

    /**
     * 付款申请办结-回款核销-保存
     * @param verificationDTO
     * @return
     */
    Boolean savePaymentVerificationByPayment(CrmAgentPaymentVerificationDTO verificationDTO);

    /**
     * 付款申请-入参-基础数据验证
     * @param verificationDTO
     * @return
     */
    Boolean isCanSaveByPayment(CrmAgentPaymentVerificationDTO verificationDTO);

    /**
     * 业绩上报生效-付款信息-回款核销-保存
     * @param infoDTOList
     * @return
     */
    Boolean savePaymentVerificationByPerformance(List<PerformanceReportPaymentInfoDTO> infoDTOList);

    /**
     * 业绩上报生效-预付款抵货款-回款核销-保存
     * @param
     * @return
     */
    Boolean savePaymentVerificationByAdvanceUsed(List<PerformancePreInfoVO> performancePreInfoVOS);

    /**
     * <AUTHOR>
     * @description 列表查询
     * @date 2025/7/21 10:22
     * @param query
     * @param deptId
     * @return com.topsec.crm.framework.common.util.PageUtils<com.topsec.crm.agent.api.entity.agentpaymentverification.AgentPaymentVerificationVO>
     **/
    PageUtils<AgentPaymentVerificationVO> verificationPage(AgentPaymentVerificationQuery query);

    PageUtils<AgentPaymentVerificationVO> verificationHiddenPage(AgentPaymentVerificationQuery query);
    /**
     * <AUTHOR>
     * @description
     * @date 2025/7/21 15:14
     * @param query
     * @return com.topsec.crm.framework.common.util.PageUtils<com.topsec.crm.agent.api.entity.agentpaymentverification.AgentPrePaymentVerificationVO>
     **/
    PageUtils<AgentPrePaymentVerificationVO> verificationPrePage(AgentPaymentHiddenPageQuery query);

    PageUtils<AgentReceivedlVO> advancesReceivedPage(AgentPaymentVerificationQuery query);
    /**
     * <AUTHOR>
     * @description 查询回款核销记录
     * @date 2025/7/21 10:22
     * @param id
     * @return com.topsec.crm.agent.api.entity.agentpaymentverification.AgentPaymentVerificationVO
     **/
    AgentPaymentVerificationVO queryInfoById(String id);
    /**
     * <AUTHOR>
     * @description 查询核销明细
     * @date 2025/7/21 10:23
     * @param id
     * @return com.topsec.crm.agent.api.entity.agentpaymentverification.PaymentVerificationDetailVO
     **/
    PaymentVerificationDetailVO queryVetrificationDetailsById(String id,String type);
    /**
     * <AUTHOR>
     * @description 回冲
     * @date 2025/7/21 10:23
     * @param reversalDetailVO
     * @return void
     **/
    void reversal(ReversalDetailVO reversalDetailVO);
    /**
     * <AUTHOR>
     * @description 修改跳转查询
     * @date 2025/7/21 11:57
     * @param id
     * @return com.topsec.crm.agent.api.entity.agentpaymentverification.DetailInfoVO
     **/
    DetailInfoVO queryDetailInfo(String id);
    /**
     * <AUTHOR>
     * @description 修改货款之后的保存
     * @date 2025/7/21 11:57
     * @param submitDetailDTO
     * @return void
     **/
    void updateDetailInfo(SubmitDetailDTO submitDetailDTO);
    /**
     * <AUTHOR>
     * @description  核销预付款——确认提交
     * @date 2025/7/22 9:58
     * @param submitDetailDTO
     * @return void
     **/
    void applyAdvance(SubmitDetailDTO submitDetailDTO);
    /**
     * <AUTHOR>
     * @description 预付款流程完成，将核销数据传回
     * @date 2025/7/21 16:40
     * @param submitDetailDTO
     * @return void
     **/
    void savePreDetailInfo(SubmitPreDetailDTO submitDetailDTO);
    /**
     * <AUTHOR>
     * @description 渠道付款，如果是预付款，则首先冻结
     * @date 2025/7/22 20:32
     * @param submitDetailDTO
     * @return java.lang.Boolean
     **/
    String addVerification(@RequestBody AgentPaymentProcessSubmitDTO submitDetailDTO);
    /**
     * <AUTHOR>
     * @description 删除
     * @date 2025/7/21 12:32
     * @param id
     * @return void
     **/
    void deleteDetailInfo(String id);
    /**
     * <AUTHOR>
     * @description 产看流程
     * @date 2025/7/21 12:32
     * @param verificationId
     * @return java.util.List<com.topsec.crm.contract.api.entity.paymentcollection.PaymentVerificationProcessVO>
     **/
    List<AgentProcessVO> queryVerificationProcessList(String verificationId);
    /**
     * <AUTHOR>
     * @description 修改进账单号
     * @date 2025/7/21 14:22
     * @param modifyCarNumberDTO
     * @return java.lang.Boolean
     **/
    void modifyCarNumber(ModifyCarNumberDTO modifyCarNumberDTO);

    PrepaymentSummaryVO verificationCount(String payerAgentName,String receivedAgentName);

    List<PrepaymentSummaryVO> batchVerificationCount(List<Map<String,String>> params);

    PageUtils<PerformanceExecuteVO> pagePerformanceReport(PerformanceExecuteQuery query);

    List<AgentPrePaymentVerificationVO> queryPrePaymentVerificationList(String payerAgentId, String receivedAgentId);
}
