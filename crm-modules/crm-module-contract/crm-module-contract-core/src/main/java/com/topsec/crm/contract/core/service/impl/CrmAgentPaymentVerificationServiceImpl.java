package com.topsec.crm.contract.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.topsec.crm.agent.api.RemoteAgentService;
import com.topsec.crm.agent.api.entity.CrmAgentVo;
import com.topsec.crm.contract.api.RemoteCrmAgentPaymentVerificationService;
import com.topsec.crm.contract.api.entity.crmagentpayment.CrmAgentAdvancePaymentQuery;
import com.topsec.crm.contract.api.entity.crmagentpayment.CrmAgentAdvancePaymentVO;
import com.topsec.crm.contract.api.entity.crmagentpayment.CrmAgentPaymentVerificationDTO;
import com.topsec.crm.contract.api.entity.crmagentpayment.CrmAgentPaymentVerificationDetailVO;
import com.topsec.crm.contract.api.entity.crmagentpayment.agentpaymentverification.*;
import com.topsec.crm.contract.api.entity.paymentcollection.PaymentVerificationVO;
import com.topsec.crm.contract.api.entity.request.CrmContractAfterQuery;
import com.topsec.crm.contract.core.entity.CrmAgentPaymentVerification;
import com.topsec.crm.contract.core.entity.CrmAgentPaymentVerificationDetail;
import com.topsec.crm.contract.core.entity.CrmPerformanceReport;
import com.topsec.crm.contract.core.mapper.CrmAgentPaymentVerificationDetailMapper;
import com.topsec.crm.contract.core.mapper.CrmAgentPaymentVerificationMapper;
import com.topsec.crm.contract.core.service.CrmAgentPaymentVerificationDetailService;
import com.topsec.crm.contract.core.service.CrmAgentPaymentVerificationService;
import com.topsec.crm.contract.core.service.CrmPerformanceReportService;
import com.topsec.crm.flow.api.RemoteAgentPrepaymentUsageService;
import com.topsec.crm.flow.api.RemotePerformanceExecuteService;
import com.topsec.crm.flow.api.dto.agentPrepaymentUsage.AgentPrepaymentUsageDTO;
import com.topsec.crm.flow.api.dto.borrowforprobation.BorrowFlowQuery;
import com.topsec.crm.flow.api.dto.performancereport.PerformanceExecuteQuery;
import com.topsec.crm.flow.api.dto.performancereport.PerformanceExecuteVO;
import com.topsec.crm.flow.api.dto.performancereport.PerformanceReportPaymentInfoDTO;
import com.topsec.crm.framework.common.bean.AgentPrePaymentVerificationVO;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.enums.AgentEnum;
import com.topsec.crm.framework.common.enums.AgentVerificationTypeEnum;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.name.NameUtils;
import com.topsec.crm.framework.common.util.CommonUtils;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tbscommon.constant.TbsConstants;
import com.topsec.tbscommon.utils.UUIDUtils;
import com.topsec.tfs.api.client.TfsFormContentClient;
import com.topsec.tfs.api.client.TfsNodeClient;
import com.topsec.tos.api.client.TosEmployeeClient;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.tos.common.vo.EmployeeVO;
import com.topsec.vo.TfsFormContentVo;
import com.topsec.vo.node.ApproveNode;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 渠道-回款核销 服务实现类
 */
@Slf4j
@Service
public class CrmAgentPaymentVerificationServiceImpl extends ServiceImpl<CrmAgentPaymentVerificationMapper, CrmAgentPaymentVerification> implements CrmAgentPaymentVerificationService {

    @Resource
    private CrmAgentPaymentVerificationMapper crmAgentPaymentVerificationMapper;

    @Resource
    private CrmAgentPaymentVerificationDetailMapper agentVerificationDetailMapper;

    @Resource
    private CrmAgentPaymentVerificationDetailService crmAgentPaymentVerificationDetailService;

    @Resource
    private RemotePerformanceExecuteService remotePerformanceExecuteService;

    @Resource
    private CrmPerformanceReportService crmPerformanceReportService;

    @Resource
    private TosEmployeeClient tosEmployeeClient;
    @Resource
    private TfsNodeClient tfsNodeClient;

    @Resource
    private RemoteAgentService remoteAgentService;

    @Resource
    private TfsFormContentClient tfsFormContentClient;

    @Resource
    private RemoteCrmAgentPaymentVerificationService remoteCrmAgentPaymentVerificationService;

    @Resource
    private RemoteAgentPrepaymentUsageService remoteAgentPrepaymentUsageService;

    /**
     * 渠道-预付款汇总列表
     *
     * @param query
     * @return
     */
    @Override
    public PageUtils<CrmAgentAdvancePaymentVO> advancePaymentPage(CrmAgentAdvancePaymentQuery query) {
        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();
        Set<String> personIdsOfPermission = dataScopeParam != null ? dataScopeParam.getPersonIdList() : Collections.EMPTY_SET;
        String currentAgentId = dataScopeParam != null ? dataScopeParam.getAgentId() : null;
        if (StringUtils.isNotEmpty(currentAgentId)) {

        } else if (CollectionUtils.isNotEmpty(personIdsOfPermission)) {
            List<String> personIds = new ArrayList<>(personIdsOfPermission);
            JsonObject<List<EmployeeVO>> byIds = tosEmployeeClient.findByIds(personIds);
            if (byIds.isSuccess() && Objects.nonNull(byIds.getObjEntity())) {
                for (EmployeeVO employeeVO : byIds.getObjEntity()) {
                    personIds.addAll(ListUtils.emptyIfNull(employeeVO.getSubordinates()).stream().map(EmployeeVO::getUuid).toList());
                }
            }
        }
        List<CrmAgentAdvancePaymentVO> apVOList = crmAgentPaymentVerificationMapper.selectAdvancePaymentList(query);
        List<CrmAgentAdvancePaymentVO> apVOPagingList = CommonUtils.subListPage(apVOList, query.getPageSize(), query.getPageNum());
        PageUtils<CrmAgentAdvancePaymentVO> pageUtils = new PageUtils<>(apVOPagingList, apVOList.size(), query.getPageSize(), query.getPageNum());
        return pageUtils;
    }

    /**
     * 付款申请办结-回款核销-保存
     *
     * @param verificationDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean savePaymentVerificationByPayment(CrmAgentPaymentVerificationDTO verificationDTO) {
        CrmAgentPaymentVerification verification = HyperBeanUtils.copyPropertiesByJackson(verificationDTO, CrmAgentPaymentVerification.class);
        verification.setSourceRemark(3);
        verification.setDelFlag(false);
        // 更新回款核销主表
        if (save(verification)) {
            String id = verification.getId();
            if (CollectionUtils.isNotEmpty(verificationDTO.getDetailVOList())) {
                List<CrmAgentPaymentVerificationDetail> detailList = HyperBeanUtils.copyListPropertiesByJackson(verificationDTO.getDetailVOList(), CrmAgentPaymentVerificationDetail.class);
                for (CrmAgentPaymentVerificationDetail detail : detailList) {
                    detail.setVerificationId(id);
                    detail.setPayerAgentId(verification.getPayerAgentId());
                    detail.setPayerAgentName(verification.getPayerAgentName());
                }
                // 更新回款核销从表
                if (crmAgentPaymentVerificationDetailService.saveBatch(detailList)) {
                    List<String> numbers = detailList.stream().map(detail -> detail.getPerformanceReportNumber()).collect(Collectors.toList());
                    Map<String, BigDecimal> writeOffMap = crmAgentPaymentVerificationDetailService.getWriteOffByNumbers(numbers);
                    List<PerformanceExecuteVO> voList = new ArrayList<>();
                    for (CrmAgentPaymentVerificationDetail detail : detailList) {
                        if (detail.getVerificationType() == AgentVerificationTypeEnum.PAYMENT.getCode()) {
                            PerformanceExecuteVO vo = new PerformanceExecuteVO();
                            vo.setPerformanceProcessInstanceId(detail.getPerformanceProcessInstanceId());
                            vo.setReturnedAmount(writeOffMap.get(detail.getPerformanceReportNumber()));
                            voList.add(vo);
                        }
                    }
                    // 更新业绩执行回款信息
                    if (voList.size() > 0) {
                        remotePerformanceExecuteService.updateWriteOff(voList);
                    }
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 付款申请-入参-基础数据验证
     *
     * @param verificationDTO
     * @return
     */
    @Override
    public Boolean isCanSaveByPayment(CrmAgentPaymentVerificationDTO verificationDTO) {
        if (verificationDTO == null) return false;
        if (verificationDTO.getDetailVOList() == null || verificationDTO.getDetailVOList().size() == 0
                || ifNullToZero(verificationDTO.getCollectionAmount()).compareTo(BigDecimal.ZERO) == 0) return false;
        // 1-预付款 回款详情只能1条
        if (verificationDTO.getPaymentNature() == 1 && verificationDTO.getDetailVOList().size() != 1) return false;
        // 1-预付款 核销金额必须等于核销详情金额
        if (verificationDTO.getPaymentNature() == 1 && ifNullToZero(verificationDTO.getDetailVOList().get(0).getWriteOffPrice()).compareTo(verificationDTO.getCollectionAmount()) != 0)
            return false;
        // 2-货款
        if (verificationDTO.getPaymentNature() == 2) {
            // 业绩上报回款要小于等于其欠款金额
            List<String> numbers = verificationDTO.getDetailVOList().stream().map(CrmAgentPaymentVerificationDetailVO::getPerformanceReportNumber).collect(Collectors.toList());
            Map<String, BigDecimal> debts = crmAgentPaymentVerificationDetailService.getDebtByNumbers(numbers);
            if (debts.size() != numbers.size()) return false;
            verificationDTO.getDetailVOList().forEach(detailVO -> {
                if (ifNullToZero(detailVO.getWriteOffPrice()).compareTo(BigDecimal.ZERO) == 0)
                    Assert.isFalse(true, "核销金额不能为0或空");
                if (detailVO.getWriteOffPrice().compareTo(ifNullToZero(debts.get(detailVO.getPerformanceReportNumber()))) > 0)
                    Assert.isFalse(true, "业绩上报要生效且核销金额要小于等于其欠款金额");
            });
            // 回款金额之和必须小于等于收款金额
            BigDecimal sum = verificationDTO.getDetailVOList().stream().map(CrmAgentPaymentVerificationDetailVO::getWriteOffPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (sum.compareTo(verificationDTO.getCollectionAmount()) > 0) return false;
        }
        return true;
    }

    /**
     * 业绩上报生效-付款信息-回款核销-保存
     *
     * @param infoDTOList
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean savePaymentVerificationByPerformance(List<PerformanceReportPaymentInfoDTO> infoDTOList) {
        List<PerformanceReportPaymentInfoDTO> collect = infoDTOList.stream().filter(infoDTO -> infoDTO.getProcessInstanceId() != null
                && ifNullToZero(infoDTO.getPaymentPrice()).compareTo(BigDecimal.ZERO) != 0
                && infoDTO.getPaymentDate() != null
                && ifNullToZero(infoDTO.getWriteOffPrice()).compareTo(BigDecimal.ZERO) != 0
                && infoDTO.getAccountDate() != null).collect(Collectors.toList());
        if (collect == null || collect.size() == 0) return false;
        List<String> reportIds = collect.stream().map(PerformanceReportPaymentInfoDTO::getProcessInstanceId).collect(Collectors.toList());
        Map<String, CrmPerformanceReport> reportMap = crmPerformanceReportService.list(new LambdaQueryWrapper<CrmPerformanceReport>()
                        .in(CrmPerformanceReport::getProcessInstanceId, reportIds)
                        .eq(CrmPerformanceReport::getDelFlag, false)).stream()
                .collect(Collectors.toMap(CrmPerformanceReport::getProcessInstanceId, item -> item));
        collect.forEach(infoDTO -> {
            CrmPerformanceReport report = reportMap.get(infoDTO.getProcessInstanceId());
            if (report == null) throw new RuntimeException("业绩上报不存在");
            CrmAgentPaymentVerification verification = new CrmAgentPaymentVerification();
            verification.setReceivedDate(infoDTO.getAccountDate());
            verification.setCollectionAmount(infoDTO.getPaymentPrice());
            verification.setAccountNumber(infoDTO.getAccountNumber());
            verification.setReceivedAgentId(report.getSupplierId());
            verification.setReceivedAgentName(report.getSupplierName());
            verification.setReceivedLevel(report.getSupplierType());
            verification.setPayerAgentId(report.getChannelCompanyId());
            verification.setPayerAgentName(report.getChannelCompanyName());
            verification.setSourceRemark(1);
            verification.setPerformanceReportPaymentInfoId(infoDTO.getId());
            verification.setDelFlag(false);
            if (save(verification)) {
                CrmAgentPaymentVerificationDetail detail = new CrmAgentPaymentVerificationDetail();
                detail.setVerificationId(verification.getId());
                detail.setVerificationType(AgentVerificationTypeEnum.PAYMENT.getCode());
                detail.setPerformanceProcessInstanceId(infoDTO.getProcessInstanceId());
                detail.setPerformanceReportNumber(report.getProcessNumber());
                detail.setWriteOffPrice(infoDTO.getWriteOffPrice());
                detail.setWriteOffDate(report.getEffectiveTime());
                detail.setPayerAgentId(report.getChannelCompanyId());
                detail.setPayerAgentName(report.getChannelCompanyName());
                detail.setDelFlag(false);
                crmAgentPaymentVerificationDetailService.save(detail);
            }
        });
        return true;
    }

    /**
     * 业绩上报生效-预付款抵货款-回款核销-保存
     *
     * @param
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean savePaymentVerificationByAdvanceUsed(List<PerformancePreInfoVO> performancePreInfoVOS) {
        Map<String, CrmAgentPaymentVerificationDetail> map = agentVerificationDetailMapper.selectByIds(performancePreInfoVOS
                        .stream().map(PerformancePreInfoVO::getId).toList())
                .stream().collect(Collectors.toMap(CrmAgentPaymentVerificationDetail::getId, item -> item, (v1, v2) -> v1));
        Iterator<Map.Entry<String, CrmAgentPaymentVerificationDetail>> iterator = map.entrySet().iterator();

        if (CollectionUtils.isEmpty(performancePreInfoVOS)){
            log.error("业绩上报生效-预付款抵货款-回款核销：无回款明细");
            return false;
        }else {
            performancePreInfoVOS.forEach(preInfoVO -> {
                CrmAgentPaymentVerificationDetail detail = map.get(preInfoVO.getId());
                //预付款顶货款，则直接减仓预付款的
                detail.setWriteOffPrice(detail.getWriteOffPrice().subtract(preInfoVO.getWriteOffPrice()));
                agentVerificationDetailMapper.updateById(detail);
                CrmAgentPaymentVerificationDetail huoDetail = HyperBeanUtils.copyPropertiesByJackson(preInfoVO, CrmAgentPaymentVerificationDetail.class);
                huoDetail.setId(UUIDUtils.generateUUID()).setVerificationType(AgentVerificationTypeEnum.PAYMENT.getCode()).setStatus(1)
                        .setWriteOffPrice(preInfoVO.getUsedAmount())
                        .setWriteOffDate(LocalDateTime.now()).setPerformanceProcessInstanceId(preInfoVO.getProcessInstanceId()).setPerformanceReportNumber(preInfoVO.getProcessNumber());
                agentVerificationDetailMapper.insert(huoDetail);
                recalculate(detail.getVerificationId());
            });
        }
        return true;
    }

    @Override
    public PageUtils<AgentPaymentVerificationVO> verificationPage(AgentPaymentVerificationQuery query) {
        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();
        List<String> roles = UserInfoHolder.getLoginInfo().getRoles();
        String userAgentId = dataScopeParam.getAgentId();
        Set<String> personList = dataScopeParam != null && com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(dataScopeParam.getPersonIdList()) ? dataScopeParam.getPersonIdList() : Collections.EMPTY_SET;
        //查询所有用户的部门
        Set<String> depts = dataScopeParam != null ? Optional.ofNullable(dataScopeParam.getDeptIdList()).orElse(new HashSet<>()).stream().filter(s -> {
            return !"-1".equals(s);
        }).collect(Collectors.toSet()) : Collections.EMPTY_SET;

        //总代数据
        List<String> allagents = new ArrayList<>();
        if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(personList)) {
            allagents.addAll(queryAgentBypersonId(personList));
        }
        //二代数据
        List<String> twoagents = new ArrayList<>();
        if (twoAgentCheck(roles)) {
            twoagents.add(userAgentId);
        }
        IPage<CrmAgentPaymentVerification> page = new Page<>();
        page.setSize(query.getPageSize());
        page.setCurrent(query.getPageNum());
        Map<String, List<CrmAgentPaymentVerificationDetail>> stringListMap = new HashMap<>();
        IPage<CrmAgentPaymentVerification> agentPaymentVerificationIPage = crmAgentPaymentVerificationMapper.selectPages(page, query, depts, allagents, twoagents);
        if (CollectionUtils.isNotEmpty(agentPaymentVerificationIPage.getRecords())) {
            stringListMap.putAll(Optional.ofNullable(agentVerificationDetailMapper.listByIds(agentPaymentVerificationIPage.getRecords().stream().map(CrmAgentPaymentVerification::getId).collect(Collectors.toUnmodifiableList())))
                    .orElse(new ArrayList<>()).stream().collect(Collectors.groupingBy(CrmAgentPaymentVerificationDetail::getVerificationId)));
        }
        return new PageUtils<>(agentPaymentVerificationIPage.getRecords().stream().map(agentPaymentVerification -> {
            AgentPaymentVerificationVO agentPaymentVerificationVO = HyperBeanUtils.copyPropertiesByJackson(agentPaymentVerification, AgentPaymentVerificationVO.class);
            if (stringListMap.containsKey(agentPaymentVerificationVO.getId())) {
                agentPaymentVerificationVO.setDetails(HyperBeanUtils.copyListPropertiesByJackson(stringListMap.get(agentPaymentVerificationVO.getId()), AgentVerificationDetailVO.class));
            }
            return agentPaymentVerificationVO;
        }).collect(Collectors.toList()), agentPaymentVerificationIPage.getTotal(), query.getPageNum(), query.getPageSize());
    }

    @Override
    public PageUtils<AgentPaymentVerificationVO> verificationHiddenPage(AgentPaymentVerificationQuery query) {

        IPage<CrmAgentPaymentVerification> page = new Page<>();
        page.setSize(query.getPageSize());
        page.setCurrent(query.getPageNum());
        Map<String, List<CrmAgentPaymentVerificationDetail>> stringListMap = new HashMap<>();
        IPage<CrmAgentPaymentVerification> agentPaymentVerificationIPage = crmAgentPaymentVerificationMapper.verificationHiddenPage(page, query);
        if (CollectionUtils.isNotEmpty(agentPaymentVerificationIPage.getRecords())) {
            stringListMap.putAll(Optional.ofNullable(agentVerificationDetailMapper.listByIds(agentPaymentVerificationIPage.getRecords().stream().map(CrmAgentPaymentVerification::getId).collect(Collectors.toUnmodifiableList())))
                    .orElse(new ArrayList<>()).stream().collect(Collectors.groupingBy(CrmAgentPaymentVerificationDetail::getVerificationId)));
        }
        return new PageUtils<>(agentPaymentVerificationIPage.getRecords().stream().map(agentPaymentVerification -> {
            AgentPaymentVerificationVO agentPaymentVerificationVO = HyperBeanUtils.copyPropertiesByJackson(agentPaymentVerification, AgentPaymentVerificationVO.class);
            if (stringListMap.containsKey(agentPaymentVerificationVO.getId())) {
                agentPaymentVerificationVO.setDetails(HyperBeanUtils.copyListPropertiesByJackson(stringListMap.get(agentPaymentVerificationVO.getId()), AgentVerificationDetailVO.class));
            }
            return agentPaymentVerificationVO;
        }).collect(Collectors.toList()), agentPaymentVerificationIPage.getTotal(), query.getPageNum(), query.getPageSize());
    }

    @Override
    public PageUtils<AgentPrePaymentVerificationVO> verificationPrePage(AgentPaymentHiddenPageQuery query) {
        DataScopeParam dataScopeParam = query.getDataScopeParam();
        List<String> roles = query.getRoles();
        Set<String> personList = dataScopeParam != null && com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(dataScopeParam.getPersonIdList()) ? dataScopeParam.getPersonIdList() : Collections.EMPTY_SET;
        //查询所有用户的部门
        Set<String> depts = dataScopeParam != null ? Optional.ofNullable(dataScopeParam.getDeptIdList()).orElse(new HashSet<>()).stream().filter(s -> {
            return !"-1".equals(s);
        }).collect(Collectors.toSet()) : Collections.EMPTY_SET;
        String userAgentId = Optional.ofNullable(dataScopeParam).map(DataScopeParam::getAgentId).orElse(null);
        Integer level = null;
        //总代数据
        List<String> allagents = new ArrayList<>();
        //二代数据
        List<String> twoagents = new ArrayList<>();
        if (StringUtils.isNotEmpty(userAgentId)){
            if (twoAgentCheck(roles)) {
                twoagents.add(userAgentId);
            }else {
                level = Optional.ofNullable(remoteAgentService.getAgentInfo(userAgentId).getObjEntity()).map(CrmAgentVo::getLevel).orElse(null);
                //判断是否为总代数据
                if ( AgentEnum.AgentLevelEnum.NATIONAL_DISTRIBUTOR.getLevel()==level||AgentEnum.AgentLevelEnum.PROVINCIAL_DISTRIBUTOR.getLevel()==level){
                    allagents.add(userAgentId);
                }else {
                    throw new CrmException(ResultEnum.AUTH_ERROR_500006);
                }
            }
        }else if (CollectionUtils.isNotEmpty(personList)) {
            allagents.addAll(queryAgentBypersonId(personList));
        }
        IPage<AgentPrePaymentVerificationVO> page = new Page<>();
        page.setSize(query.getPageSize());
        page.setCurrent(query.getPageNum());
        IPage<AgentPrePaymentVerificationVO> paymentVerificationIPage = crmAgentPaymentVerificationMapper.selectPrePages(page, query, depts, allagents, twoagents, AgentVerificationTypeEnum.IN_PRICE_DIFFERENCE.getCode());
        return new PageUtils<>(paymentVerificationIPage.getRecords().stream().map(agentPrePaymentVerificationVO -> {
            if (0==agentPrePaymentVerificationVO.getStatus()){
                agentPrePaymentVerificationVO.setFreezeAmount(agentPrePaymentVerificationVO.getRemainingPrepaymentAmount());
            }
            return agentPrePaymentVerificationVO;
        }).toList(), paymentVerificationIPage.getTotal(), query.getPageNum(), query.getPageSize());
    }

    @Override
    public List<AgentPrePaymentVerificationVO> queryPrePaymentVerificationList(String payerAgentId, String receivedAgentId) {
        List<AgentPrePaymentVerificationVO> agentPrePaymentVerificationVOS = crmAgentPaymentVerificationMapper.selectPrePaymentVerificationList(payerAgentId, receivedAgentId);
        return agentPrePaymentVerificationVOS.stream().map(agentPrePaymentVerificationVO -> {
            if (0==agentPrePaymentVerificationVO.getStatus()){
                agentPrePaymentVerificationVO.setFreezeAmount(agentPrePaymentVerificationVO.getRemainingPrepaymentAmount());
            }
            return agentPrePaymentVerificationVO;
        }).collect(Collectors.toList());
    }

    @Override
    public PageUtils<AgentReceivedlVO> advancesReceivedPage(AgentPaymentVerificationQuery query) {
        DataScopeParam dataScopeParam = query.getDataScopeParam();
        List<String> roles = UserInfoHolder.getLoginInfo().getRoles();
        Set<String> personList = dataScopeParam != null && com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(dataScopeParam.getPersonIdList()) ? dataScopeParam.getPersonIdList() : Collections.EMPTY_SET;
        //查询所有用户的部门
        Set<String> depts = dataScopeParam != null ? Optional.ofNullable(dataScopeParam.getDeptIdList()).orElse(new HashSet<>()).stream().filter(s -> {
            return !"-1".equals(s);
        }).collect(Collectors.toSet()) : Collections.EMPTY_SET;
        String userAgentId = Optional.ofNullable(dataScopeParam).map(DataScopeParam::getAgentId).orElse(null);
        Integer level = null;
        //总代数据
        List<String> allagents = new ArrayList<>();
        //二代数据
        List<String> twoagents = new ArrayList<>();
        if (StringUtils.isNotEmpty(userAgentId)){
            if (twoAgentCheck(roles)) {
                twoagents.add(userAgentId);
            }else {
                level = Optional.ofNullable(remoteAgentService.getAgentInfo(userAgentId).getObjEntity()).map(CrmAgentVo::getLevel).orElse(null);
                //判断是否为总代数据
                if ( AgentEnum.AgentLevelEnum.NATIONAL_DISTRIBUTOR.getLevel()==level||AgentEnum.AgentLevelEnum.PROVINCIAL_DISTRIBUTOR.getLevel()==level){
                    allagents.add(userAgentId);
                }else {
                    throw new CrmException(ResultEnum.AUTH_ERROR_500006);
                }
            }
        }else if (CollectionUtils.isNotEmpty(personList)) {
            allagents.addAll(queryAgentBypersonId(personList));
        }

        IPage<AgentReceivedlVO> page = new Page<>();
        page.setSize(query.getPageSize());
        page.setCurrent(query.getPageNum());
        IPage<AgentReceivedlVO> agentReceivedlVOS = crmAgentPaymentVerificationMapper.selectAdvancesReceivedPages(page, query, depts, allagents, twoagents);
        return new PageUtils<>(agentReceivedlVOS.getRecords(), agentReceivedlVOS.getTotal(), query.getPageNum(), query.getPageSize());
    }

    @Override
    public AgentPaymentVerificationVO queryInfoById(String id) {
        CrmAgentPaymentVerification agentPaymentVerification = getById(id);
        permissionCheck(agentPaymentVerification,null);
        return HyperBeanUtils.copyPropertiesByJackson(agentPaymentVerification, AgentPaymentVerificationVO.class);
    }

    @Override
    public PaymentVerificationDetailVO queryVetrificationDetailsById(String id,String type) {
        permissionCheck(null,id);
        List<CrmAgentPaymentVerificationDetail> details = agentVerificationDetailMapper.listByIds(Arrays.asList(id.split(",")));
        BigDecimal allAmount = new BigDecimal(0);
        AtomicReference<BigDecimal> sum = new AtomicReference<>(allAmount);
        List<AgentVerificationDetailVO> detailVOs = details.stream().filter(agentVerificationDetail->{
            return StringUtils.isEmpty(type) ||
                    !"repayment".equals(type) ||
                    AgentVerificationTypeEnum.IN_PRICE_DIFFERENCE.getCode() == agentVerificationDetail.getVerificationType();
        }).map(agentVerificationDetail -> {
            sum.set(sum.get().add(agentVerificationDetail.getWriteOffPrice()));
            return HyperBeanUtils.copyPropertiesByJackson(agentVerificationDetail, AgentVerificationDetailVO.class);
        }).collect(Collectors.toUnmodifiableList());
        allAmount = sum.get();
        return new PaymentVerificationDetailVO().setDetails(detailVOs).setAllAmount(allAmount);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reversal(ReversalDetailVO reversalDetailVO) {
        //更新总资产数据
        CrmAgentPaymentVerification agentPaymentVerification = getById(reversalDetailVO.getVerificationId());
        Assert.notNull(agentPaymentVerification, "回款核销记录不存在");
        permissionCheck(agentPaymentVerification,null);
        CrmAgentPaymentVerificationDetail agentVerificationDetailOld = fidDetailById(reversalDetailVO.getId());
        reversalCheck(agentVerificationDetailOld, reversalDetailVO);
        CrmAgentPaymentVerificationDetail newAgentVerificationDetail = HyperBeanUtils.copyPropertiesByJackson(reversalDetailVO, CrmAgentPaymentVerificationDetail.class);
        newAgentVerificationDetail.setId(UUIDUtils.generateUUID());
        newAgentVerificationDetail.setVerificationId(agentVerificationDetailOld.getVerificationId()).setReversal(1).setVerificationType(agentVerificationDetailOld.getVerificationType())
                .setPayerAgentId(agentVerificationDetailOld.getPayerAgentId()).setPayerAgentName(agentVerificationDetailOld.getPayerAgentName());
        CrmAgentPaymentVerificationDetail preAgent = agentVerificationDetailMapper.selectAdvancePayment(reversalDetailVO.getVerificationId(), AgentVerificationTypeEnum.IN_PRICE_DIFFERENCE.getCode());
        if (preAgent == null) {
            preAgent = initAgentVerificationDetail(agentVerificationDetailOld.getVerificationId(), BigDecimal.ZERO, null, AgentVerificationTypeEnum.IN_PRICE_DIFFERENCE.getCode());
        }
        //更新预付款的核销资金
        preAgent.setWriteOffPrice(preAgent.getWriteOffPrice().add(reversalDetailVO.getReversalAmount()));
        newAgentVerificationDetail.setWriteOffDate(LocalDateTime.now());
        //剩余可回冲金额 = 核销金额-回冲金额
        agentVerificationDetailOld.setRemainingRefundableAmount(agentVerificationDetailOld.getWriteOffPrice().subtract(reversalDetailVO.getReversalAmount()));

        agentPaymentVerification.setWrittenOffAmount(agentPaymentVerification.getWrittenOffAmount().subtract(reversalDetailVO.getReversalAmount()));
        agentPaymentVerification.setOutstandingReceivableAmount(agentPaymentVerification.getOutstandingReceivableAmount().add(reversalDetailVO.getReversalAmount()));
        preAgent.setStatus(checkFreezeFlag(agentPaymentVerification.getReceivedDate()));
        if (0 == preAgent.getStatus()) {
            preAgent.setFreezeTime(LocalDateTime.now());
        }
        agentVerificationDetailMapper.insertOrUpdate(preAgent);
        agentVerificationDetailMapper.updateById(agentVerificationDetailOld);
        updateById(agentPaymentVerification);

        //新增回冲数据
        newAgentVerificationDetail.setWriteOffPrice(reversalDetailVO.getWriteOffPrice().negate());
        //欠款金额 需要将回冲的资金传给业绩上报，且会返回实际的欠款金额
        PerformanceExecuteVO performanceExecuteVO = pagePerformanceReport(agentVerificationDetailOld.getPerformanceReportNumber());
        newAgentVerificationDetail.setDeptAmount(Optional.ofNullable(performanceExecuteVO).map(PerformanceExecuteVO::getDebtAmount).orElse(null));
        agentVerificationDetailMapper.insert(newAgentVerificationDetail);
    }

    @Override
    public DetailInfoVO queryDetailInfo(String id) {
        CrmAgentPaymentVerificationDetail agentVerificationDetailOld = fidDetailById(id);
        CrmAgentPaymentVerification agentPaymentVerification = getById(agentVerificationDetailOld.getVerificationId());
        Assert.notNull(agentPaymentVerification, "回款核销记录不存在");
        permissionCheck(agentPaymentVerification,null);
        checkCommon(agentVerificationDetailOld, false);
        DetailInfoVO detailInfoVO = HyperBeanUtils.copyPropertiesByJackson(agentVerificationDetailOld, DetailInfoVO.class);
        //查询业绩上报数据
        PerformanceExecuteVO performanceExecuteVO = pagePerformanceReport(agentVerificationDetailOld.getPerformanceReportNumber());
        detailInfoVO.setFinalCustomerName(Optional.ofNullable(performanceExecuteVO).map(PerformanceExecuteVO::getFinalCustomerName).orElse(null));
        detailInfoVO.setDeptAmount(Optional.ofNullable(performanceExecuteVO).map(PerformanceExecuteVO::getDebtAmount).orElse(null));
        detailInfoVO.setVerificationTotalAmount(Optional.ofNullable(performanceExecuteVO).map(PerformanceExecuteVO::getReturnedAmount).orElse(null));
        return detailInfoVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDetailInfo(SubmitDetailDTO submitDetailDTO) {
        CrmAgentPaymentVerificationDetail agentVerificationDetailOld = fidDetailById(submitDetailDTO.getId());
        CrmAgentPaymentVerification agentPaymentVerification = getById(submitDetailDTO.getVerificationId());
        Assert.notNull(agentPaymentVerification, "回款核销记录不存在");
        permissionCheck(agentPaymentVerification,null);
        //校验金额是否等于选择核销金额
        calculateAmount(submitDetailDTO, agentVerificationDetailOld.getWriteOffPrice(), false);
        List<CrmAgentPaymentVerificationDetail> agentVerificationDetailList = new ArrayList<>();
        //货款
        Set<String> idSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(submitDetailDTO.getDetailInfoList())) {
            submitDetailDTO.getDetailInfoList().forEach(detailInfoVO -> {
                if (agentVerificationDetailOld.getId().equals(detailInfoVO.getId())) {
                    agentVerificationDetailOld.setWriteOffPrice(detailInfoVO.getWriteOffPrice());
                    agentVerificationDetailOld.setDeptAmount(detailInfoVO.getDeptAmount());
                    agentVerificationDetailList.add(agentVerificationDetailOld);
                    idSet.add(agentVerificationDetailOld.getId());
                } else {
                    CrmAgentPaymentVerificationDetail agentVerificationDetail = HyperBeanUtils.copyPropertiesByJackson(agentVerificationDetailOld, CrmAgentPaymentVerificationDetail.class);
                    agentVerificationDetail.setVerificationType(AgentVerificationTypeEnum.PAYMENT.getCode()).setWriteOffDate(LocalDateTime.now()).setStatus(1)
                            .setId(UUIDUtils.generateUUID()).setWriteOffPrice(detailInfoVO.getWriteOffPrice()).setRemark(detailInfoVO.getRemark()).setPerformanceReportNumber(detailInfoVO.getPerformanceReportNumber())
                            .setCreateUser(UserInfoHolder.getCurrentPersonId());
                    idSet.add(agentVerificationDetail.getId());
                    agentVerificationDetailList.add(agentVerificationDetail);
                }

            });
        }
        if (!idSet.contains(submitDetailDTO.getId())&&AgentVerificationTypeEnum.PAYMENT.getCode()==agentVerificationDetailOld.getVerificationType()) {
            agentVerificationDetailOld.setDelFlag(true);
            updateById(agentPaymentVerification);
        }
        saveDetailInfo(submitDetailDTO, agentVerificationDetailList, agentPaymentVerification,agentVerificationDetailOld.getVerificationType());

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void applyAdvance(SubmitDetailDTO submitDetailDTO) {
        CrmAgentPaymentVerification agentPaymentVerification = getById(submitDetailDTO.getVerificationId());
        Assert.notNull(agentPaymentVerification, "回款核销记录不存在");
        permissionCheck(agentPaymentVerification,null);
        List<CrmAgentPaymentVerificationDetail> agentVerificationDetailList = new ArrayList<>();
        //校验金额是否等于选择核销金额
        calculateAmount(submitDetailDTO, agentPaymentVerification.getOutstandingReceivableAmount(), true);
        //货款
        if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(submitDetailDTO.getDetailInfoList())) {
            submitDetailDTO.getDetailInfoList().forEach(detailInfoVO -> {
                CrmAgentPaymentVerificationDetail agentVerificationDetail = HyperBeanUtils.copyPropertiesByJackson(detailInfoVO, CrmAgentPaymentVerificationDetail.class);
                agentVerificationDetail.setVerificationType(AgentVerificationTypeEnum.PAYMENT.getCode()).setWriteOffDate(LocalDateTime.now())
                        .setStatus(1)
                        .setId(null).setVerificationId(submitDetailDTO.getVerificationId()).setWriteOffPrice(detailInfoVO.getWriteOffPrice()).setRemark(detailInfoVO.getRemark()).setPerformanceReportNumber(detailInfoVO.getPerformanceReportNumber())
                        .setCreateUser(UserInfoHolder.getCurrentPersonId());
                agentVerificationDetailList.add(agentVerificationDetail);
            });
        }
        saveDetailInfo(submitDetailDTO, agentVerificationDetailList, agentPaymentVerification,null);
    }

    public void saveDetailInfo(SubmitDetailDTO submitDetailDTO, List<CrmAgentPaymentVerificationDetail> agentVerificationDetailList, CrmAgentPaymentVerification agentPaymentVerification,Integer type) {

        //预付款
        if (submitDetailDTO.getAdvancePayment() != null && submitDetailDTO.getAdvancePayment().getWriteOffPrice() != null) {
            CrmAgentPaymentVerificationDetail advancePayment = agentVerificationDetailMapper.selectAdvancePayment(submitDetailDTO.getVerificationId(), AgentVerificationTypeEnum.IN_PRICE_DIFFERENCE.getCode());
            if (advancePayment == null) {
                advancePayment = initAgentVerificationDetail(submitDetailDTO.getVerificationId(), submitDetailDTO.getAdvancePayment().getWriteOffPrice(), submitDetailDTO.getAdvancePayment().getRemark(), AgentVerificationTypeEnum.IN_PRICE_DIFFERENCE.getCode());
                advancePayment.setStatus(checkFreezeFlag(agentPaymentVerification.getReceivedDate()));
                if (0 == advancePayment.getStatus()) {
                    advancePayment.setFreezeTime(LocalDateTime.now());
                }
            } else if (type!=null&&AgentVerificationTypeEnum.IN_PRICE_DIFFERENCE.getCode()==type){
                advancePayment.setRemark(submitDetailDTO.getAdvancePayment().getRemark());
                advancePayment.setWriteOffPrice(submitDetailDTO.getAdvancePayment().getWriteOffPrice());
            } else {
                advancePayment.setRemark(submitDetailDTO.getAdvancePayment().getRemark());
                advancePayment.setWriteOffPrice(advancePayment.getWriteOffPrice().add(submitDetailDTO.getAdvancePayment().getWriteOffPrice()));
            }

            agentVerificationDetailList.add(advancePayment);
        }
        //other
        if (submitDetailDTO.getOther() != null && submitDetailDTO.getOther().getWriteOffPrice() != null) {
            CrmAgentPaymentVerificationDetail other = agentVerificationDetailMapper.selectAdvancePayment(submitDetailDTO.getVerificationId(), AgentVerificationTypeEnum.OTHER.getCode());
            if (other == null) {
                other = initAgentVerificationDetail(submitDetailDTO.getVerificationId(), submitDetailDTO.getOther().getWriteOffPrice(), submitDetailDTO.getOther().getRemark(), AgentVerificationTypeEnum.OTHER.getCode());
            } else if (type!=null&&AgentVerificationTypeEnum.OTHER.getCode()==type){
                other.setRemark(submitDetailDTO.getAdvancePayment().getRemark());
                other.setWriteOffPrice(submitDetailDTO.getOther().getWriteOffPrice());
            } else {
                other.setRemark(submitDetailDTO.getOther().getRemark());
                other.setWriteOffPrice(other.getWriteOffPrice().add(submitDetailDTO.getOther().getWriteOffPrice()));
            }
            agentVerificationDetailList.add(other);
        }
        agentVerificationDetailMapper.insertOrUpdate(agentVerificationDetailList);
        recalculate(submitDetailDTO.getVerificationId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savePreDetailInfo(SubmitPreDetailDTO submitDetailDTO) {
        CrmAgentPaymentVerification agentPaymentVerification = getById(submitDetailDTO.getId());
        Assert.notNull(agentPaymentVerification, "回款核销记录不存在");
        List<CrmAgentPaymentVerificationDetail> agentVerificationDetailList = new ArrayList<>();
        //货款
        if (CollectionUtils.isNotEmpty(submitDetailDTO.getDetailInfoList())) {
            submitDetailDTO.getDetailInfoList().forEach(detailInfoVO -> {
                CrmAgentPaymentVerificationDetail agentVerificationDetail = HyperBeanUtils.copyPropertiesByJackson(detailInfoVO, CrmAgentPaymentVerificationDetail.class);
                agentVerificationDetail.setVerificationType(AgentVerificationTypeEnum.PAYMENT.getCode())
                        .setId(null).setWriteOffPrice(detailInfoVO.getWriteOffPrice())
                        .setWriteOffDate(LocalDateTime.now()).setRemark(detailInfoVO.getRemark()).setPerformanceReportNumber(detailInfoVO.getPerformanceReportNumber())
                        .setVerificationId(agentPaymentVerification.getId());
                agentVerificationDetail.setCreateUser(detailInfoVO.getApplyUserId());
                agentVerificationDetail.setUpdateUser(detailInfoVO.getApplyUserId());
                agentVerificationDetailList.add(agentVerificationDetail);
            });
        }
        agentVerificationDetailMapper.insert(agentVerificationDetailList);
        recalculate(agentPaymentVerification.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String addVerification(AgentPaymentProcessSubmitDTO submitDetailDTO) {
        CrmAgentPaymentVerification agentPaymentVerification = HyperBeanUtils.copyPropertiesByJackson(submitDetailDTO, CrmAgentPaymentVerification.class);
        agentPaymentVerification.setId(UUIDUtils.generateUUID());
        agentPaymentVerification.setCreateUser(submitDetailDTO.getApplyId());
        agentPaymentVerification.setUpdateUser(submitDetailDTO.getApplyId());
        List<CrmAgentPaymentVerificationDetail> agentVerificationDetailList = new ArrayList<>();
        if (AgentVerificationTypeEnum.IN_PRICE_DIFFERENCE.getCode() == submitDetailDTO.getVerificationType()) {
            CrmAgentPaymentVerificationDetail agentVerificationDetail = initAgentVerificationDetail(agentPaymentVerification.getId(), submitDetailDTO.getCollectionAmount(), null, submitDetailDTO.getVerificationType());
            agentVerificationDetail.setWriteOffPrice(BigDecimal.ZERO);
            agentVerificationDetail.setStatus(checkFreezeFlag(submitDetailDTO.getReceivedDate().toLocalDate()));
            if (0 == agentVerificationDetail.getStatus()) {
                agentVerificationDetail.setFreezeTime(LocalDateTime.now());
            }
            agentVerificationDetail.setCreateUser(submitDetailDTO.getApplyId());
            agentVerificationDetail.setUpdateUser(submitDetailDTO.getApplyId());
            agentVerificationDetailList.add(agentVerificationDetail);
        } else if (AgentVerificationTypeEnum.PAYMENT.getCode() == submitDetailDTO.getVerificationType()) {
            submitDetailDTO.getDetailInfoList().forEach(detailInfoVO -> {
                CrmAgentPaymentVerificationDetail agentVerificationDetail = HyperBeanUtils.copyPropertiesByJackson(detailInfoVO, CrmAgentPaymentVerificationDetail.class);
                agentVerificationDetail.setCreateUser(submitDetailDTO.getApplyId());
                agentVerificationDetail.setUpdateUser(submitDetailDTO.getApplyId());
                agentVerificationDetail.setVerificationId(agentPaymentVerification.getId());
                //此时的核销时间是收款时间
                agentVerificationDetail.setWriteOffDate(submitDetailDTO.getReceivedDate());
                agentVerificationDetail.setVerificationType(AgentVerificationTypeEnum.PAYMENT.getCode());
                agentVerificationDetailList.add(agentVerificationDetail);
            });
        }
        if (CollectionUtils.isNotEmpty(agentVerificationDetailList)) {
            agentVerificationDetailMapper.insertOrUpdate(agentVerificationDetailList);
        } else {
            throw new CrmException("核销分类为货款,货款明细不能为空");
        }
        save(agentPaymentVerification);
        recalculate(agentPaymentVerification.getId());
        return agentPaymentVerification.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteDetailInfo(String id) {
        CrmAgentPaymentVerificationDetail agentVerificationDetailOld = fidDetailById(id);
        CrmAgentPaymentVerification agentPaymentVerification = getById(agentVerificationDetailOld.getVerificationId());
        Assert.notNull(agentPaymentVerification, "回款核销记录不存在");
        permissionCheck(agentPaymentVerification,null);
        if (AgentVerificationTypeEnum.OTHER.getCode() == agentVerificationDetailOld.getVerificationType() || AgentVerificationTypeEnum.PAYMENT.getCode() == agentVerificationDetailOld.getVerificationType()) {
            CrmAgentPaymentVerificationDetail advancePayment = agentVerificationDetailMapper.selectAdvancePayment(agentVerificationDetailOld.getVerificationId(), AgentVerificationTypeEnum.IN_PRICE_DIFFERENCE.getCode());
            if (advancePayment == null) {
                advancePayment = initAgentVerificationDetail(agentVerificationDetailOld.getVerificationId(), agentVerificationDetailOld.getWriteOffPrice(), null, AgentVerificationTypeEnum.IN_PRICE_DIFFERENCE.getCode());
                advancePayment.setStatus(checkFreezeFlag(agentPaymentVerification.getReceivedDate()));
                if (0 == advancePayment.getStatus()) {
                    advancePayment.setFreezeTime(LocalDateTime.now());
                }
            } else {
                advancePayment.setWriteOffPrice(advancePayment.getWriteOffPrice().add(agentVerificationDetailOld.getWriteOffPrice()));
            }
            agentVerificationDetailMapper.insertOrUpdate(advancePayment);
        }
        agentVerificationDetailOld.setDelFlag(true);
        agentVerificationDetailMapper.updateById(agentVerificationDetailOld);
        recalculate(agentVerificationDetailOld.getVerificationId());
    }

    @Override
    public List<AgentProcessVO> queryVerificationProcessList(String verificationId) {

        CrmAgentPaymentVerification agentPaymentVerification = getById(verificationId);
        Assert.notNull(agentPaymentVerification, "回款核销记录不存在");
        permissionCheck(agentPaymentVerification,null);
        // 权限过滤 todo 待完成

        List<String> verificationIds = new ArrayList<>();
        verificationIds.add(agentPaymentVerification.getId());
        //预付款使用
        List<AgentPrepaymentUsageDTO> refundFlowLaunchDTOS = remoteAgentPrepaymentUsageService.list(verificationId).getObjEntity();

        //渠道付款申请
        TfsFormContentVo tfsFormContentVo = tfsFormContentClient.findByProcessInstanceId(agentPaymentVerification.getPaymentDisburseProcessInstanceId()).getObjEntity();
        ArrayList<AgentProcessVO> result = new ArrayList<>();
        result.addAll(refundFlowLaunchDTOS.stream().map(agentPrepaymentUsageDTO -> {
            AgentProcessVO agentProcessVO =HyperBeanUtils.copyPropertiesByJackson(agentPrepaymentUsageDTO, AgentProcessVO.class);
            agentProcessVO.setProcessName("预付款使用");
            return agentProcessVO;
        }).collect(Collectors.toUnmodifiableList()));
        if (tfsFormContentVo != null) {
            result.add(initProccess(tfsFormContentVo));
        }
        if (result.isEmpty()) {
            return Collections.emptyList();
        }

        // 流程节点信息
        Map<String, Set<ApproveNode>> flowMap = tfsNodeClient.queryNodeByProcessInstanceIdList(result.stream().map(AgentProcessVO::getProcessInstanceId).collect(Collectors.toList())).getObjEntity();
        result.forEach(item -> {
            item.setCreateUserName(NameUtils.getName(item.getCreateUser()));
            Set<ApproveNode> approveNodes = flowMap.get(item.getProcessInstanceId());
            if (approveNodes != null) {
                item.setProcessStep(approveNodes.stream()
                        .map(ApproveNode::getNodeName)
                        .collect(Collectors.joining(",")));
            } else {
                // 处理 approveNodes 为 null 的情况
                item.setProcessStep("流程已办结");
            }
        });
        return result;
    }

    private AgentProcessVO initProccess(TfsFormContentVo tfsFormContentVo) {
        AgentProcessVO agentProcessVO = HyperBeanUtils.copyProperties(tfsFormContentVo, AgentProcessVO::new);
        agentProcessVO.setCreateUser(tfsFormContentVo.getPersonId());
        agentProcessVO.setProcessName("渠道付款申请");
        return agentProcessVO;
    }

    @Override
    public void modifyCarNumber(ModifyCarNumberDTO modifyCarNumberDTO) {
        CrmAgentPaymentVerification agentPaymentVerification = getById(modifyCarNumberDTO.getId());
        Assert.notNull(agentPaymentVerification, "回款核销记录不存在");
        permissionCheck(agentPaymentVerification,null);
        List<CrmAgentPaymentVerification> crmAgentPaymentVerifications = list(new LambdaQueryWrapper<CrmAgentPaymentVerification>().ne(CrmAgentPaymentVerification::getId, modifyCarNumberDTO.getId()).eq(CrmAgentPaymentVerification::getAccountNumber, modifyCarNumberDTO.getAccountNumber()));
        Assert.isFalse(CollectionUtils.isNotEmpty(crmAgentPaymentVerifications), "进账单号已使用");
        agentPaymentVerification.setAccountNumber(modifyCarNumberDTO.getAccountNumber());
        updateById(agentPaymentVerification);
    }

    @Override
    public PrepaymentSummaryVO verificationCount(String payerAgentName,String receivedAgentName) {
        List<CrmAgentPaymentVerificationDetail> agentPaymentVerificationDetails = agentVerificationDetailMapper.selectPrepaymentSummary(payerAgentName,receivedAgentName);
        BigDecimal allPreAmount = BigDecimal.ZERO;
        BigDecimal allFrezzAmount = BigDecimal.ZERO;
        AtomicReference<BigDecimal> sum1 = new AtomicReference<>(allPreAmount);
        AtomicReference<BigDecimal> sum2 = new AtomicReference<>(allFrezzAmount);
        agentPaymentVerificationDetails.forEach(detailInfoVO -> {
            if (detailInfoVO.getStatus()==1){
                sum1.set(sum1.get().add(detailInfoVO.getWriteOffPrice()));
            }else if (detailInfoVO.getStatus()==0){
                sum2.set(sum2.get().add(detailInfoVO.getWriteOffPrice()));
            }
        });
        allPreAmount = sum1.get();
        allFrezzAmount = sum2.get();
        return new PrepaymentSummaryVO().setAllFrezzAmount(allFrezzAmount).setAllPreAmount(allPreAmount);
    }

    @Override
    public List<PrepaymentSummaryVO> batchVerificationCount(List<Map<String, String>> params) {
        List<PrepaymentSummaryVO> list = new ArrayList<>();
        IntStream.range(0, params.size()).parallel().forEach(index -> {
            Map<String, String> param = params.get(index);
            PrepaymentSummaryVO prepaymentSummaryVO = verificationCount(param.get("payerAgentName"),param.get("receivedAgentName"));
            prepaymentSummaryVO.setPayerAgentName(param.get("payerAgentName"));
            prepaymentSummaryVO.setReceivedAgentName(param.get("receivedAgentName"));
            list.add(prepaymentSummaryVO);
        });
        return list;
    }

    @Override
    public PageUtils<PerformanceExecuteVO> pagePerformanceReport(PerformanceExecuteQuery query) {
        PageUtils<PerformanceExecuteVO> pageUtils = null;
        query.setReportStatus(1);
        if (StringUtils.isBlank(UserInfoHolder.getCurrentAgentId())) {
            pageUtils = remotePerformanceExecuteService.pageNternal(query).getObjEntity();
        } else {
            pageUtils = remotePerformanceExecuteService.pageAgent(query).getObjEntity();
        }
        return pageUtils;
    }

    private void permissionCheck(CrmAgentPaymentVerification agentPaymentVerification, String id){
        if (agentPaymentVerification == null){
            agentPaymentVerification = getById(id);
            Assert.notNull(agentPaymentVerification, "回款核销记录不存在");
        }
        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();
        List<String> roles = UserInfoHolder.getLoginInfo().getRoles();
        String userAgentId = Optional.ofNullable(dataScopeParam).map(DataScopeParam::getAgentId).orElse(null);
        Integer level = null;
        Set<String> personList = dataScopeParam != null && com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(dataScopeParam.getPersonIdList()) ? dataScopeParam.getPersonIdList() : Collections.EMPTY_SET;
        //查询所有用户的部门
        Set<String> depts = dataScopeParam != null ? Optional.ofNullable(dataScopeParam.getDeptIdList()).orElse(new HashSet<>()).stream().filter(s -> {
            return !"-1".equals(s);
        }).collect(Collectors.toSet()) : Collections.EMPTY_SET;
        //总代数据
        List<String> allagents = new ArrayList<>();
        //二代数据
        List<String> twoagents = new ArrayList<>();
        if (StringUtils.isNotEmpty(userAgentId)){
            if (twoAgentCheck(roles)) {
                twoagents.add(userAgentId);
            }else {
                level = Optional.ofNullable(remoteAgentService.getAgentInfo(userAgentId).getObjEntity()).map(CrmAgentVo::getLevel).orElse(null);
                //判断是否为总代数据
                if ( AgentEnum.AgentLevelEnum.NATIONAL_DISTRIBUTOR.getLevel()==level||AgentEnum.AgentLevelEnum.PROVINCIAL_DISTRIBUTOR.getLevel()==level){
                    allagents.add(userAgentId);
                }else {
                    throw new CrmException(ResultEnum.AUTH_ERROR_500006);
                }
            }
        }else if (CollectionUtils.isNotEmpty(personList)) {
            allagents.addAll(queryAgentBypersonId(personList));
        }
        Boolean checkResult = false;
        if (CollectionUtils.isNotEmpty(allagents)) {
            for (String agentId : allagents) {
                if (agentPaymentVerification.getReceivedAgentId().equals(agentId)||agentPaymentVerification.getPayerAgentId().equals(agentId)) {
                    checkResult = true;
                    break;
                }
            }
        }else if (CollectionUtils.isNotEmpty(twoagents)) {
            for (String agentId : twoagents) {
                if (agentPaymentVerification.getPayerAgentId().equals(agentId)) {
                    checkResult = true;
                    break;
                }
            }
        }else {
            checkResult = true;
        }
        if (!checkResult) {
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

    // 权限过滤
    private List<PaymentVerificationVO> authFilter(List<PaymentVerificationVO> list) {

        if (com.alibaba.nacos.common.utils.CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        Set<String> personIdList = PreAuthorizeAspect.getDataScopeParam().getPersonIdList();
        // null 为最大权限
        if (com.alibaba.nacos.common.utils.CollectionUtils.isEmpty(personIdList)) {
            return list;
        }
        // 借试用
        CompletableFuture<List<String>> borrowNumberFuture = CompletableFuture.supplyAsync(() -> {
            BorrowFlowQuery borrowFlowQuery = new BorrowFlowQuery();
            borrowFlowQuery.setBorrowerPersonIds(personIdList);
//            return flowBorrowForProbationService.borrowForProbationList(borrowFlowQuery)
//                    .getObjEntity()
//                    .parallelStream()
//                    .map(BorrowFlowPartVO::getProcessNumber)
//                    .toList();
            return null;
        });

        // 合同
        CompletableFuture<List<String>> contractNumberFuture = CompletableFuture.supplyAsync(() -> {
            CrmContractAfterQuery crmContractAfterQuery = new CrmContractAfterQuery();
            CrmContractAfterQuery.BaseQuery baseQuery = new CrmContractAfterQuery.BaseQuery();
            baseQuery.setContractOwnerIds(personIdList.stream().toList());
            crmContractAfterQuery.setBaseQuery(baseQuery);
//            return contractExecuteService.pageByCondition(crmContractAfterQuery)
//                    .getList()
//                    .parallelStream()
//                    .map(CrmContractExecuteVO::getContractNumber)
//                    .toList();
            return null;
        });

        CompletableFuture<Void> combinedFuture = CompletableFuture.allOf(borrowNumberFuture, contractNumberFuture);
        try {
            combinedFuture.get();
        } catch (InterruptedException | ExecutionException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("数据获取失败", e);
        }

        List<String> borrowNumber = borrowNumberFuture.join();
        List<String> contractNumber = contractNumberFuture.join();
        // TODO 借款

        // 预付款 不过滤
        // 借试用 过滤掉不在borrowNumber
        // 货款 过滤掉不在contractNumber中的
        // 退款 过滤掉createUser不在personIdList中的
        return list.stream()
                .filter(item -> {
                    if (item.getVerificationType().equals(PaymentVerificationVO.VerificationTypeEnum.ADVANCE.getCode())) {
                        return true;
                    }
                    if (item.getVerificationType().equals(PaymentVerificationVO.VerificationTypeEnum.FOR_GOODS.getCode())) {
                        return contractNumber.contains(item.getBusinessNumber());
                    }
                    if (item.getVerificationType().equals(PaymentVerificationVO.VerificationTypeEnum.DEPOSIT.getCode())) {
                        return borrowNumber.contains(item.getBusinessNumber());
                    }
                    if (item.getVerificationType().equals(PaymentVerificationVO.VerificationTypeEnum.REFUND.getCode())) {
                        return borrowNumber.contains(item.getBusinessNumber());
                    }
                    return false;
                }).toList();
    }

    /**
     * @param submitDetailDTO
     * @param verificationAmount
     * @return void
     * <AUTHOR>
     * @description 校验是否操作金额累计金额等于核销金额
     * @date 2025/7/18 16:40
     **/
    private void calculateAmount(SubmitDetailDTO submitDetailDTO, BigDecimal verificationAmount, boolean isSave) {
        BigDecimal amount = BigDecimal.ZERO;
        if (submitDetailDTO.getAdvancePayment() != null&&submitDetailDTO.getAdvancePayment().getWriteOffPrice()!=null) {
            amount = amount.add(submitDetailDTO.getAdvancePayment().getWriteOffPrice());
        }
        if (submitDetailDTO.getOther() != null&&submitDetailDTO.getOther().getWriteOffPrice()!=null) {
            amount = amount.add(submitDetailDTO.getOther().getWriteOffPrice());
        }
        if (CollectionUtils.isNotEmpty(submitDetailDTO.getDetailInfoList())) {
            AtomicReference<BigDecimal> sum = new AtomicReference<>(amount);
            submitDetailDTO.getDetailInfoList().forEach(detailInfoVO -> {
                sum.set(sum.get().add(detailInfoVO.getWriteOffPrice()));
            });
            amount = sum.get();
        }
        if (!isSave && verificationAmount.compareTo(amount) != 0) {
            log.error("总核销金额不等于所选择的核销金额:总核销金额:{},核销金额:{}", verificationAmount, amount);
            throw new CrmException("总核销金额不等于所选择的核销金额");
        } else if (isSave && verificationAmount.compareTo(amount) < 0) {
            log.error("所选择的核销金额不能超过剩余收款金额:剩余收款金额:{},剩余收款金额:{}", verificationAmount, amount);
            throw new CrmException("所选择的核销总金额不能超过剩余收款金额");
        }
    }

    private CrmAgentPaymentVerificationDetail fidDetailById(String id) {
        CrmAgentPaymentVerificationDetail agentVerificationDetailOld = agentVerificationDetailMapper.selectById(id);
        Assert.notNull(agentVerificationDetailOld, "原核销明细不存在");
        return agentVerificationDetailOld;
    }

    /**
     * @param agentVerificationDetailOld
     * @param reversalDetailVO
     * @return void
     * <AUTHOR>
     * @description 回冲前数据校验
     * @date 2025/7/21 10:36
     **/
    private void reversalCheck(CrmAgentPaymentVerificationDetail agentVerificationDetailOld, ReversalDetailVO reversalDetailVO) {
        if (AgentVerificationTypeEnum.PAYMENT.getCode() != agentVerificationDetailOld.getVerificationType()) {
            log.error("回冲【核销明细id-业绩上报单号：{}-{}】,只能回冲货款类型的数据", reversalDetailVO.getVerificationId(), reversalDetailVO.getPerformanceNumber());
            throw new CrmException("只能回冲货款类型的数据");
        } else if (agentVerificationDetailOld.getWriteOffPrice().compareTo(BigDecimal.ZERO) < 0) {
            log.error("回冲【核销明细id-业绩上报单号：{}-{}】,核销金额小于0，无法回冲", reversalDetailVO.getVerificationId(), reversalDetailVO.getPerformanceNumber());
            throw new CrmException("核销金额小于0，无法回冲");
        } else if (agentVerificationDetailOld.getRemainingRefundableAmount().compareTo(reversalDetailVO.getReversalAmount()) < 0) {
            log.error("回冲【核销明细id-业绩上报单号：{}-{}】,回冲金额应小于等于核销金额", reversalDetailVO.getVerificationId(), reversalDetailVO.getPerformanceNumber());
            throw new CrmException("回冲金额应小于等于剩余可回冲金额");
        }
        int month = agentVerificationDetailOld.getCreateTime().getMonthValue();
        int nowmonth = LocalDate.now().getMonthValue();
        if (agentVerificationDetailOld.getCreateTime().isAfter(LocalDateTime.now())) {
            log.error("回冲【核销明细id-业绩上报单号：{}-{}】,回冲数据的时间异常：不应该大于当前时间", reversalDetailVO.getVerificationId(), reversalDetailVO.getPerformanceNumber());
            throw new CrmException("回冲数据的时间异常：不应该大于当前时间");
        } else if (month >= nowmonth) {
            log.error("回冲【核销明细id-业绩上报单号：{}-{}】,回冲数据的时间异常：只能回冲非当月数据", reversalDetailVO.getVerificationId(), reversalDetailVO.getPerformanceNumber());
            throw new CrmException("只能回冲非当月数据");
        }
    }

    /**
     * @param agentPaymentId
     * @param verificationAmount
     * @param remark
     * @param verificationType
     * @return com.topsec.crm.agent.core.entity.AgentVerificationDetail
     * <AUTHOR>
     * @description 主要初始化 预付款和其他类型的核销明细
     * @date 2025/7/18 17:08
     **/
    private CrmAgentPaymentVerificationDetail initAgentVerificationDetail(String agentPaymentId, BigDecimal verificationAmount, String remark, Integer verificationType) {
        return new CrmAgentPaymentVerificationDetail().setVerificationId(agentPaymentId).setVerificationType(verificationType)
                .setWriteOffPrice(verificationAmount).setWriteOffDate(LocalDateTime.now()).setRemark(remark);
    }


    /**
     * @param personList
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @description 根据用户id来查询负责的渠道商
     * @date 2025/7/17 17:33
     **/
    private List<String> queryAgentBypersonId(Set<String> personList) {
        JsonObject<List<CrmAgentVo>> mapJsonObject = remoteAgentService.getAgentsByPersonIds(personList.stream().toList());
        return Optional.ofNullable(mapJsonObject.getObjEntity().stream().map(CrmAgentVo::getId).collect(Collectors.toList())).orElse(Collections.emptyList());
    }

    /**
     * @param contractNumber
     * @return com.topsec.crm.flow.api.dto.performancereport.PerformanceExecuteVO
     * <AUTHOR>
     * @description 根据业绩上报单号查询业绩上报数据
     * @date 2025/7/18 15:47
     **/
    public PerformanceExecuteVO pagePerformanceReport(String contractNumber) {
        PerformanceExecuteQuery query = new PerformanceExecuteQuery();
        query.setContractNumber(contractNumber);
        query.setReportStatus(1);
        query.setDebtAmountGt(BigDecimal.ZERO);
        PageUtils<PerformanceExecuteVO> pageUtils = null;
        if (StringUtils.isBlank(UserInfoHolder.getCurrentAgentId())) {
            pageUtils = remotePerformanceExecuteService.pageNternal(query).getObjEntity();
        } else {
            pageUtils = remotePerformanceExecuteService.pageAgent(query).getObjEntity();
        }
        if (pageUtils == null || pageUtils.getList().size() <= 0) {
            return null;
        } else {
            return pageUtils.getList().get(0);
        }
    }

    /**
     * @param roles
     * @return boolean
     * <AUTHOR>
     * @description 判断用户是否是二代
     * @date 2025/7/17 17:15
     **/
    private static boolean twoAgentCheck(List<String> roles) {
        return CollectionUtils.isNotEmpty(roles) && roles.stream().anyMatch(role -> role.contains(TbsConstants.RoleId.CRM_AGENT_ED) || role.contains(TbsConstants.RoleId.CRM_AGENT_ED_ZL) || role.contains(TbsConstants.RoleId.CRM_AGENT_ED_SQ));
    }

    /**
     * @param agentPaymentId
     * @return void
     * <AUTHOR>
     * @description 重新计算核销金额
     * @date 2025/7/18 17:22
     **/
    private void recalculate(String agentPaymentId) {
        List<CrmAgentPaymentVerificationDetail> list = agentVerificationDetailMapper.listByIds(Arrays.asList(agentPaymentId));
        BigDecimal writtenOffAmount = BigDecimal.ZERO;
        if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(list)) {
            AtomicReference<BigDecimal> sum = new AtomicReference<>(writtenOffAmount);
            list.forEach(detail -> {
                sum.set(sum.get().add(detail.getWriteOffPrice()));
            });
            writtenOffAmount = sum.get();
        }
        CrmAgentPaymentVerification agentPaymentVerification = getById(agentPaymentId);
        if (agentPaymentVerification != null) {
            agentPaymentVerification.setWrittenOffAmount(writtenOffAmount);
            agentPaymentVerification.setOutstandingReceivableAmount(agentPaymentVerification.getCollectionAmount().subtract(writtenOffAmount));
            if (agentPaymentVerification.getOutstandingReceivableAmount().compareTo(BigDecimal.ZERO) < 0) {
                log.error("回款核销记录【{}】剩余收款金额小于0", agentPaymentId);
                throw new CrmException("回款核销记录剩余收款金额小于0");
            }
            updateById(agentPaymentVerification);
        } else {
            log.error("回款核销记录【{}】不存在", agentPaymentId);
            throw new CrmException("回款核销记录不存在");
        }
    }

    private void checkCommon(CrmAgentPaymentVerificationDetail agentVerificationDetailOld, Boolean checkAmout) {
        int month = agentVerificationDetailOld.getCreateTime().getMonthValue();
        int nowmonth = LocalDate.now().getMonthValue();
        if (agentVerificationDetailOld.getCreateTime().isAfter(LocalDateTime.now())) {
            log.error("核销明细【核销明细id-业绩上报单号：{}-{}】,数据的时间异常：不应该大于当前时间", agentVerificationDetailOld.getVerificationId(), agentVerificationDetailOld.getPerformanceReportNumber());
            throw new CrmException("修改数据的时间异常：不应该大于当前时间");
        } else if (month != nowmonth) {
            log.error("核销明细【核销明细id-业绩上报单号：{}-{}】,数据的时间异常：回冲非当月数据", agentVerificationDetailOld.getVerificationId(), agentVerificationDetailOld.getPerformanceReportNumber());
            throw new CrmException("只能修改当月数据");
        }
        if (checkAmout && agentVerificationDetailOld.getWriteOffPrice().compareTo(BigDecimal.ZERO) < 0) {
            log.error("核销明细【核销明细id-业绩上报单号：{}-{}】,数据的时间异常：核销金额小于0", agentVerificationDetailOld.getVerificationId(), agentVerificationDetailOld.getPerformanceReportNumber());
            throw new CrmException("核销金额小于0");
        }
    }

    private static Integer checkFreezeFlag(LocalDate date) {
        long daysBetween = ChronoUnit.DAYS.between(date, LocalDate.now());
        // 判断是否小于30天
        if (daysBetween < 30) {
            log.info("该日期在30天内，相差" + daysBetween + "天");
            return 0;
        } else {
            log.info("该日期超过30天，相差" + daysBetween + "天");
            return 1;
        }
    }


    public static BigDecimal ifNullToZero(BigDecimal value) {
        return value == null ? BigDecimal.ZERO : value;
    }
}
