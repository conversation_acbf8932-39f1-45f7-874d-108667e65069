package com.topsec.crm.contract.core.controller;


import com.topsec.crm.contract.api.entity.crmagentpayment.CrmAgentAdvancePaymentQuery;
import com.topsec.crm.contract.api.entity.crmagentpayment.CrmAgentAdvancePaymentVO;
import com.topsec.crm.contract.api.entity.crmagentpayment.agentpaymentverification.*;
import com.topsec.crm.contract.api.entity.paymentcollection.PaymentVerificationProcessVO;
import com.topsec.crm.contract.core.service.AgentPaymentAttachmentService;
import com.topsec.crm.contract.core.service.CrmAgentPaymentVerificationService;
import com.topsec.crm.flow.api.dto.performancereport.PerformanceExecuteQuery;
import com.topsec.crm.flow.api.dto.performancereport.PerformanceExecuteVO;
import com.topsec.crm.framework.common.util.PageIterator;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.poi.ExcelUtil;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ForkJoinPool;
import java.util.function.BiFunction;

/**
 * 渠道-回款核销 前端控制器
 */
@RestController
@RequestMapping("/crmAgentPaymentVerification")
@Tag(name = "渠道回款核销", description = "/crmAgentPaymentVerification")
public class CrmAgentPaymentVerificationController extends BaseController {

    @Resource
    private CrmAgentPaymentVerificationService agentAuthPaymentDisburseService;

    @Resource
    private AgentPaymentAttachmentService agentPaymentAttachmentService;

    @PostMapping("/advancePaymentPage")
    @Operation(summary = "渠道预付款汇总列表")
    @PreAuthorize(hasPermission = "crm_agent_repayment_stats",dataScope="crm_agent_repayment_stats")
    public JsonObject<PageUtils<CrmAgentAdvancePaymentVO>> advancePaymentPage(@RequestBody CrmAgentAdvancePaymentQuery query) {
        return new JsonObject<>(agentAuthPaymentDisburseService.advancePaymentPage(query));
    }

    @PostMapping("/verificationPage")
    @Operation(summary = "渠道回款核销列表")
    @PreAuthorize(hasPermission = "crm_agent_repayment_verify",dataScope = "crm_agent_repayment_verify")
    public JsonObject<PageUtils<AgentPaymentVerificationVO>> verificationPage(@RequestBody AgentPaymentVerificationQuery query) {
        return new JsonObject<>(agentAuthPaymentDisburseService.verificationPage(query));
    }

    @PreAuthorize(hasPermission = "crm_agent_repayment_verify_export",dataScope = "crm_agent_repayment_verify")
    @PostMapping("/export")
    @Operation(summary = "渠道回款核销-导出", method = "POST")
    public void export(@RequestBody AgentPaymentVerificationQuery query) throws Exception {
        BiFunction<Integer, Integer, PageUtils<AgentPaymentVerificationVO>> call = (pageNo, pageSize) -> {
            AgentPaymentVerificationQuery paramsNew = HyperBeanUtils.copyPropertiesByJackson(query, AgentPaymentVerificationQuery.class);
            paramsNew.setPageNum(pageNo);
            paramsNew.setPageSize(pageSize);
            PageUtils<AgentPaymentVerificationVO> page = agentAuthPaymentDisburseService.verificationPage(query);
            return Optional.ofNullable(page).orElse(null);
        };
        List<AgentPaymentVerificationVO> contentList = PageIterator.iteratePageToList(300, call, true, new ForkJoinPool(50));
        ExcelUtil<AgentPaymentVerificationExportVO> excelUtil = new ExcelUtil<>(AgentPaymentVerificationExportVO.class);
        List<AgentPaymentVerificationExportVO> exportVOS = new ArrayList<>();
        contentList.forEach(agentPaymentVerificationVO -> {
            if (CollectionUtils.isNotEmpty(agentPaymentVerificationVO.getDetails())){
                agentPaymentVerificationVO.getDetails().forEach(agentVerificationDetailVO -> {
                    AgentPaymentVerificationExportVO agentPaymentVerificationExportVO = HyperBeanUtils.copyPropertiesByJackson(agentPaymentVerificationVO, AgentPaymentVerificationExportVO.class);
                    agentPaymentVerificationExportVO.setStatus(agentVerificationDetailVO.getStatus());
                    agentPaymentVerificationExportVO.setWriteOffPrice(agentVerificationDetailVO.getWriteOffPrice());
                    agentPaymentVerificationExportVO.setDeptAmount(agentVerificationDetailVO.getDeptAmount());
                    agentPaymentVerificationExportVO.setDetailRemark(agentVerificationDetailVO.getDetailRemark());
                    agentPaymentVerificationExportVO.setSignCompany(agentVerificationDetailVO.getPayerAgentName());
                    agentPaymentVerificationExportVO.setPerformanceReportNumber(agentVerificationDetailVO.getPerformanceReportNumber());
                    agentPaymentVerificationExportVO.setVerificationTypeCn(agentVerificationDetailVO.getVerificationTypeCn());
                    exportVOS.add(agentPaymentVerificationExportVO);
                });
            }else {
                AgentPaymentVerificationExportVO agentPaymentVerificationExportVO = HyperBeanUtils.copyPropertiesByJackson(agentPaymentVerificationVO, AgentPaymentVerificationExportVO.class);
                exportVOS.add(agentPaymentVerificationExportVO);
            }
        });
        excelUtil.exportExcel(response, exportVOS, "渠道回款核销");
    }


    @GetMapping("/queryInfoById")
    @Operation(summary = "渠道回款核销详情页——付款信息")
    @PreAuthorize(hasPermission = "crm_agent_repayment_verify",dataScope = "crm_agent_repayment_verify")
    public JsonObject<AgentPaymentVerificationVO> queryInfoById(@RequestParam String id) {
        return new JsonObject<>(agentAuthPaymentDisburseService.queryInfoById(id));
    }

    @GetMapping("/queryVetrificationDetailsById")
    @Operation(summary = "渠道回款核销详情页——核销明细", parameters = {@Parameter(name = "id", description = "核销明细id", example = "16153ea6c322121134e729a3f80d5adc")})
    @PreAuthorize(hasPermission = "crm_agent_repayment_verify",dataScope = "crm_agent_repayment_verify")
    public JsonObject<PaymentVerificationDetailVO> queryVetrificationDetailsById(@RequestParam String id,@RequestParam(required = false)String type) {
        return new JsonObject<>(agentAuthPaymentDisburseService.queryVetrificationDetailsById(id,type));
    }

    @PostMapping("/reversal")
    @Operation(summary = "渠道回款核销情页——核销明细——回冲")
    @PreAuthorize(hasPermission = "crm_agent_repayment_verify_update",dataScope = "crm_agent_repayment_verify")
    public JsonObject<Boolean> reversal(@RequestBody @Valid ReversalDetailVO reversalDetailVO) {
        agentAuthPaymentDisburseService.reversal(reversalDetailVO);
        return new JsonObject<>(true);
    }

    @GetMapping("/queryDetailInfo")
    @Operation(summary = "渠道回款核销详情页——核销明细——修改（跳转查询）", parameters = {@Parameter(name = "id", description = "核销明细id", example = "16153ea6c322121134e729a3f80d5adc")})
    @PreAuthorize(hasPermission = "crm_agent_repayment_verify",dataScope = "crm_agent_repayment_verify")
    public JsonObject<DetailInfoVO> queryDetailInfo(@RequestParam String id) {
        return new JsonObject<>(agentAuthPaymentDisburseService.queryDetailInfo(id));
    }

    @PostMapping("/pagePerformanceReport")
    @Operation(summary = "渠道回款核销详情页——核销明细——新增核销")
    @PreAuthorize(hasPermission = "crm_agent_repayment_verify",dataScope="crm_agent_repayment_verify")
    public JsonObject<PageUtils<PerformanceExecuteVO>> pagePerformanceReport(@RequestBody PerformanceExecuteQuery query) {
        return new JsonObject<>(agentAuthPaymentDisburseService.pagePerformanceReport(query));
    }

    @PostMapping("/updateDetailInfo")
    @Operation(summary = "渠道回款核销详情页——核销明细——修改确认提交")
    @PreAuthorize(hasPermission = "crm_agent_repayment_verify_update",dataScope = "crm_agent_repayment_verify")
    public JsonObject<Boolean> updateDetailInfo(@RequestBody @Valid SubmitDetailDTO submitDetailDTO) {
        agentAuthPaymentDisburseService.updateDetailInfo(submitDetailDTO);
        return new JsonObject<>(true);
    }

    @PostMapping("/applyAdvance")
    @Operation(summary = "渠道回款核销详情页—核销预付款——确认提交")
    @PreAuthorize(hasPermission = "crm_agent_repayment_verify_update",dataScope = "crm_agent_repayment_verify")
    public JsonObject<Boolean> applyAdvance(@RequestBody @Valid SubmitDetailDTO submitDetailDTO) {
        agentAuthPaymentDisburseService.applyAdvance(submitDetailDTO);
        return new JsonObject<>(true);
    }

    @GetMapping("/deleteDetailInfo")
    @Operation(summary = "渠道回款核销详情页——核销明细——删除", parameters = {@Parameter(name = "id", description = "核销明细id", example = "16153ea6c322121134e729a3f80d5adc")})
    @PreAuthorize(hasPermission = "crm_agent_repayment_verify_update",dataScope = "crm_agent_repayment_verify")
    public JsonObject<Boolean> deleteDetailInfo(@RequestParam String id) {
        agentAuthPaymentDisburseService.deleteDetailInfo(id);
        return new JsonObject<>(true);
    }


    @GetMapping("/queryVerificationProcessList")
    @Operation(summary = "获取渠道回款核销页面流程列表",
            parameters = {@Parameter(name = "agentPaymentId", description = "回款核销ID", example = "16153ea6c322121134e729a3f80d5adc")})
    @PreAuthorize(hasPermission = "crm_agent_repayment_verify",dataScope = "crm_agent_repayment_verify")
    public JsonObject<List<AgentProcessVO>> queryVerificationProcessList(@RequestParam String verificationId) {
        return new JsonObject<>(agentAuthPaymentDisburseService.queryVerificationProcessList(verificationId));
    }

    @PreAuthorize(hasPermission = "crm_agent_repayment_verify_update",dataScope = "crm_agent_repayment_verify")
    @PostMapping("/saveAttachment")
    @Operation(summary = "渠道回款核销详情——上传收款回单")
    public JsonObject<Boolean> saveAttachment(@RequestBody @Validated AttachmentInfos attachmentInfos) {
        agentPaymentAttachmentService.saveAttachment(attachmentInfos);
        return new JsonObject<>(true);
    }

    @PreAuthorize(hasPermission = "crm_agent_repayment_verify_update",dataScope = "crm_agent_repayment_verify")
    @PostMapping("/deleteAttachment")
    @Operation(summary = "渠道回款核销详情——收款回单删除")
    public JsonObject<Boolean> deleteAttachment(@RequestParam String id) {
        agentPaymentAttachmentService.deleteAttachment(id);
        return new JsonObject<>(true);
    }

    @PostMapping("/modifyCarNumber")
    @Operation(summary = "渠道回款核销详情——修改进账单号")
    @PreAuthorize(hasPermission = "crm_agent_repayment_verify_update_account_number",dataScope = "crm_agent_repayment_verify")
    public JsonObject<Boolean> modifyCarNumber(@RequestBody @Validated ModifyCarNumberDTO modifyCarNumberDTO) {
        agentAuthPaymentDisburseService.modifyCarNumber(modifyCarNumberDTO);
        return new JsonObject<>(true);
    }

    @GetMapping("/queryAttachmentInfoById")
    @Operation(summary = "渠道回款核销详情页——收款回单", parameters = {@Parameter(name = "id", description = "核销明细id", example = "16153ea6c322121134e729a3f80d5adc")})
    @PreAuthorize(hasPermission = "crm_agent_repayment_verify",dataScope = "crm_agent_repayment_verify")
    public JsonObject<List<AgentPaymentAttachmentVO>> queryAttachmentInfoById(@RequestParam String id) {
        return new JsonObject<>(agentPaymentAttachmentService.queryAttachmentInfoById(id));
    }
}

