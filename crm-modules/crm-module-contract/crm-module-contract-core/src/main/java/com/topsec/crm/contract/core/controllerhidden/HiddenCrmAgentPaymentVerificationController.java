package com.topsec.crm.contract.core.controllerhidden;

import com.topsec.crm.contract.api.entity.crmagentpayment.CrmAgentPaymentVerificationDTO;
import com.topsec.crm.contract.api.entity.crmagentpayment.agentpaymentverification.*;
import com.topsec.crm.contract.core.service.CrmAgentPaymentVerificationDetailService;
import com.topsec.crm.contract.core.service.CrmAgentPaymentVerificationService;
import com.topsec.crm.flow.api.dto.performancereport.PerformanceReportPaymentInfoDTO;
import com.topsec.crm.framework.common.bean.AgentPrePaymentVerificationVO;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 回款核销接口
 */
@RestController
@RequestMapping("/hidden/crmAgentPaymentVerification")
@Tag(name = "渠道回款核销")
@RequiredArgsConstructor
@Slf4j

public class HiddenCrmAgentPaymentVerificationController {

    private final CrmAgentPaymentVerificationService crmAgentPaymentVerificationService;
    private final CrmAgentPaymentVerificationDetailService crmAgentPaymentVerificationDetailService;


    @Operation(summary = "获取业绩上报对应欠款金额")
    @PostMapping("/getDebtByNumbers")
    public JsonObject<Map<String, BigDecimal>> getDebtByNumbers(@RequestBody(required = false) List<String> numbers) {
        return new JsonObject<>(crmAgentPaymentVerificationDetailService.getDebtByNumbers(numbers));
    }

    @Operation(summary = "付款申请办结-回款核销-保存")
    @PostMapping("/savePaymentVerificationByPayment")
    public JsonObject<Boolean> savePaymentVerificationByPayment(@RequestBody CrmAgentPaymentVerificationDTO verificationDTO) {
        if(!crmAgentPaymentVerificationService.isCanSaveByPayment(verificationDTO)) return new JsonObject<>(false);
        return new JsonObject<>(crmAgentPaymentVerificationService.savePaymentVerificationByPayment(verificationDTO));
    }

    @Operation(summary = "生效业绩上报（写入最终表后调用）-付款信息-回款核销-保存")
    @PostMapping("/savePaymentVerificationByPerformance")
    public JsonObject<Boolean> savePaymentVerificationByPerformance(@RequestBody List<PerformanceReportPaymentInfoDTO> infoDTOList) {
        return new JsonObject<>(crmAgentPaymentVerificationService.savePaymentVerificationByPerformance(infoDTOList));
    }

    @Operation(summary = "生效业绩上报（写入最终表后调用）-预付款抵货款-回款核销-保存")
    @PostMapping("/savePaymentVerificationByAdvanceUsed")
    public JsonObject<Boolean> savePaymentVerificationByAdvanceUsed(@RequestBody List<PerformancePreInfoVO> performancePreInfoVOS) {
        return new JsonObject<>(crmAgentPaymentVerificationService.savePaymentVerificationByAdvanceUsed(performancePreInfoVOS));
    }


    @GetMapping("/queryPreByPayerAgentId")
    @Operation(summary = "生效业绩上报-使用预付款-查询")
    public JsonObject<List<PerformancePreInfoVO>> queryPreByPayerAgentId(@RequestParam String payerAgentId){
        return new JsonObject<>(crmAgentPaymentVerificationDetailService.queryPreByPayerAgentId(payerAgentId));
    }

    @GetMapping("/queryPreByPayerAgent")
    @Operation(summary = "生效业绩上报-使用预付款-根据渠道商查询")
    public JsonObject<List<PerformancePreInfoVO>> queryPreByPayerAgent(@RequestParam String payerAgentName,@RequestParam String receivedAgentName){
        return new JsonObject<>(crmAgentPaymentVerificationDetailService.queryPreByPayerAgent(payerAgentName,receivedAgentName));
    }

    @PostMapping("/verificationPrePage")
    @Operation(summary = "回款核销预付款分页列表")
    public JsonObject<PageUtils<AgentPrePaymentVerificationVO>> verificationPrePage(@RequestBody AgentPaymentHiddenPageQuery query){
        return new JsonObject<>(crmAgentPaymentVerificationService.verificationPrePage(query));
    }

    @PostMapping("/verificationPage")
    @Operation(summary = "回款核销分页列表")
    public JsonObject<PageUtils<AgentPaymentVerificationVO>> verificationHiddenPage(@RequestBody AgentPaymentHiddenPageQuery query){
        return new JsonObject<>(crmAgentPaymentVerificationService.verificationHiddenPage(query));
    }

    @GetMapping("/verificationCount")
    @Operation(summary = "回款核销分页列表(查询自定渠道商的带有统计的接口)")
    public JsonObject<PrepaymentSummaryVO> verificationCount(@RequestParam String payerAgentName,@RequestParam String receivedAgentName){
        return new JsonObject<>(crmAgentPaymentVerificationService.verificationCount(payerAgentName,receivedAgentName));
    }

    @PostMapping("/batchVerificationCount")
    @Operation(summary = "回款核销分页列表(查询自定渠道商的带有统计的接口)批量")
    public JsonObject<List<PrepaymentSummaryVO>> batchVerificationCount(@RequestBody List<Map<String,String>> params){
        return new JsonObject<>(crmAgentPaymentVerificationService.batchVerificationCount(params));
    }

    @PostMapping("/savePreDetailInfo")
    @Operation(summary = "预付款流程办结完——回写核销数据")
    public JsonObject<Boolean> savePreDetailInfo(@RequestBody SubmitPreDetailDTO submitDetailDTO){
        crmAgentPaymentVerificationService.savePreDetailInfo(submitDetailDTO);
        return new JsonObject<>(true);
    }

    @PostMapping("/addVerification")
    @Operation(summary = "渠道付款流程办结完——回写核销数据")
    public JsonObject<String> addVerification(@RequestBody AgentPaymentProcessSubmitDTO submitDetailDTO){
        return new JsonObject<>(crmAgentPaymentVerificationService.addVerification(submitDetailDTO));
    }

    @PostMapping("/executFreeze")
    @Operation(summary = "每天执行清洗冻结定时器")
    public JsonObject<Void> executFreeze(@RequestBody Map<String,Object> param){
        crmAgentPaymentVerificationDetailService.executFreeze(param);
        return new JsonObject<>();
    }

    @GetMapping("/queryInfoByProcessInstanceId")
    @Operation(summary = "生效业绩上报-根据业绩上报流程id查询明细")
    public JsonObject<List<PerformanceInfoVO>> queryInfoByProcessInstanceId(@RequestParam String processInstanceId){
        return new JsonObject<>(crmAgentPaymentVerificationDetailService.queryInfoByProcessInstanceId(processInstanceId));
    }

    @GetMapping("/queryPrePaymentVerificationList")
    @Operation(summary = "查询回款核销预付款信息")
    public JsonObject<List<AgentPrePaymentVerificationVO>> queryPrePaymentVerificationList(@RequestParam String payerAgentId, @RequestParam String receivedAgentId){
        return new JsonObject<>(crmAgentPaymentVerificationService.queryPrePaymentVerificationList(payerAgentId, receivedAgentId));
    }

}
