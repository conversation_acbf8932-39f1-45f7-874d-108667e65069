package com.topsec.crm.contract.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.topsec.crm.contract.core.service.ICrmContractForecastService;
import com.topsec.crm.contract.core.entity.CrmContractForecast;
import com.topsec.crm.contract.core.mapper.CrmContractForecastMapper;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.Query;
import com.topsec.crm.framework.common.util.date.DateUtil;
import com.topsec.crm.framework.common.web.page.CrmPageQuery;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 月新增合同预测设置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@Service
public class CrmContractForecastServiceImpl extends ServiceImpl<CrmContractForecastMapper, CrmContractForecast> implements ICrmContractForecastService {

    @Resource
    private ICrmContractForecastService crmContractForecastService;

    /**
     * 分页查询月新增合同预测设置
     *
     * @param crmPageQuery
     * @return
     */
    @Override
    public PageUtils<CrmContractForecast> selectCrmContractForecastPageByParams(CrmPageQuery crmPageQuery) {
        LambdaQueryWrapper<CrmContractForecast> queryWrapper = new LambdaQueryWrapper<CrmContractForecast>()
                .orderByDesc(CrmContractForecast::getCreateTime, CrmContractForecast::getId);
        Page<CrmContractForecast> page = crmContractForecastService.page(new Query<CrmContractForecast>().getPage(crmPageQuery), queryWrapper);
        return new PageUtils<>(page);
    }

    /**
     * 修改月新增合同预测设置
     *
     * @param crmContractForecast
     * @return
     */
    @Override
    public Boolean updateCrmContractForecast(CrmContractForecast crmContractForecast) {
        return crmContractForecastService.updateById(crmContractForecast);
    }

    /**
     * 创建合同预测计划
     * 当月生成下个月的计划
     * @return
     */
    @Override
    public Boolean createContractForecastPlan() {
        LocalDate target = LocalDate.now().plusMonths(1).withDayOfMonth(1);
        String str = DateUtil.localDateToStr(target);
        String month = str.substring(0, 7);
        long count = crmContractForecastService.count(new LambdaQueryWrapper<CrmContractForecast>().eq(CrmContractForecast::getMonth, month));
        if(count == 0){
            CrmContractForecast build = CrmContractForecast.builder().month(month).beginDate(target).autoFinishOne(target.plusDays(6)).autoFinishTwo(target.plusDays(10)).build();
            return crmContractForecastService.save(build);
        }else{
            return false;
        }
    }

    /**
     * 是否显示合同预测未办结排名
     *
     * @return
     */
    @Override
    public Boolean showContractForecastRank() {
        LocalDate now = LocalDate.now();
        long count = crmContractForecastService.count(new LambdaQueryWrapper<CrmContractForecast>().le(CrmContractForecast::getBeginDate, now).ge(CrmContractForecast::getAutoFinishTwo, now));
        return count > 0;
    }

    /**
     * 获取当前期数
     *
     * @return
     */
    @Override
    public String getCurrentMonth() {
        LocalDate now = LocalDate.now();
        CrmContractForecast one = crmContractForecastService.getOne(new LambdaQueryWrapper<CrmContractForecast>().le(CrmContractForecast::getBeginDate, now).ge(CrmContractForecast::getAutoFinishTwo, now));
        return one.getMonth();
    }

    /**
     * 获取01步自动结束的期数
     */
    @Override
    public List<String> getAutoFinishMonth01() {
        LocalDate now = LocalDate.now();
        List<CrmContractForecast> list = crmContractForecastService.list(new LambdaQueryWrapper<CrmContractForecast>().ge(CrmContractForecast::getAutoFinishOne, now).orderByDesc(CrmContractForecast::getMonth).last("limit 3"));
        if(CollectionUtils.isNotEmpty(list)){
            return list.stream().map(CrmContractForecast::getMonth).toList();
        }else{
            return List.of();
        }
    }

    /**
     * 获取02步自动结束的期数
     */
    @Override
    public List<String> getAutoFinishMonth02() {
        LocalDate now = LocalDate.now();
        List<CrmContractForecast> list = crmContractForecastService.list(new LambdaQueryWrapper<CrmContractForecast>().ge(CrmContractForecast::getAutoFinishTwo, now).orderByDesc(CrmContractForecast::getMonth).last("limit 3"));
        if(CollectionUtils.isNotEmpty(list)){
            return list.stream().map(CrmContractForecast::getMonth).toList();
        }else{
            return List.of();
        }
    }
}
