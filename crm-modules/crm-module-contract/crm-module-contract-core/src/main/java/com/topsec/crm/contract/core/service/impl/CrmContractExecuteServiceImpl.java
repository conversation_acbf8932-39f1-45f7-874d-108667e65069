package com.topsec.crm.contract.core.service.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.topsec.crm.agent.api.RemoteAgentService;
import com.topsec.crm.agent.api.entity.CrmAgentVo;
import com.topsec.crm.contract.api.entity.CrmContractBaseInfoVO;
import com.topsec.crm.contract.api.entity.contractexecute.CrmContractExecuteUpdateVO;
import com.topsec.crm.contract.api.entity.contractexecute.CrmContractExecuteVO;
import com.topsec.crm.contract.api.entity.request.CrmContractAfterQuery;
import com.topsec.crm.contract.api.entity.request.CrmContractExecuteQuery;
import com.topsec.crm.contract.api.entity.request.FiledConvertHandler;
import com.topsec.crm.contract.core.entity.CrmContractExecute;
import com.topsec.crm.contract.core.mapper.CrmContractExecuteMapper;
import com.topsec.crm.contract.core.service.*;
import com.topsec.crm.contract.core.util.CrmContractExecuteUtil;
import com.topsec.crm.flow.api.*;
import com.topsec.crm.flow.api.dto.contractreview.statistics.ContractExecuteStatisticsVO;
import com.topsec.crm.flow.api.vo.handoverProcess.HandoverContractVo;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.bean.HandoverProcessQuery;
import com.topsec.crm.framework.common.bean.StatsDeptTimeSearchVO;
import com.topsec.crm.framework.common.bean.StatsPersonTimeSearchVO;
import com.topsec.crm.framework.common.util.CommonUtils;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.util.sql.SqlUtil;
import com.topsec.crm.framework.security.config.AuthorizeContextHolder;
import com.topsec.crm.framework.security.utils.AuthorizeUtil;
import com.topsec.crm.operation.api.RemoteContractReviewConfigService;
import com.topsec.crm.operation.api.entity.ContractReviewConfig.ContractTypeVO;
import com.topsec.crm.project.api.RemoteProjectDynastyService;
import com.topsec.crm.project.api.client.RemoteProjectDirectlyClient;
import com.topsec.crm.project.api.entity.CrmProjectDirectlyPriceStatisticsVO;
import com.topsec.crm.project.api.entity.CrmProjectDirectlyVo;
import com.topsec.crm.project.api.entity.CrmProjectDynastyVo;
import com.topsec.crm.project.api.entity.PriceStatisticsVO;
import com.topsec.crm.stats.api.entity.StatsAgentSaleVO;
import com.topsec.crm.stats.api.entity.StatsBusinessDeptVO;
import com.topsec.crm.stats.api.entity.StatsProjectVO;
import com.topsec.crm.stats.api.entity.StatsYearCompareDeptVO;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.tbsapi.client.TbsAccountClient;
import com.topsec.tbsapi.client.TbsPersonClient;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tbscommon.vo.DepartmentVO;
import com.topsec.tbscommon.vo.PersonVO;
import com.topsec.tos.api.client.TosDepartmentClient;
import com.topsec.tos.common.HyperBeanUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 *
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CrmContractExecuteServiceImpl extends ServiceImpl<CrmContractExecuteMapper, CrmContractExecute>
        implements CrmContractExecuteService {

    private final RemoteProjectDirectlyClient remoteProjectDirectlyClient;
    private final CrmContractReviewMainService mainService;
    private final RemoteProjectDynastyService remoteProjectDynastyService;
    private final RemoteContractReviewConfigService remoteContractReviewConfigService;
    private final CrmContractReviewPaymentProvisionService crmContractReviewPaymentProvisionService;
    private final RedissonClient redissonClient;
    private final RemoteContractReviewConfigService contractReviewConfigService;
    private final RemoteAgentService remoteAgentService;
    private final AuthorizeUtil authorizeUtil;
    private final JdbcTemplate jdbcTemplate;
    private final RedisTemplate<String, String> redisTemplate;
    private final TbsPersonClient tbsPersonClient;
    private final RemoteContractCollectionLetterService remoteContractCollectionLetterService;
    private final RemoteContractReviewFlowService remoteContractReviewFlowService;
    private final RemoteFlowReceivableService remoteFlowReceivableService;
    private final RemoteContractReturnExchangeService remoteContractReturnExchangeService;
    private final RemoteContractOriginalExpeditingService remoteContractOriginalExpeditingService;

    @Override
    public CrmContractExecuteVO getByContractNumber(String contractNumber) {
        CrmContractExecuteVO crmContractExecuteVO = HyperBeanUtils.copyProperties(getOne(new LambdaQueryWrapper<CrmContractExecute>()
                .eq(CrmContractExecute::getContractNumber, contractNumber)
                .eq(CrmContractExecute::getDelFlag, false)
                .last("limit 1")), CrmContractExecuteVO::new);
        if (crmContractExecuteVO != null) {
            initContractTypeName(Collections.singletonList(crmContractExecuteVO));
        }
        return crmContractExecuteVO;
    }

    @Override
    public List<CrmContractExecuteVO> getByContractNumberBatch(Set<String> contractNumbers) {
        if (CollectionUtils.isEmpty(contractNumbers)) {
            return Collections.emptyList();
        }

        List<CrmContractExecuteVO> executeVOS = HyperBeanUtils.copyListProperties(list(new LambdaQueryWrapper<CrmContractExecute>()
                .in(CrmContractExecute::getContractNumber, contractNumbers)
                .eq(CrmContractExecute::getDelFlag, false)), CrmContractExecuteVO::new);
        if (CollectionUtils.isNotEmpty(executeVOS)) {
            initContractTypeName(executeVOS);
        }
        return executeVOS;
    }

    @Override
    public List<CrmContractExecuteVO> getByContractNumberBatchNotEffective(Set<String> contractNumbers) {
        if (CollectionUtils.isEmpty(contractNumbers)) {
            return Collections.emptyList();
        }

        List<CrmContractExecuteVO> executeVOS = HyperBeanUtils.copyListProperties(list(new LambdaQueryWrapper<CrmContractExecute>()
                .in(CrmContractExecute::getContractNumber, contractNumbers)), CrmContractExecuteVO::new);
        if (CollectionUtils.isNotEmpty(executeVOS)) {
            initContractTypeName(executeVOS);
        }
        return executeVOS;
    }

    @Override
    public PageUtils<CrmContractExecuteVO> pageDirectly(CrmContractExecuteQuery query) {
        DataScopeParam dataScopeParam = AuthorizeContextHolder.getDataScopeParam();
        Set<String> personIdList = dataScopeParam.getPersonIdList();

        // 后续可以优化使用pageByCondition方法
        LambdaQueryWrapper<CrmContractExecute> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CrmContractExecute::getDelFlag, false)
                .like(StringUtils.isNotEmpty(query.getContractNumber()), CrmContractExecute::getContractNumber, query.getContractNumber())
                .like(StringUtils.isNotEmpty(query.getContractCompanyName()), CrmContractExecute::getContractCompanyName, query.getContractCompanyName())
                .like(StringUtils.isNotEmpty(query.getFinalCustomerName()), CrmContractExecute::getFinalCustomerName, query.getFinalCustomerName())
                .eq(query.getContractExecuteStatus() != null, CrmContractExecute::getContractExecuteStatus, query.getContractExecuteStatus())
                .eq(StringUtils.isNotEmpty(query.getContractOwnerId()), CrmContractExecute::getContractOwnerId, query.getContractOwnerId())
                .eq(StringUtils.isNotEmpty(query.getSaleDeptId()), CrmContractExecute::getContractOwnerDeptId, query.getSaleDeptId())
                .between(query.getContractTimeStart() != null && query.getContractTimeEnd() != null, CrmContractExecute::getContractTime, query.getContractTimeStart(), query.getContractTimeEnd())
                .in(CollectionUtils.isNotEmpty(personIdList), CrmContractExecute::getContractOwnerId, personIdList)
                .eq(CrmContractExecute::getProjectSource, 1)
                .orderByDesc(CrmContractExecute::getContractTime);

        // 收款情况
        if (query.getReturnedStatus() != null) {
            switch (query.getReturnedStatus()) {
                case 1:
                    wrapper.apply("returned_amount = contract_amount");
                    break;
                case 0:
                    wrapper.apply("returned_amount < contract_amount");
                    break;
            }
        }
        // 开票情况
        if (query.getInvoiceStatus() != null) {
            switch (query.getInvoiceStatus()) {
                case 1:
                    wrapper.apply("invoiced_amount = contract_amount");
                    break;
                case 2:
                    // 如果是合同金额等于0 视为已开票 所以未全开这个地方要筛选不等于0
                    wrapper.apply("(invoiced_amount < contract_amount and contract_amount <> 0)");
                    break;
            }
        }
        List<CrmContractExecute> list = list(wrapper);
        PageUtils<CrmContractExecuteVO> pageUtils = new PageUtils<>();
        pageUtils.setTotalCount((int) new PageInfo<>(list).getTotal());
        List<CrmContractExecuteVO> crmContractExecuteVOS = HyperBeanUtils.copyListProperties(list, CrmContractExecuteVO::new);
        if (CollectionUtils.isEmpty(crmContractExecuteVOS)) {
            pageUtils.setList(crmContractExecuteVOS);
            return pageUtils;
        }
        initContractExecute(crmContractExecuteVOS, 1);
        pageUtils.setList(crmContractExecuteVOS);
        return pageUtils;
    }

    private void initContractExecute(List<CrmContractExecuteVO> crmContractExecuteVOS, Integer projectSource) {
        // 赋值
        List<String> contractIds = crmContractExecuteVOS.stream().map(CrmContractExecuteVO::getContractId).toList();
        List<CrmContractBaseInfoVO> baseInfoVOS = mainService.getByContractIdBatch(contractIds);
        Map<String, CrmContractBaseInfoVO> baseInfoVOMap = baseInfoVOS.stream().collect(Collectors.toMap(CrmContractBaseInfoVO::getId, v -> v, (v1, v2) -> v1));
        Set<String> contractTypes = baseInfoVOS.stream().map(CrmContractBaseInfoVO::getContractType).collect(Collectors.toSet());
        List<ContractTypeVO> contractTypeVOS = contractReviewConfigService.getByContractTypeIds(contractTypes).getObjEntity();
        Map<String, ContractTypeVO> contractTypeVOMap = contractTypeVOS.stream().collect(Collectors.toMap(ContractTypeVO::getId, v -> v, (v1, v2) -> v1));
        Map<String, CrmProjectDirectlyVo> projectDirectlyVoMap;
        Map<String, CrmProjectDynastyVo> projectDynastyVoMap;
        if (projectSource == 1) {
            try {
                List<CrmProjectDirectlyVo> projectDirectlyVos = remoteProjectDirectlyClient.listSimpleDataByIds(baseInfoVOS.stream().map(CrmContractBaseInfoVO::getProjectId).toList()).getObjEntity();
                projectDirectlyVoMap = projectDirectlyVos.stream().collect(Collectors.toMap(CrmProjectDirectlyVo::getId, v -> v, (v1, v2) -> v1));
            } catch (Exception e) {
                // 垃圾数据查项目查不出来 不处理
                log.warn("查询项目失败", e);
                projectDirectlyVoMap = new HashMap<>();
            }
        } else {
            projectDirectlyVoMap = new HashMap<>();
        }
        if (projectSource == 3) {
            try {
                List<CrmProjectDynastyVo> projectDynastyVos = remoteProjectDynastyService.batchGetDynastyInfo(baseInfoVOS.stream().map(CrmContractBaseInfoVO::getProjectId).toList()).getObjEntity();
                projectDynastyVoMap = projectDynastyVos.stream().collect(Collectors.toMap(CrmProjectDynastyVo::getId, v -> v, (v1, v2) -> v1));
            } catch (Exception e) {
                // 垃圾数据查项目查不出来 不处理
                log.warn("查询项目失败", e);
                projectDynastyVoMap = new HashMap<>();
            }
        } else {
            projectDynastyVoMap = new HashMap<>();
        }

        Map<String, CrmProjectDirectlyVo> finalProjectDirectlyVoMap = projectDirectlyVoMap;
        Map<String, CrmProjectDynastyVo> finalProjectDynastyVoMap = projectDynastyVoMap;
        ListUtils.emptyIfNull(crmContractExecuteVOS).forEach(item -> {
            CrmContractBaseInfoVO baseInfoVO = baseInfoVOMap.get(item.getContractId());
            if (baseInfoVO == null) {
                return;
            }
            ContractTypeVO contractTypeVO = contractTypeVOMap.get(baseInfoVO.getContractType());
            if (contractTypeVO != null) {
                item.setContractTypeName(contractTypeVO.getType());
            }
            if (projectSource == 1) {
                // 公司项目
                CrmProjectDirectlyVo crmProjectDirectlyVo = finalProjectDirectlyVoMap.get(baseInfoVO.getProjectId());
                if (crmProjectDirectlyVo == null) {
                    return;
                }
                item.setProjectName(crmProjectDirectlyVo.getProjectName());
            }
            if (projectSource == 3) {
                //  Dynasty 项目
                CrmProjectDynastyVo crmProjectDynastyVo = finalProjectDynastyVoMap.get(baseInfoVO.getProjectId());
                if (crmProjectDynastyVo == null) {
                    return;
                }
                item.setProjectName(crmProjectDynastyVo.getProjectName());
            }
        });
    }

    @Override
    public PageUtils<CrmContractExecuteVO> pageDynasty(CrmContractExecuteQuery query) {
        DataScopeParam dataScopeParam = AuthorizeContextHolder.getDataScopeParam();
        Set<String> personIdList = dataScopeParam.getPersonIdList();
        // 后续可以优化使用pageByCondition方法
        LambdaQueryWrapper<CrmContractExecute> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CrmContractExecute::getDelFlag, false)
                .like(StringUtils.isNotEmpty(query.getContractNumber()), CrmContractExecute::getContractNumber, query.getContractNumber())
                .like(StringUtils.isNotEmpty(query.getContractCompanyName()), CrmContractExecute::getContractCompanyName, query.getContractCompanyName())
                .like(StringUtils.isNotEmpty(query.getFinalCustomerName()), CrmContractExecute::getFinalCustomerName, query.getFinalCustomerName())
                .in(CollectionUtils.isNotEmpty(personIdList), CrmContractExecute::getContractOwnerId, personIdList)
                .eq(CrmContractExecute::getProjectSource, 3)
                .orderByDesc(CrmContractExecute::getContractTime);
        // 判断登录人是否是渠道商
        String currentAgentId = UserInfoHolder.getCurrentAgentId();
        if (StringUtils.isNotEmpty(currentAgentId)) {
            CrmAgentVo agentInfo = remoteAgentService.getAgentInfo(currentAgentId).getObjEntity();
            String agentName = agentInfo.getAgentName();
            if (StringUtils.isNotEmpty(agentName)) {
                // 签约单位或最终用户是该渠道商
                wrapper.and(w -> w.eq(CrmContractExecute::getContractCompanyName, agentName)
                        .or()
                        .eq(CrmContractExecute::getFinalCustomerName, agentName));
            }
        }
        List<CrmContractExecute> list = list(wrapper);
        PageUtils<CrmContractExecuteVO> pageUtils = new PageUtils<>();
        pageUtils.setTotalCount((int) new PageInfo<>(list).getTotal());
        List<CrmContractExecuteVO> crmContractExecuteVOS = HyperBeanUtils.copyListProperties(list, CrmContractExecuteVO::new);
        if (CollectionUtils.isEmpty(crmContractExecuteVOS)) {
            pageUtils.setList(crmContractExecuteVOS);
            return pageUtils;
        }
        initContractExecute(crmContractExecuteVOS, 3);
        pageUtils.setList(crmContractExecuteVOS);
        return pageUtils;
    }

    @Override
    public PageUtils<CrmContractExecuteVO> pageByCondition(CrmContractAfterQuery query) {
        QueryWrapper<CrmContractExecute> wrapper = new QueryWrapper<>();
        try {
            wrapper.eq("del_flag", false);
            buildWrapper(wrapper, query);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        PageUtils<CrmContractExecuteVO> pageUtils = new PageUtils<>();

        // 判断是否有rpc的过滤 此逻辑避免in 条件过多
        Set<String> rpcContractNumbers = query.getContractNumbers();
        Set<String> rpcContractNumbersNotIn = query.getContractNumbersNotIn();
        if (CollectionUtils.isNotEmpty(rpcContractNumbers) || CollectionUtils.isNotEmpty(rpcContractNumbersNotIn)) {
            // 改成批量取
            return handleRpcQuery(rpcContractNumbersNotIn,rpcContractNumbers, wrapper, query, pageUtils);
        }
        // 无rpc的普通查询 pageNum 和 pageSize为空 则查所有
        if (StringUtils.isNotNull(query.getPageNum()) && StringUtils.isNotNull(query.getPageSize())) {
            String orderBy = SqlUtil.escapeOrderBySql(query.getOrderBy());
            PageHelper.startPage(query.getPageNum(), query.getPageSize(), orderBy);
        }
        // 默认条件 过滤垃圾数据的
        wrapper.and(w1 -> {
            w1.eq("project_source", 1).or().eq("project_source", 3);
        });
        // 支持排序
        if (StringUtils.isNotEmpty(query.getOrderByColumn())) {
            if ("asc".equals(query.getIsAsc())) {
                wrapper.orderByAsc(query.getOrderByColumn());
            } else {
                wrapper.orderByDesc(query.getOrderByColumn());
            }
        }
        List<CrmContractExecute> list = list(wrapper);
        pageUtils.setTotalCount((int) new PageInfo<>(list).getTotal());
        pageUtils.setList(HyperBeanUtils.copyListProperties(list, CrmContractExecuteVO::new));
        // 判断是否要拼接合同类型
        if (query.getNeedContractType() != null && query.getNeedContractType()) {
            initContractTypeName(pageUtils.getList());
        }
        return pageUtils;
    }

    private PageUtils<CrmContractExecuteVO> handleRpcQuery(Set<String> rpcContractNumbersNotIn, Set<String> rpcContractNumbers, QueryWrapper<CrmContractExecute> wrapper, CrmContractAfterQuery query, PageUtils<CrmContractExecuteVO> pageUtils){
        final int BATCH_SIZE = 1000; // 每批处理量
        List<CrmContractExecute> list;
        Set<String> notInSet = CollectionUtils.isNotEmpty(rpcContractNumbersNotIn)
                ? new HashSet<>(rpcContractNumbersNotIn) : Collections.emptySet();

        // 情况1：包含集合>1000时，分批查询
        if (CollectionUtils.isNotEmpty(rpcContractNumbers) && rpcContractNumbers.size() > BATCH_SIZE) {
            list = new ArrayList<>();
            List<String> contractList = new ArrayList<>(rpcContractNumbers);
            int total = contractList.size();

            // 分批查询
            for (int i = 0; i < total; i += BATCH_SIZE) {
                int end = Math.min(i + BATCH_SIZE, total);
                List<String> batchList = contractList.subList(i, end);

                QueryWrapper<CrmContractExecute> batchWrapper = wrapper.clone();
                batchWrapper.in("contract_number", batchList);

                list.addAll(list(batchWrapper));
            }
        }
        // 情况2：不包含集合>1000时，全量查询+内存过滤
        else if (CollectionUtils.isNotEmpty(rpcContractNumbersNotIn) && rpcContractNumbersNotIn.size() > BATCH_SIZE) {
            list = list(wrapper);
        }
        // 情况3：集合量小时，直接SQL过滤
        else {
            if (CollectionUtils.isNotEmpty(rpcContractNumbers)) {
                wrapper.in("contract_number", rpcContractNumbers);
            }
            if (CollectionUtils.isNotEmpty(rpcContractNumbersNotIn)) {
                wrapper.notIn("contract_number", rpcContractNumbersNotIn);
            }
            list = list(wrapper);
        }

        // 内存过滤（使用HashSet加速）
        List<CrmContractExecute> filteredList = list.stream()
                .filter(item -> {
                    String num = item.getContractNumber();
                    boolean inCondition = CollectionUtils.isEmpty(rpcContractNumbers) ||
                            rpcContractNumbers.contains(num);
                    boolean notInCondition = CollectionUtils.isEmpty(rpcContractNumbersNotIn) ||
                            !notInSet.contains(num);
                    return inCondition && notInCondition;
                })
                .sorted(Comparator.comparing(
                        CrmContractExecute::getContractTime,
                        Comparator.nullsFirst(Comparator.reverseOrder())
                ))
                .collect(Collectors.toList());

        // 内存分页
        if (StringUtils.isNotNull(query.getPageNum()) && StringUtils.isNotNull(query.getPageSize())) {
            List<CrmContractExecute> pagedList = CommonUtils.subListPage(filteredList,
                    query.getPageSize(), query.getPageNum());
            pageUtils.setTotalCount(filteredList.size());
            pageUtils.setList(HyperBeanUtils.copyListProperties(pagedList, CrmContractExecuteVO::new));
        } else {
            pageUtils.setTotalCount(filteredList.size());
            pageUtils.setList(HyperBeanUtils.copyListProperties(filteredList, CrmContractExecuteVO::new));
        }
        return pageUtils;
    }

    public void initContractTypeName(List<CrmContractExecuteVO> result){
        // 拼接合同类型
        Set<String> contractTypes = result.stream().map(CrmContractExecuteVO::getContractType).collect(Collectors.toSet());
        List<ContractTypeVO> contractTypeVOS = contractReviewConfigService.getByContractTypeIds(contractTypes).getObjEntity();
        Map<String, ContractTypeVO> contractTypeVOMap = contractTypeVOS.stream().collect(Collectors.toMap(ContractTypeVO::getId, v -> v, (v1, v2) -> v1));
        result.forEach(item -> {
            ContractTypeVO contractTypeVO = contractTypeVOMap.get(item.getContractType());
            if (contractTypeVO != null) {
                item.setContractTypeName(contractTypeVO.getType());
            }
        });
    }

    @Override
    public Boolean updateContractExecuteByContractNumber(CrmContractExecuteUpdateVO executeVO) {
        CrmAssert.hasText(executeVO.getContractNumber(), "合同编号不能为空");
        String lockKey = "contractExecute:" + executeVO.getContractNumber();
        RLock rLock = redissonClient.getLock(lockKey);
        try {
            // 避免并发修改一个合同执行信息
            rLock.lock();
            CrmContractExecuteVO contractExecuteVO = getByContractNumber(executeVO.getContractNumber());
            if (contractExecuteVO == null) {
                return true;
            }
            log.info("更新合同执行信息:{}", executeVO);
            CrmContractExecute crmContractExecute = HyperBeanUtils.copyProperties(executeVO, CrmContractExecute::new);
            crmContractExecute.setId(contractExecuteVO.getId());
            // 合同原件以及相关协议是否上传
            CrmContractOriginalDocumentService contractOriginalDocumentService = SpringUtil.getBean(CrmContractOriginalDocumentService.class);
            Boolean originalDocumentFlag = contractOriginalDocumentService.listDocumentByContractNo(executeVO.getContractNumber(), null);
            crmContractExecute.setIsExecuteComplete(CrmContractExecuteUtil.calcIsExecuteConfirm(contractExecuteVO, originalDocumentFlag));
            return updateById(crmContractExecute);
        } finally {
            rLock.unlock();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateContractExecuteByContractNumberBatch(List<CrmContractExecuteUpdateVO> executeVO) {
        CrmAssert.notEmpty(executeVO, "合同执行信息不能为空");
        executeVO.forEach(this::updateContractExecuteByContractNumber);
        return true;
    }

    @Override
    public Set<String> getAllContractNumber() {
        return list(new LambdaQueryWrapper<CrmContractExecute>().eq(CrmContractExecute::getDelFlag, false)).stream().map(CrmContractExecute::getContractNumber).collect(Collectors.toSet());
    }

    private void buildWrapper(QueryWrapper<CrmContractExecute> wrapper, CrmContractAfterQuery query) throws Exception {
        // 反射 获取查询条件里面的属性
        Class<CrmContractAfterQuery> queryClass = CrmContractAfterQuery.class;

        // 遍历所有查询条件 构建合同执行的条件
        Field[] fields = queryClass.getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            if (field.get(query) == null) {
                continue;
            }
            // 判断是不是rpc过滤用的合同号
            CrmContractAfterQuery.RpcCondition annotation = field.getAnnotation(CrmContractAfterQuery.RpcCondition.class);
            if (annotation != null) {
                // 跳过rpc过滤用的合同号
                continue;
            }
            // 遍历属性
            Class<?> o = field.getType();
            // 遍历嵌套对象
            Field[] declaredFields = o.getDeclaredFields();
            Map<Integer, Set<Field>> orGroupFields = new HashMap<>();
            for (Field declaredField : declaredFields) {
                declaredField.setAccessible(true);
                // 构建wrapper
                CrmContractAfterQuery.ContractQueryCondition condition = declaredField.getAnnotation(CrmContractAfterQuery.ContractQueryCondition.class);
                if (condition == null) {
                    continue;
                }
                if (condition.orGroup() != 0) {
                    // 如果有or拼接条件 则跳过 添加到map中 之后用and连接or
                    orGroupFields.computeIfAbsent(condition.orGroup(), k -> new HashSet<>()).add(declaredField);
                    continue;
                }
                buildSelect(wrapper, query, field, declaredField, condition);
            }

            // or 拼接 如果没有 or条件 则跳过本次循环
            if (orGroupFields.isEmpty()) {
                continue;
            }

            // 先循环判断是否需要构建and
            boolean allFieldNull = orGroupFields.values().stream().flatMap(Collection::stream).allMatch(orFields -> {
                try {
                    return orFields.get(field.get(query)) == null;
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });
            for (Set<Field> orFields : orGroupFields.values()) {
                if (allFieldNull) {
                    // 都为空 跳过
                    continue;
                }
                wrapper.and(orWrapper -> {
                    for (Field orField : orFields) {
                        orField.setAccessible(true);
                        CrmContractAfterQuery.ContractQueryCondition condition = orField.getAnnotation(CrmContractAfterQuery.ContractQueryCondition.class);
                        try {
                            buildSelect(orWrapper, query, field, orField, condition);
                            orWrapper.or();
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    }
                });
            }
        }
    }

    private void buildSelect(QueryWrapper<CrmContractExecute> wrapper, CrmContractAfterQuery query, Field field, Field orField, CrmContractAfterQuery.ContractQueryCondition condition) throws Exception {
        if (condition.value() != null && condition.typeHandler() != null) {
            Class<? extends FiledConvertHandler> handlerClass = condition.typeHandler();
            FiledConvertHandler handler = CrmContractAfterQuery.HANDLER_MAP.get(handlerClass);
            if (handler == null) {
                handler = handlerClass.getDeclaredConstructor().newInstance();
            }
            if (orField.get(field.get(query)) == null) {
                // 值为空就跳过拼接
                return;
            }
            Object value = orField.get(field.get(query));
            if (value instanceof Collection<?>) {
                // 如果是集合 判断集合是不是空
                if (((Collection<?>) value).isEmpty()) {
                    return;
                }
            }
            handler.convert(wrapper, condition.value(), value);
        }
    }


    @Override
    public CrmContractExecuteVO getByContractId(String contractId) {
        return HyperBeanUtils.copyProperties(getOne(new LambdaQueryWrapper<CrmContractExecute>()
                .eq(CrmContractExecute::getContractId, contractId)
                .eq(CrmContractExecute::getDelFlag, false)
                .last("limit 1")), CrmContractExecuteVO::new);
    }

    @Override
    public ContractExecuteStatisticsVO getExecuteStatisticsBySaleDept(String saleDeptId) {
        ContractExecuteStatisticsVO result = new ContractExecuteStatisticsVO();
        QueryWrapper<CrmContractExecute> wrapper = new QueryWrapper<>();
        wrapper.select("SUM(debt_amount) AS contract_debt_amount, SUM(overdue_amount) AS contract_overdue_amount")
                .eq("contract_owner_dept_id", saleDeptId)
                .eq("del_flag", false);
        List<Map<String, Object>> maps = this.baseMapper.selectMaps(wrapper);
        Map<String, Object> stringObjectMap = maps.get(0);
        if (stringObjectMap != null) {
            result.setContractDebtAmount(stringObjectMap.get("contract_debt_amount") == null ? BigDecimal.ZERO : (BigDecimal) stringObjectMap.get("contract_debt_amount"));
            result.setContractOverdueAmount(stringObjectMap.get("contract_overdue_amount") == null ? BigDecimal.ZERO : (BigDecimal) stringObjectMap.get("contract_overdue_amount"));
        }

        List<CrmContractExecute> crmContractExecutes = this.baseMapper.selectList(new QueryWrapper<CrmContractExecute>().eq("contract_owner_dept_id", saleDeptId)
                .gt("overdue_date", 0)
                .eq("del_flag", false));
        result.setContractExecuteVO(HyperBeanUtils.copyListProperties(crmContractExecutes, com.topsec.crm.flow.api.dto.contractreview.CrmContractExecuteVO::new));
        if (CollectionUtils.isNotEmpty(crmContractExecutes)) {
            result.setOverdueNum(crmContractExecutes.size());
        } else {
            result.setOverdueNum(0);
        }

        List<CrmContractExecute> debts = this.baseMapper.selectList(new QueryWrapper<CrmContractExecute>().eq("contract_owner_dept_id", saleDeptId)
                .gt("debt_amount", 0)
                .eq("del_flag", false));
        result.setDebtExecuteVO(HyperBeanUtils.copyListProperties(debts, com.topsec.crm.flow.api.dto.contractreview.CrmContractExecuteVO::new));
        return result;
    }

    @Override
    @Cacheable(cacheNames = "crm:contract:hasRight",key = "#personId+#contractNumber")
    public Boolean hasRight(String contractNumber, String personId) {
        CrmContractExecuteVO contractExecuteVO = this.getByContractNumber(contractNumber);
        if (contractExecuteVO == null) {
            // 合同未生效 不能调用这个接口判断
            return false;
        }
        Integer projectSource = contractExecuteVO.getProjectSource();
        if (projectSource == null) {
            return false;
        }
        DataScopeParam dataScopeParam = new DataScopeParam();
        if (projectSource == 1) {
            authorizeUtil.setDataScopeParam(personId, "crm_contract_execute_company", dataScopeParam);
        } else if (projectSource == 3) {
            authorizeUtil.setDataScopeParam(personId, "crm_contract_execute_agent", dataScopeParam);
        } else {
            return false;
        }
        // 判断dataScope中的personIds
        Set<String> personIdList = dataScopeParam.getPersonIdList();
        if (personIdList == null) {
            // 有所有人的权限
            return true;
        }
        String contractOwnerId = contractExecuteVO.getContractOwnerId();
        // 不包含合同负责人的id 代表没权限
        return personIdList.contains(contractOwnerId);
    }

    @Override
    public void giveRightToPerson(Set<String> contractNumber, String personId, Integer seconds) {
        // 放redis里面
        if (CollectionUtils.isEmpty(contractNumber)) {
            return;
        }
        contractNumber.forEach(item -> {
            redisTemplate.opsForValue().set("crm:contract:hasRight::" + personId + item, "1", seconds, TimeUnit.SECONDS);
        });
    }

    @Override
    public Set<String> getContractNumberAuthList(Set<String> contractOwnerIds) {
        CrmContractAfterQuery query = new CrmContractAfterQuery();
        CrmContractAfterQuery.BaseQuery base = new CrmContractAfterQuery.BaseQuery();
        base.setContractOwnerIds(new ArrayList<>(contractOwnerIds));
        query.setBaseQuery(base);
        PageUtils<CrmContractExecuteVO> response = pageByCondition(query);
        if (response != null && response.getList() != null) {
            return response.getList().stream()
                    .map(CrmContractExecuteVO::getContractNumber)
                    .collect(Collectors.toSet());
        }
        return null;
    }

    @Override
    public Boolean deleteContractExecute(String contractNumber) {
        return update(new LambdaUpdateWrapper<CrmContractExecute>()
                .eq(CrmContractExecute::getContractNumber, contractNumber)
                .set(CrmContractExecute::getDelFlag, 1));
    }

    @Override
    public StatsBusinessDeptVO getStatsBusinessByDept(StatsDeptTimeSearchVO query) {
        CrmContractAfterQuery contractQuery = CrmContractAfterQuery.buildBaseQuery();
        CrmContractAfterQuery.BaseQuery bq = contractQuery.getBaseQuery();
        bq.setContractOwnerDeptIds(query.getDeptIds());
        bq.setContractTimeStart(query.getStart());
        bq.setContractTimeEnd(query.getEnd());
        PageUtils<CrmContractExecuteVO> result = this.pageByCondition(contractQuery);
        List<CrmContractExecuteVO> executeVOS = result.getList();
        Set<String> contractNumbers = executeVOS.stream().map(CrmContractExecuteVO::getContractNumber).collect(Collectors.toSet());
        // 新增合同额
        BigDecimal newContractAmount = BigDecimal.ZERO;
        // 新增毛利
        BigDecimal grossMargin = BigDecimal.ZERO;
        // 回款
        BigDecimal repaymentAmount = BigDecimal.ZERO;
        // 超期应收
        BigDecimal overdueReceivable = BigDecimal.ZERO;
        CrmContractReviewService reviewService = SpringUtil.getBean(CrmContractReviewService.class);
        Map<String, CrmProjectDirectlyPriceStatisticsVO> contractPriceStatistics = reviewService.getContractPriceStatistics(contractNumbers);
        for (CrmContractExecuteVO executeVO : executeVOS) {
            newContractAmount = newContractAmount.add(executeVO.getContractAmount());
            CrmProjectDirectlyPriceStatisticsVO priceStatisticsVO = contractPriceStatistics.get(executeVO.getContractNumber());
            grossMargin = grossMargin.add(Optional.ofNullable(priceStatisticsVO)
                    .map(CrmProjectDirectlyPriceStatisticsVO::getTotal)
                    .map(PriceStatisticsVO::getGrossMargin)
                    .orElse(BigDecimal.ZERO));
            repaymentAmount = repaymentAmount.add(executeVO.getReturnedAmount());
            overdueReceivable = overdueReceivable.add(executeVO.getOverdueAmount());
        }
        return StatsBusinessDeptVO.builder()
                .newContract(newContractAmount)
                .grossMargin(grossMargin)
                .paymentAmount(repaymentAmount)
                .overdueReceivable(overdueReceivable)
                .build();
    }

    @Override
    public List<StatsYearCompareDeptVO> getStatsYearCompareByDept(Set<String> deptIds) throws ExecutionException, InterruptedException {
        List<CompletableFuture> futures = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            // 最近五年的
            // 今年 start
            LocalDate start = LocalDate.now().minusYears(i).withDayOfYear(1);
            // 今年 end
            LocalDate end = LocalDate.now().minusYears(i).with(TemporalAdjusters.lastDayOfYear());
            CompletableFuture<StatsBusinessDeptVO> future = CompletableFuture.supplyAsync(() -> {
                StatsDeptTimeSearchVO query = new StatsDeptTimeSearchVO();
                query.setDeptIds(deptIds);
                query.setStart(start);
                query.setEnd(end);
                return this.getStatsBusinessByDept(query);
            });
            futures.add(future);
        }

        CompletableFuture<Void> cf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        cf.join();

        List<StatsYearCompareDeptVO> statsYearCompareDeptVOS = new ArrayList<>();
        // 遍历
        for (int i = 0; i < futures.size(); i++) {
            CompletableFuture<StatsBusinessDeptVO> future = futures.get(i);
            StatsBusinessDeptVO statsBusinessDeptVO = future.get();
            statsYearCompareDeptVOS.add(StatsYearCompareDeptVO.builder()
                    .newContract(statsBusinessDeptVO.getNewContract())
                    .grossMargin(statsBusinessDeptVO.getGrossMargin())
                    .year(LocalDate.now().minusYears(i).getYear() + "")
                    .build()
            );
        }
        // 前三年计算比较上年的
        for (int i = 0; i < 3; i++) {
            StatsYearCompareDeptVO currentStats = statsYearCompareDeptVOS.get(i);
            StatsYearCompareDeptVO lastStats = statsYearCompareDeptVOS.get(i + 1);
            // (今年新增合同 - 去年新增合同) / 去年的新增合同
            if (lastStats.getNewContract().compareTo(BigDecimal.ZERO) == 0) {
                currentStats.setCompareNewContract(BigDecimal.ZERO);
            } else {
                currentStats.setCompareNewContract(currentStats.getNewContract().subtract(lastStats.getNewContract()).divide(lastStats.getNewContract(), 4, RoundingMode.HALF_UP));
            }
            // (今年新增毛利 - 去年新增毛利) / 去年的新增毛利
            if (lastStats.getGrossMargin().compareTo(BigDecimal.ZERO) == 0) {
                currentStats.setCompareGrossMargin(BigDecimal.ZERO);
            } else {
                currentStats.setCompareGrossMargin(currentStats.getGrossMargin().subtract(lastStats.getGrossMargin()).divide(lastStats.getGrossMargin(), 4, RoundingMode.HALF_UP));
            }
        }
        return statsYearCompareDeptVOS;
    }

    @Override
    public StatsProjectVO getStatsProjectByPersonId(StatsPersonTimeSearchVO searchVO) {
        String personId = searchVO.getCurrentPersonId();
        assert personId != null;
        CrmContractAfterQuery contractQuery = CrmContractAfterQuery.buildBaseQuery();
        CrmContractAfterQuery.BaseQuery bq = contractQuery.getBaseQuery();
        bq.setContractOwnerId(personId);
        bq.setContractTimeStart(searchVO.getStart());
        bq.setContractTimeEnd(searchVO.getEnd());
        PageUtils<CrmContractExecuteVO> result = this.pageByCondition(contractQuery);
        List<CrmContractExecuteVO> executeVOS = result.getList();
        Set<String> contractNumbers = executeVOS.stream().map(CrmContractExecuteVO::getContractNumber).collect(Collectors.toSet());
        // 新增合同额
        BigDecimal newContractAmount = BigDecimal.ZERO;
        // 超期应收
        BigDecimal overdueReceivable = BigDecimal.ZERO;
        CrmContractReviewService reviewService = SpringUtil.getBean(CrmContractReviewService.class);
        for (CrmContractExecuteVO executeVO : executeVOS) {
            newContractAmount = newContractAmount.add(executeVO.getContractAmount());
            overdueReceivable = overdueReceivable.add(executeVO.getOverdueAmount());
        }
        return StatsProjectVO.builder()
                .newContract(newContractAmount)
                .receivableAmount(overdueReceivable)
                .build();
    }

    @Override
    public List<StatsAgentSaleVO> getAgentContractTotalAmount(String month) {
        return baseMapper.getAgentContractTotalAmount(month);
    }

    @Override
    public void truncateTable() {
        baseMapper.truncateTable();
    }

    @Override
    public Map<String, BigDecimal> getCompanyNameDebtAmount() {
        List<CrmContractExecute> debts = this.baseMapper.selectList(new QueryWrapper<CrmContractExecute>()
                .gt("debt_amount", 0)
                .eq("del_flag", false));
       return debts.stream().collect(Collectors.groupingBy(CrmContractExecute::getContractCompanyId, Collectors.mapping(CrmContractExecute::getDebtAmount, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
    }

    @Override
    public List<HandoverContractVo> queryByHandoverProcessQuery(HandoverProcessQuery query) {
        PageUtils<CrmContractExecuteVO> pageUtils = buildPageUtils(query, false);
        return convertToHandoverContractVoList(pageUtils.getList());
    }

    @Override
    public PageUtils<HandoverContractVo> pageByHandoverProcessQuery(HandoverProcessQuery query) {
        PageUtils<CrmContractExecuteVO> pageUtils = buildPageUtils(query, true);
        List<HandoverContractVo> list = convertToHandoverContractVoList(pageUtils.getList());

        PageUtils<HandoverContractVo> result = new PageUtils<>();
        result.setList(list);
        result.setTotalCount(pageUtils.getTotalCount());
        return result;
    }

    @Override
    public Boolean updateContractOwner(List<HandoverContractVo> handoverContractVo) {
        if (CollectionUtils.isEmpty(handoverContractVo)) {
            return true;
        }
        Set<String> receiverIds = handoverContractVo.stream().map(HandoverContractVo::getReceiverId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(receiverIds)) {
            return true;
        }
        // 取部门
        List<PersonVO> personVOList = tbsPersonClient.listByIds(receiverIds.toArray(new String[0])).getObjEntity();
        Map<String, DepartmentVO> deptByPersonId = ListUtils.emptyIfNull(personVOList).stream().collect(Collectors.toMap(PersonVO::getUuid, item -> {
            if (CollectionUtils.isEmpty(item.getDepartmentVOList())) {
                return new DepartmentVO();
            }
            return item.getDepartmentVOList().get(0);
        }));
        handoverContractVo.forEach(item -> {
            DepartmentVO departmentVO = deptByPersonId.get(item.getReceiverId());
            item.setDeptId(departmentVO.getUuid());
            item.setDeptName(departmentVO.getDeptName());
        });

        Map<String, HandoverContractVo> handoverContractVoMap = new HashMap<>();
        Map<String, Set<String>> contractMap = handoverContractVo.stream().filter(item -> {
            if (StringUtils.isNotEmpty(item.getReceiverId())) {
                handoverContractVoMap.put(item.getReceiverId(), item);
                return true;
            }
            return false;
        }).collect(Collectors.groupingBy(HandoverContractVo::getReceiverId,
                Collectors.collectingAndThen(Collectors.toList(), vos -> vos.stream().map(HandoverContractVo::getContractNumber).filter(Objects::nonNull).collect(Collectors.toSet()))));

        // 循环
        if (MapUtils.isEmpty(contractMap)){
            return true;
        }
        contractMap.forEach((id, contractNumbers) -> {
            if (CollectionUtils.isEmpty(contractNumbers)) {
                return;
            }
            HandoverContractVo contractVo = handoverContractVoMap.get(id);
            String receiverId = contractVo.getReceiverId();
            String receiverName = contractVo.getReceiverName();
            String deptId = contractVo.getDeptId();
            String deptName = contractVo.getDeptName();
            // update
            this.update(new LambdaUpdateWrapper<CrmContractExecute>()
                    .set(CrmContractExecute::getContractOwnerId, receiverId)
                    .set(CrmContractExecute::getContractOwnerName, receiverName)
                    .set(CrmContractExecute::getContractOwnerDeptId, deptId)
                    .set(CrmContractExecute::getContractOwnerDeptName, deptName)
                    .in(CrmContractExecute::getContractNumber, contractNumbers));
        });

        // 1.合同评审
        remoteContractReviewFlowService.updateContractOwner(handoverContractVo);
        // 2.应收催款
        remoteFlowReceivableService.updateContractOwner(handoverContractVo);
        // 3.催款函
        remoteContractCollectionLetterService.updateContractOwner(handoverContractVo);
        // 4.退换货
        remoteContractReturnExchangeService.updateContractOwner(handoverContractVo);
        // 5.合同原件
        remoteContractOriginalExpeditingService.updateContractExpeditingOwner(handoverContractVo);
        return true;
    }

    // 公共逻辑抽取：构建 PageUtils<CrmContractExecuteVO>
    private PageUtils<CrmContractExecuteVO> buildPageUtils(HandoverProcessQuery query, boolean withPagination) {
        String contractNumber = query.getContractNumber();
        String personId = query.getPersonId();
        CrmAssert.hasText(personId, "人员id不能为空");

        CrmContractAfterQuery afterQuery = CrmContractAfterQuery.buildBaseQuery();
        CrmContractAfterQuery.BaseQuery bq = afterQuery.getBaseQuery();
        bq.setIsExecuteComplete(query.getIsCompleted());
        bq.setContractOwnerId(query.getPersonId());
        bq.setContractNumber(contractNumber);

        if (withPagination) {
            afterQuery.setPageNum(query.getPageNum());
            afterQuery.setPageSize(query.getPageSize());
        }

        return this.pageByCondition(afterQuery);
    }

    // 公共逻辑抽取：转换为 HandoverContractVo 列表
    private List<HandoverContractVo> convertToHandoverContractVoList(List<CrmContractExecuteVO> executeVOS) {
        List<String> contractNumbers = ListUtils.emptyIfNull(executeVOS).stream()
                .map(CrmContractExecuteVO::getContractNumber)
                .toList();

        if (contractNumbers.isEmpty()) {
            return Collections.emptyList();
        }
        CrmContractOriginalDocumentService documentService = SpringUtil.getBean(CrmContractOriginalDocumentService.class);
        Map<String, Boolean> contractOriginalDocuments = documentService.listDocumentByContractNos(contractNumbers, List.of(0));
        Map<String, Boolean> returnExchangeDocuments = documentService.listDocumentByContractNos(contractNumbers, List.of(1));

        return executeVOS.stream().map(executeVO -> {
            HandoverContractVo handoverContractVo = new HandoverContractVo();
            handoverContractVo.setPersonId(executeVO.getContractOwnerId());
            handoverContractVo.setBusinessId(executeVO.getContractNumber());
            handoverContractVo.setContractNumber(executeVO.getContractNumber());
            handoverContractVo.setContractAmount(executeVO.getContractAmount());
            handoverContractVo.setPaymentReceived(executeVO.getReturnedAmount());
            handoverContractVo.setInvoicedAmount(executeVO.getInvoicedAmount());
            handoverContractVo.setRevenueConfirmed(executeVO.getRevenueRecognitionAmount());

            Boolean hasOriginalDoc = contractOriginalDocuments.get(executeVO.getContractNumber());
            handoverContractVo.setOriginalContract(booleanToInt(hasOriginalDoc));

            Boolean hasReturnDoc = returnExchangeDocuments.get(executeVO.getContractNumber());
            handoverContractVo.setReturnAgreement(booleanToInt(hasReturnDoc));

            handoverContractVo.setIsCompleted(booleanToInt(executeVO.getIsExecuteComplete()));

            return handoverContractVo;
        }).toList();
    }

    // 辅助方法：布尔转整数
    private int booleanToInt(Boolean value) {
        if (value == null) {
            return 0;
        }
        return Boolean.TRUE.equals(value) ? 1 : 0;
    }

    @Override
    public List<CrmContractExecuteVO> getByContractCompanyId(String contractCompanyId) {
        List<CrmContractExecute> list = list(new LambdaQueryWrapper<CrmContractExecute>().eq(CrmContractExecute::getContractCompanyId, contractCompanyId)
                .eq(CrmContractExecute::getContractExecuteStatus, 1)
                .and(i -> i.gt(CrmContractExecute::getDebtAmount, 0).or().gt(CrmContractExecute::getOverdueAmount, 0)));
        return HyperBeanUtils.copyListPropertiesByJackson(list, CrmContractExecuteVO.class);
    }
}
