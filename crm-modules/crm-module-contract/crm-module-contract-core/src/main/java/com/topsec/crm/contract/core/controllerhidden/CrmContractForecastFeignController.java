package com.topsec.crm.contract.core.controllerhidden;

import com.topsec.crm.contract.core.service.ICrmContractForecastService;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @version V1.0
 * @Description: 合同预测内部调用接口
 * @ClassName: com.topsec.crm.contract.core.controllerhidden.CrmContractForecastFeignController.java
 * @Copyright 天融信 - Powered By 企业软件研发中心
 * @author: leo
 * @date: 2025-07-22 16:40
 */
@RestController
@RequestMapping("/contractForecastFeign")
@Tag(name = "合同预测-不对外开放", description = "/contractForecastFeign")
public class CrmContractForecastFeignController extends BaseController {

    @Resource
    private ICrmContractForecastService crmContractForecastService;

    @GetMapping("/createContractForecastPlan")
    JsonObject<Boolean> createContractForecastPlan(){
        return new JsonObject<>(crmContractForecastService.createContractForecastPlan());
    }

    @GetMapping("/showContractForecastRank")
    JsonObject<Boolean> showContractForecastRank(){
        return new JsonObject<>(crmContractForecastService.showContractForecastRank());
    }


    @GetMapping("/getCurrentMonth")
    JsonObject<String> getCurrentMonth(){
        return new JsonObject<String>(crmContractForecastService.getCurrentMonth());
    }

    @GetMapping("/getAutoFinishMonth01")
    JsonObject<List<String>> getAutoFinishMonth01(){
        return new JsonObject<List<String>>(crmContractForecastService.getAutoFinishMonth01());
    }

    @GetMapping("/getAutoFinishMonth02")
    JsonObject<List<String>> getAutoFinishMonth02(){
        return new JsonObject<List<String>>(crmContractForecastService.getAutoFinishMonth02());
    }
}
