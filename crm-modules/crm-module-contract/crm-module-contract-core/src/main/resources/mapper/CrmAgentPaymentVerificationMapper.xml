<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.contract.core.mapper.CrmAgentPaymentVerificationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.topsec.crm.contract.core.entity.CrmAgentPaymentVerification">
        <id column="id" property="id" />
        <result column="received_date" property="receivedDate" />
        <result column="collection_amount" property="collectionAmount" />
        <result column="account_number" property="accountNumber" />
        <result column="received_agent_id" property="receivedAgentId" />
        <result column="received_agent_name" property="receivedAgentName" />
        <result column="received_level" property="receivedLevel" />
        <result column="payer_agent_id" property="payerAgentId" />
        <result column="payer_agent_name" property="payerAgentName" />
        <result column="doc_id" property="docId" />
        <result column="remark" property="remark" />
        <result column="source_remark" property="sourceRemark" />
        <result column="performance_report_payment_info_id" property="performanceReportPaymentInfoId" />
        <result column="payment_disburse_process_instance_id" property="paymentDisburseProcessInstanceId" />
        <result column="create_time" property="createTime" />
        <result column="create_user" property="createUser" />
        <result column="update_time" property="updateTime" />
        <result column="update_user" property="updateUser" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, received_date, collection_amount, account_number, received_agent_id, received_agent_name, received_level, payer_agent_id, payer_agent_name, doc_id, remark, source_remark, performance_report_payment_info_id, payment_disburse_process_instance_id, create_time, create_user, update_time, update_user, del_flag
    </sql>
    <select id="selectAdvancePaymentList"
            resultType="com.topsec.crm.contract.api.entity.crmagentpayment.CrmAgentAdvancePaymentVO">
        select received_agent_id, received_agent_name, received_level, payer_agent_id, payer_agent_name
             , sum(case when status=1 then write_off_price else 0 end) as advance_payment_balance
             , sum(case when status=0 then write_off_price else 0 end) as advance_payment_freeze
        from crm_agent_payment_verification_detail a
        left join crm_agent_payment_verification b on a.verification_id = b.id
        <where>
            a.verification_type=1 and a.del_flag=0 and b.del_flag=0
            <if test="receivedAgentId!=null and receivedAgentId!=''">
                and received_agent_id = #{receivedAgentId}
            </if>
            <if test="receivedAgentName!=null and receivedAgentName!=''">
                and received_agent_name like concat('%',#{receivedAgentName},'%')
            </if>
            <if test="receivedLevel!=null and receivedLevel!=''">
                and received_level = #{receivedLevel}
            </if>
            <if test="payerAgentId!=null and payerAgentId!=''">
                and payer_agent_id = #{payerAgentId}
            </if>
            <if test="payerAgentName!=null and payerAgentName!=''">
                and payer_agent_name like concat('%',#{payerAgentName},'%')
            </if>
        </where>
        group by received_agent_id, received_agent_name, received_level, payer_agent_id, payer_agent_name
    </select>

    <select id="selectPages" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crm_agent_payment_verification sarm
        <where>
            sarm.del_flag = 0
            <if test="query.receivedAgentName!=null and query.receivedAgentName!=''">
                and sarm.received_agent_name like  CONCAT('%',#{query.receivedAgentName},'%')
            </if>
            <if test="query.payerAgentName!=null and query.payerAgentName!=''">
                and sarm.payer_agent_name like  CONCAT('%',#{query.payerAgentName},'%')
            </if>
            <if test="query.accountNumber!=null and query.accountNumber!=''">
                and sarm.account_number like  CONCAT('%',#{query.accountNumber},'%')
            </if>
            <if test="query.accountingDateBegin!=null">
                and <![CDATA[sarm.received_date >= DATE_FORMAT(#{query.accountingDateBegin}, '%Y-%m-%d')]]>
            </if>
            <if test="query.accountingDateEnd!=null ">
                and <![CDATA[sarm.received_date <=  DATE_FORMAT(#{query.accountingDateEnd}, '%Y-%m-%d')]]>
            </if>
            <if test="allagents != null and allagents.size() > 0">
                and (sarm.payer_agent_id in
                <foreach collection="allagents" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>

                or sarm.received_agent_id in
                <foreach collection="allagents" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                )
            </if>
            <if test="twoagents != null and twoagents.size() > 0">
                and (sarm.payer_agent_id in
                <foreach collection="twoagents" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                )
            </if>
            <if test="depts != null and depts.size() > 0">
                and (sarm.payer_agent_id  in
                <foreach collection="depts" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                )
            </if>
        </where>
        order by sarm.received_date desc,sarm.update_time desc
    </select>

    <select id="verificationHiddenPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crm_agent_payment_verification sarm
        <where>
            sarm.del_flag = 0
            <if test="query.receivedAgentName!=null and query.receivedAgentName!=''">
                and sarm.received_agent_name =  #{query.receivedAgentName}
            </if>
            <if test="query.payerAgentName!=null and query.payerAgentName!=''">
                and sarm.payer_agent_name =  #{query.payerAgentName}
            </if>
        </where>
        order by sarm.received_date desc,sarm.update_time desc
    </select>

    <select id="selectAdvancesReceivedPages" resultType="com.topsec.crm.contract.api.entity.crmagentpayment.agentpaymentverification.AgentReceivedlVO">
        select
        avd.id,avd.remaining_refundable_amount, avd.verification_id,avd.status, avd.verification_type, avd.performance_report_number, sarm.payer_agent_id, sarm.payer_agent_name, avd.contract_amount, avd.reversal, avd.write_off_date, avd.write_off_price, avd.dept_amount, avd.remark, avd.create_time, avd.create_user, avd.update_time, avd.update_user, avd.del_flag
         ,sarm.received_date,sarm.received_agent_id, sarm.received_agent_name,sarm.received_level,sarm.collection_amount
        from crm_agent_payment_verification sarm,crm_agent_payment_verification_detail avd
        <where>
            sarm.id = avd.verification_id and avd.del_flag = 0 and avd.verification_type = 2
            <if test="query.payerAgentName!=null and query.payerAgentName!=''">
                and sarm.payer_agent_name like  CONCAT('%',#{query.payerAgentName},'%')
            </if>
            <if test="query.receivedAgentName!=null and query.receivedAgentName!=''">
                and sarm.received_agent_name =  #{query.receivedAgentName}
            </if>
            <if test="query.accountingDateBegin!=null">
                and <![CDATA[sarm.received_date >= DATE_FORMAT(#{query.accountingDateBegin}, '%Y-%m-%d')]]>
            </if>
            <if test="query.accountingDateEnd!=null ">
                and <![CDATA[sarm.received_date <=  DATE_FORMAT(#{query.accountingDateEnd}, '%Y-%m-%d')]]>
            </if>
            <if test="allagents != null and allagents.size() > 0">
                and (sarm.payer_agent_id in
                <foreach collection="allagents" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>

                or sarm.received_agent_id in
                <foreach collection="allagents" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                )
            </if>
            <if test="twoagents != null and twoagents.size() > 0">
                and (sarm.payer_agent_id in
                <foreach collection="twoagents" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                )
            </if>
            <if test="depts != null and depts.size() > 0">
                and (sarm.payer_agent_id  in
                <foreach collection="depts" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                )
            </if>
        </where>
        order by sarm.received_date desc,sarm.update_time desc
    </select>

    <select id="selectPrePages" resultType="com.topsec.crm.framework.common.bean.AgentPrePaymentVerificationVO">
        select
        avd.id,avd.verification_id, sarm.payer_agent_id as payerAgentId, sarm.payer_agent_name as payerAgentName, sarm.received_agent_id as receivedAgentId, sarm.received_agent_name as receivedAgentName,  sarm.received_date as receivedDate,
        avd.verification_type as verificationType,avd.write_off_price as remainingPrepaymentAmount,sarm.received_level as receivedLevel,
        avd.status
        from crm_agent_payment_verification sarm,crm_agent_payment_verification_detail avd
        <where>
            sarm.id = avd.verification_id and avd.del_flag = 0 and avd.verification_type = #{verificationType} and avd.write_off_price>0
            <if test="query.payerAgentName!=null and query.payerAgentName!=''">
                and sarm.payer_agent_name like  CONCAT('%',#{query.payerAgentName},'%')
            </if>
            <if test="allagents != null and allagents.size() > 0">
                and (sarm.agreement_manager_person_id in
                <foreach collection="allagents" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                )
            </if>
            <if test="allagents != null and allagents.size() > 0">
                and (sarm.payer_agent_id in
                <foreach collection="allagents" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>

                or sarm.received_agent_id in
                <foreach collection="allagents" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                )
            </if>
            <if test="twoagents != null and twoagents.size() > 0">
                and (sarm.payer_agent_id in
                <foreach collection="twoagents" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                )
            </if>
            <if test="depts != null and depts.size() > 0">
                and (sarm.payer_agent_id  in
                <foreach collection="depts" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                )
            </if>
        </where>
        order by sarm.received_date desc,sarm.update_time desc
    </select>

    <select id="selectAccountNumbers" resultType="string">
        select distinct account_number from crm_agent_payment_verification where del_flag = 0
    </select>

    <select id="selectPrePaymentVerificationList" resultType="com.topsec.crm.framework.common.bean.AgentPrePaymentVerificationVO">
        select
        avd.id,avd.verification_id, sarm.payer_agent_id as payerAgentId, sarm.payer_agent_name as payerAgentName, sarm.received_agent_id as receivedAgentId, sarm.received_agent_name as receivedAgentName,  sarm.received_date as receivedDate,
        avd.verification_type as verificationType,avd.write_off_price as remainingPrepaymentAmount,sarm.received_level as receivedLevel,
        avd.status
        from crm_agent_payment_verification sarm,crm_agent_payment_verification_detail avd
        where sarm.id = avd.verification_id and avd.del_flag = 0 and avd.write_off_price>0 and avd.verification_type=2 and sarm.payer_agent_id=#{payerAgentId} and sarm.received_agent_id=#{receivedAgentId}
    </select>
</mapper>
